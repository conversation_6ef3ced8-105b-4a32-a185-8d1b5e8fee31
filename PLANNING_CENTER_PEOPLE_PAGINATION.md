# Planning Center People Tab with Pagination and Search

This document describes the implementation of pagination and search functionality for the Planning Center People tab in the CSF Portal.

## Overview

The Planning Center People tab now displays all people from Planning Center with server-side pagination and search capabilities. This implementation ensures efficient loading and browsing of large datasets from the Planning Center API.

## Features Implemented

1. **Server-side Pagination**
   - People are loaded in pages (default: 25 per page)
   - Pagination controls allow navigation between pages
   - Total count of people is displayed

2. **Server-side Search**
   - Search input filters people by name
   - Search is performed on the server for better performance
   - Search results are also paginated

3. **UI Enhancements**
   - Pagination controls at the bottom of the table
   - Total count display
   - Improved search experience

## Implementation Details

### Server-side Changes

1. **Controller Updates (`planningCenterController.js`)**
   - The `getPeopleForDirectory` method now handles pagination parameters:
     - `page`: Current page number
     - `per_page`: Number of items per page
   - Search parameter is mapped to Planning Center's API format:
     - Client's `search` parameter is mapped to Planning Center's `where[search_name]`
   - Response includes pagination metadata:
     - `people`: Array of formatted people
     - `pagination`: Pagination information (currentPage, totalPages, totalCount)
     - `meta`: Original metadata from Planning Center API
     - `links`: Navigation links from Planning Center API

2. **API Integration (`planningCenterAPI.js`)**
   - Already supported pagination through query parameters
   - Extracts pagination information from API responses

### Client-side Changes

1. **Service Updates (`peopleService.js`)**
   - Updated `getPlanningCenterPeople` to handle the new response format
   - Returns an object with people array and pagination metadata

2. **Component Updates (`PeoplePage.js`)**
   - Added state for pagination:
     ```javascript
     const [pcPagination, setPcPagination] = useState({
       currentPage: 1,
       totalPages: 1,
       totalCount: 0,
       perPage: 25
     });
     ```
   - Updated fetch function to pass pagination and search parameters
   - Added pagination UI controls
   - Implemented pagination change handler
   - Modified search to work with server-side pagination

## How to Test

1. **Pagination**
   - Navigate to the People Directory page
   - Ensure the Planning Center toggle is enabled
   - Verify that Planning Center people are displayed
   - If there are multiple pages, pagination controls should appear at the bottom
   - Click on page numbers, first/last buttons to navigate between pages
   - Verify that different people are loaded when changing pages

2. **Search**
   - Enter a search term in the search box
   - Verify that the results are filtered to match the search term
   - Verify that pagination works correctly with search results
   - Clear the search and verify that all people are displayed again

## Troubleshooting

If pagination or search is not working as expected:

1. Check browser console for errors
2. Verify that Planning Center API credentials are valid
3. Check network requests to see if pagination parameters are being sent correctly
4. Verify that the Planning Center API is responding with the expected data

## Future Improvements

Potential enhancements for the future:

1. Add more advanced search options (e.g., by email, phone)
2. Implement sorting options
3. Add ability to adjust items per page
4. Improve performance for very large datasets

## References

- [Planning Center People API Documentation](https://developer.planning.center/docs/#/apps/people/2025-07-02/vertices/person)
- [Material-UI Pagination Component](https://mui.com/material-ui/react-pagination/)