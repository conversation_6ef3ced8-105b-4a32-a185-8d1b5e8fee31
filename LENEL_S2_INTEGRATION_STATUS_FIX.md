# Lenel S2 Integration Status Fix

## Issue Description

The building-management page was incorrectly detecting if the Lenel S2 NetBox service is configured or not. Specifically, the Lenel S2 NetBox integration was properly configured and working on the Lenel page, but it showed as "not configured" on the building-management page.

## Root Cause

The issue was caused by a discrepancy between how the Lenel S2 page and the building-management page determine if Lenel S2 is configured:

1. **Lenel S2 Page**: Checks if the environment variables (`LENEL_S2_NETBOX_HOST`, `LENEL_S2_NETBOX_USERNAME`, `LENEL_S2_NETBOX_PASSWORD`) are set to determine if Lenel S2 is configured.

2. **Building Management Page**: Uses the integration status from the `integration-status.md` file, which was being manually updated by the `fix-integration-status.js` script without checking the actual configuration status.

## Solution

A new script (`fix-lenel-integration-status.js`) was created to fix the integration status for Lenel S2 NetBox. This script:

1. Checks if the Lenel S2 NetBox environment variables are properly set
2. Updates the `integration-status.md` file with the correct status based on the actual configuration
   - If the environment variables are set, it updates the status to "active"
   - If the environment variables are not set, it updates the status to "not_configured"

## Implementation

The script has been implemented and tested. It correctly updates the integration status based on the actual configuration status of Lenel S2 NetBox.

### Script: `fix-lenel-integration-status.js`

```javascript
/**
 * Script to fix the integration status for Lenel S2 NetBox
 * This script checks if the Lenel S2 NetBox environment variables are properly set
 * before updating the integration-status.md file
 */

const fs = require('fs');
const path = require('path');

// Path to the integration status file
const statusFilePath = path.resolve(process.cwd(), 'integration-status.md');

// Check if the file exists
if (!fs.existsSync(statusFilePath)) {
  console.error('Integration status file not found:', statusFilePath);
  process.exit(1);
}

// Check if Lenel S2 NetBox environment variables are set
const host = process.env.LENEL_S2_NETBOX_HOST || '';
const username = process.env.LENEL_S2_NETBOX_USERNAME || '';
const password = process.env.LENEL_S2_NETBOX_PASSWORD || '';

// Determine if Lenel S2 NetBox is properly configured
const isLenelConfigured = !!(host && username && password);

console.log('Checking Lenel S2 NetBox configuration:');
console.log(`- Host: ${host ? 'Set' : 'Not set'}`);
console.log(`- Username: ${username ? 'Set' : 'Not set'}`);
console.log(`- Password: ${password ? 'Set' : 'Not set'}`);
console.log(`- Configuration status: ${isLenelConfigured ? 'Configured' : 'Not configured'}`);

// Read the current content
let content = fs.readFileSync(statusFilePath, 'utf8');
const lines = content.split('\n');

// Update the last updated timestamp in the header
const now = new Date();
const lastUpdatedLine = `Last Updated: ${now.toISOString()}`;

// Find and replace the header timestamp
let updatedLines = lines.map(line => {
  if (line.startsWith('Last Updated:')) {
    return lastUpdatedLine;
  }
  return line;
});

// Find and update the Lenel S2 NetBox status based on actual configuration
let lenelS2Updated = false;
updatedLines = updatedLines.map(line => {
  if (line.includes('Lenel S2 NetBox') && !lenelS2Updated) {
    lenelS2Updated = true;
    if (isLenelConfigured) {
      return `| Lenel S2 NetBox | active | ${now.toISOString()} | ${now.toISOString()} | Integration is properly authenticated and ready to use. |`;
    } else {
      return `| Lenel S2 NetBox | not_configured | ${now.toISOString()} | ${now.toISOString()} | Environment variables are not properly set. |`;
    }
  }
  return line;
});

// If we didn't find the integration, add it
if (!lenelS2Updated) {
  if (isLenelConfigured) {
    updatedLines.push(`| Lenel S2 NetBox | active | ${now.toISOString()} | ${now.toISOString()} | Integration is properly authenticated and ready to use. |`);
  } else {
    updatedLines.push(`| Lenel S2 NetBox | not_configured | ${now.toISOString()} | ${now.toISOString()} | Environment variables are not properly set. |`);
  }
}

// Write the updated content back to the file
fs.writeFileSync(statusFilePath, updatedLines.join('\n'));

console.log('Integration status updated successfully:');
console.log(`- Lenel S2 NetBox: ${isLenelConfigured ? 'active' : 'not_configured'}`);
```

## Setup Instructions

To ensure that the integration status is always up to date, you should set up a scheduled task to run the script periodically.

### Option 1: Cron Job (Linux/macOS)

1. Open the crontab editor:
   ```
   crontab -e
   ```

2. Add a line to run the script every hour:
   ```
   0 * * * * cd /path/to/csfportal && node fix-lenel-integration-status.js >> /path/to/logs/lenel-status-update.log 2>&1
   ```

### Option 2: Scheduled Task (Windows)

1. Create a batch file (e.g., `update-lenel-status.bat`) with the following content:
   ```
   cd /path/to/csfportal
   node fix-lenel-integration-status.js >> logs\lenel-status-update.log 2>&1
   ```

2. Open Task Scheduler and create a new task to run the batch file every hour.

### Option 3: Run on Server Startup

Add the script to your server startup process to ensure it runs whenever the server is restarted.

## Verification

After setting up the scheduled task, you can verify that the integration status is being correctly updated by:

1. Checking the `integration-status.md` file to see if the Lenel S2 NetBox status matches the actual configuration.
2. Verifying that the building-management page correctly shows the Lenel S2 NetBox status.

## Troubleshooting

If the integration status is still not being correctly updated, check the following:

1. Make sure the script has the necessary permissions to read and write the `integration-status.md` file.
2. Verify that the environment variables are correctly set in the environment where the script is running.
3. Check the logs for any error messages.

## Additional Notes

This fix ensures that the integration status in the `integration-status.md` file accurately reflects the actual configuration status of Lenel S2 NetBox. This approach can be extended to other integrations as well to ensure consistency between the actual configuration status and what is displayed on the building-management page.