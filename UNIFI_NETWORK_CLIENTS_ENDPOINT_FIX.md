# UniFi Network Clients Endpoint Fix

## Issue Description
The `/api/unifi-network/clients` endpoint was returning a 404 error. This indicated that the API endpoint format being used in the UniFi Network API wrapper was not compatible with the specific UniFi Network Controller version being used.

## Root Cause
The UniFi Network API wrapper was using a single endpoint format (`/proxy/network/integration/v1/sites/${site}/clients`) to fetch clients from the UniFi Network Controller. However, different versions of the UniFi Network Controller may use different endpoint formats. If the specific format being used is not supported by the controller version, it would result in a 404 error.

## Fix Applied
The fix involved implementing a fallback mechanism in the UniFi Network API wrapper to try multiple endpoint formats if the first one fails. This ensures compatibility with different UniFi Network Controller versions.

### Changes to `getClients` Method
Updated the `getClients` method in `unifiNetworkAPI.js` to:

1. Try multiple endpoint formats in sequence:
   - `/proxy/network/integration/v1/sites/${site}/clients`
   - `/proxy/network/api/s/${site}/stat/sta`
   - `/api/s/${site}/stat/sta`
   - `/v2/api/site/${site}/clients`
   - `/sites/${site}/clients`

2. Handle different response data structures based on the endpoint format.

3. Provide detailed error logging if all endpoint formats fail.

### Changes to `getClientDetails` Method
Similarly updated the `getClientDetails` method to:

1. Try multiple endpoint formats in sequence:
   - `/proxy/network/integration/v1/sites/${site}/clients/${clientId}`
   - `/proxy/network/api/s/${site}/stat/user/${clientId}`
   - `/api/s/${site}/stat/user/${clientId}`
   - `/v2/api/site/${site}/clients/${clientId}`
   - `/sites/${site}/clients/${clientId}`

2. If all direct endpoint formats fail, fall back to getting all clients and filtering for the specific client.

3. Handle different response data structures based on the endpoint format.

4. Provide detailed error logging if all approaches fail.

## Verification
A test script (`test-unifi-network-clients-fix.js`) was created to verify the fix. The script tests the updated UniFi Network API wrapper to ensure it can successfully fetch clients from the UniFi Network Controller by trying multiple endpoint formats.

## Additional Notes
- The fix is minimal and focused specifically on resolving the 404 error without introducing any new functionality or changing existing behavior.
- The implementation follows the recommendation in the documentation to include "fallbacks for different UniFi Network Controller versions" to ensure maximum compatibility.
- The fix should work with various UniFi Network Controller versions, including older versions that use the `/api/s/${site}/stat/sta` endpoint format and newer versions that use the `/proxy/network/integration/v1/sites/${site}/clients` format.

## Date Fixed
2025-07-30