#!/usr/bin/env node

/**
 * Diagnose project mismatch and API enablement issues
 */

require('dotenv').config();

async function diagnoseProjectMismatch() {
  console.log('🔍 Diagnosing Project Configuration Issues\n');

  // Extract project information from environment variables
  const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
  const oauthClientId = process.env.GOOGLE_CLIENT_ID;
  const domain = process.env.ALLOWED_DOMAINS;

  console.log('📋 Current Configuration Analysis:');
  console.log('-'.repeat(50));
  console.log(`Service Account Email: ${serviceAccountEmail}`);
  console.log(`OAuth Client ID: ${oauthClientId}`);
  console.log(`Domain: ${domain}`);
  console.log();

  // Extract project IDs
  let serviceAccountProjectId = null;
  let oauthProjectId = null;

  if (serviceAccountEmail) {
    const match = serviceAccountEmail.match(/@(.+)\.iam\.gserviceaccount\.com$/);
    if (match) {
      serviceAccountProjectId = match[1];
    }
  }

  if (oauthClientId) {
    const parts = oauthClientId.split('-');
    if (parts.length > 0) {
      oauthProjectId = parts[0];
    }
  }

  console.log('🏗️  Project ID Analysis:');
  console.log('-'.repeat(50));
  console.log(`Service Account Project: ${serviceAccountProjectId || 'Unable to extract'}`);
  console.log(`OAuth Client Project: ${oauthProjectId || 'Unable to extract'}`);
  
  if (serviceAccountProjectId && oauthProjectId) {
    if (serviceAccountProjectId === oauthProjectId) {
      console.log('✅ Projects match - configuration is consistent');
    } else {
      console.log('⚠️  PROJECT MISMATCH DETECTED!');
      console.log('   This could cause authentication issues.');
      console.log(`   Service account is in project: ${serviceAccountProjectId}`);
      console.log(`   OAuth client is in project: ${oauthProjectId}`);
    }
  }
  console.log();

  console.log('🔧 Required Actions Based on Analysis:');
  console.log('-'.repeat(50));

  if (serviceAccountProjectId) {
    console.log(`1. ENABLE APIS IN SERVICE ACCOUNT PROJECT (${serviceAccountProjectId}):`);
    console.log(`   Go to: https://console.cloud.google.com/apis/library?project=${serviceAccountProjectId}`);
    console.log('   Enable these APIs:');
    console.log('   • Google Drive API');
    console.log('   • Admin SDK API'); 
    console.log('   • Google Calendar API');
    console.log('   • Google Forms API');
    console.log('   • Identity and Access Management (IAM) API');
    console.log('   • Service Usage API');
    console.log();

    console.log('2. GET SERVICE ACCOUNT CLIENT ID:');
    console.log(`   Go to: https://console.cloud.google.com/iam-admin/serviceaccounts?project=${serviceAccountProjectId}`);
    console.log(`   Find: ${serviceAccountEmail}`);
    console.log('   Click on the service account name');
    console.log('   Copy the "Unique ID" (21-digit number)');
    console.log();

    console.log('3. CONFIGURE DOMAIN-WIDE DELEGATION:');
    console.log('   Go to: https://admin.google.com/');
    console.log('   Security → Access and data control → API controls');
    console.log('   Manage Domain Wide Delegation → Add new');
    console.log('   Enter the Client ID from step 2');
    console.log('   Add these scopes:');

    const scopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.file',
      'https://www.googleapis.com/auth/drive.metadata',
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.group',
      'https://www.googleapis.com/auth/admin.directory.user.security',
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/calendar.events',
      'https://www.googleapis.com/auth/calendar.settings.readonly',
      'https://www.googleapis.com/auth/forms.body',
      'https://www.googleapis.com/auth/forms.responses.readonly'
    ];

    scopes.forEach((scope, i) => {
      console.log(`   ${(i + 1).toString().padStart(2, ' ')}. ${scope}`);
    });
    
    console.log('\n   Or copy this comma-separated list:');
    console.log(`   ${scopes.join(',')}`);
    console.log();
  }

  console.log('4. VERIFY USER EXISTS:');
  console.log('   Go to: https://admin.google.com/');
  console.log('   Directory → Users');
  console.log(`   Search for: bpritchett@${domain}`);
  console.log('   Verify the user exists and is active');
  console.log();

  console.log('5. TEST CONFIGURATION:');
  console.log('   After completing steps 1-4, test with:');
  console.log('   node debug-domain-wide-delegation.js');
  console.log();

  console.log('🎯 Most Likely Issues:');
  console.log('-'.repeat(50));
  console.log('1. Required APIs not enabled in the service account project');
  console.log('2. Wrong Client ID used in domain-wide delegation');
  console.log('3. User email doesn\'t exist in Google Workspace');
  console.log('4. Scopes don\'t match exactly in delegation configuration');
  console.log();

  console.log('📞 Quick Check Commands:');
  console.log('-'.repeat(50));
  if (serviceAccountProjectId) {
    console.log(`Check API status: https://console.cloud.google.com/apis/dashboard?project=${serviceAccountProjectId}`);
    console.log(`Service accounts: https://console.cloud.google.com/iam-admin/serviceaccounts?project=${serviceAccountProjectId}`);
  }
  console.log(`Domain delegation: https://admin.google.com/ac/owl/domainwidedelegation`);
  console.log(`Users directory: https://admin.google.com/ac/users`);
}

diagnoseProjectMismatch();