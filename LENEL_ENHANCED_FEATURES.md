# Lenel S2 NetBox Enhanced Features Documentation

## Overview
This document describes the enhanced features added to the Lenel S2 NetBox API integration, including advanced search capabilities, detailed views, comprehensive CRUD operations, and utility functions.

## Enhanced Features Summary

### 🔍 **Advanced Search and Filtering**
- **searchCardholders()** - Advanced cardholder search with multiple filters
- **searchEvents()** - Event search with date ranges and severity filtering
- **searchPortals()** - Portal/door search with type and location filters
- **searchAlarms()** - Alarm search with status and priority filters

### 📋 **Enhanced Detailed Views**
- **getCardholderDetailsEnhanced()** - Comprehensive cardholder information
- **getPortalDetailsEnhanced()** - Portal details with status and activity
- **getAccessLevelDetailsEnhanced()** - Access level with associated users

### 🛠️ **CRUD Operations**
- **Access Levels**: Create, Read, Update, Delete
- **Time Specifications**: Create, Read, Update, Delete
- **Bulk Operations**: Assign/remove access levels for multiple users

### 🔧 **Utility Functions**
- **System Statistics** - Overview of system entities and status
- **Credential Validation** - Check expiration and status
- **Bulk Operations** - Mass updates for efficiency

---

## Detailed API Reference

### Advanced Search Methods

#### `searchCardholders(filters)`
Advanced search for cardholders with comprehensive filtering options.

**Parameters:**
```javascript
{
  firstName: string,           // First name filter
  lastName: string,            // Last name filter
  email: string,              // Email filter
  status: 'Active'|'Inactive', // Status filter
  accessLevels: Array,        // Access level names to filter by
  activationDateFrom: string, // Activation date from (YYYY-MM-DD)
  activationDateTo: string,   // Activation date to (YYYY-MM-DD)
  expirationDateFrom: string, // Expiration date from (YYYY-MM-DD)
  expirationDateTo: string,   // Expiration date to (YYYY-MM-DD)
  limit: number,              // Maximum results (default: 50)
  offset: number              // Pagination offset (default: 0)
}
```

**Returns:**
```javascript
{
  results: Array,           // Array of cardholder objects
  pagination: {
    total: number,          // Total matching records
    offset: number,         // Current offset
    limit: number,          // Current limit
    hasMore: boolean        // More results available
  },
  filters: Object          // Applied filters
}
```

**Example:**
```javascript
const results = await api.searchCardholders({
  status: 'Active',
  accessLevels: ['Employee', 'Contractor'],
  limit: 25,
  offset: 0
});
```

#### `searchEvents(filters)`
Search events with date ranges and filtering.

**Parameters:**
```javascript
{
  eventName: string,        // Event name filter
  startDate: string,        // Start date (YYYY-MM-DD HH:MM:SS)
  endDate: string,          // End date (YYYY-MM-DD HH:MM:SS)
  severity: string,         // Severity filter (high/medium/low)
  personId: string,         // Person ID filter
  portalName: string,       // Portal name filter
  limit: number,            // Maximum results (default: 100)
  offset: number            // Pagination offset (default: 0)
}
```

#### `searchPortals(filters)`
Search portals/doors with type and location filtering.

**Parameters:**
```javascript
{
  name: string,             // Portal name filter
  type: 'Portal'|'Reader',  // Type filter
  location: string,         // Location filter
  status: string,           // Status filter
  limit: number,            // Maximum results (default: 50)
  offset: number            // Pagination offset (default: 0)
}
```

#### `searchAlarms(filters)`
Search alarms with status and priority filtering.

**Parameters:**
```javascript
{
  eventName: string,        // Event name filter
  priority: string,         // Priority filter
  active: boolean,          // Active status filter
  ackPending: boolean,      // Acknowledgment pending filter
  ownerId: string,          // Owner ID filter
  limit: number,            // Maximum results (default: 50)
  offset: number            // Pagination offset (default: 0)
}
```

---

### Enhanced Detailed View Methods

#### `getCardholderDetailsEnhanced(cardholderId)`
Get comprehensive cardholder information including related data.

**Returns:**
```javascript
{
  // All standard cardholder fields plus:
  accessLevels: Array,      // Enhanced access level objects with descriptions
  credentials: Array,       // Enhanced credential objects with status
  recentActivity: Array,    // Last 10 access activities
  statistics: {
    totalAccessLevels: number,
    totalCredentials: number,
    activeCredentials: number,
    recentActivityCount: number
  },
  lastUpdated: string
}
```

#### `getPortalDetailsEnhanced(portalId)`
Get comprehensive portal information with current status and activity.

**Returns:**
```javascript
{
  // All standard portal fields plus:
  currentStatus: Object,    // Current door status (locked/unlocked/online)
  recentEvents: Array,      // Recent events for this portal
  relatedAccessLevels: Array, // Access levels that can access this portal
  statistics: {
    recentEventCount: number,
    lastActivity: string
  },
  lastUpdated: string
}
```

#### `getAccessLevelDetailsEnhanced(accessLevelId)`
Get comprehensive access level information with associated users.

**Returns:**
```javascript
{
  // All standard access level fields plus:
  associatedCardholders: Array, // Cardholders with this access level
  availablePortals: Array,      // Portals this access level can access
  statistics: {
    totalCardholders: number,
    activeCardholders: number,
    totalPortals: number
  },
  lastUpdated: string
}
```

---

### CRUD Operations

#### Access Level Management

**Create Access Level:**
```javascript
const result = await api.createAccessLevel({
  name: 'New Access Level',
  description: 'Description of the access level',
  portalGroups: ['group1', 'group2'],
  timeSpecGroups: ['timespec1', 'timespec2']
});
```

**Update Access Level:**
```javascript
const result = await api.updateAccessLevel('accessLevelKey', {
  name: 'Updated Name',
  description: 'Updated description'
});
```

**Delete Access Level:**
```javascript
const result = await api.deleteAccessLevel('accessLevelKey');
```

#### Time Specification Management

**Create Time Spec:**
```javascript
const result = await api.createTimeSpec({
  name: 'Business Hours',
  description: 'Standard business hours',
  startTime: '09:00',
  endTime: '17:00',
  daysOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
});
```

**Update Time Spec:**
```javascript
const result = await api.updateTimeSpec('timeSpecKey', {
  endTime: '18:00',
  description: 'Extended business hours'
});
```

**Delete Time Spec:**
```javascript
const result = await api.deleteTimeSpec('timeSpecKey');
```

---

### Bulk Operations

#### `bulkAssignAccessLevels(cardholderIds, accessLevelIds)`
Assign access levels to multiple cardholders at once.

**Parameters:**
- `cardholderIds`: Array of cardholder IDs
- `accessLevelIds`: Array of access level IDs to assign

**Returns:**
```javascript
{
  success: boolean,
  results: {
    successful: Array,      // Successfully processed cardholders
    failed: Array,          // Failed operations with error messages
    total: number           // Total operations attempted
  },
  message: string
}
```

#### `bulkRemoveAccessLevels(cardholderIds, accessLevelIds)`
Remove access levels from multiple cardholders at once.

---

### Utility Functions

#### `getSystemStatistics()`
Get comprehensive system statistics and overview.

**Returns:**
```javascript
{
  overview: {
    totalPortals: number,
    totalCardholders: number,
    totalAccessLevels: number,
    totalAlarms: number
  },
  cardholders: {
    total: number,
    active: number,
    inactive: number
  },
  alarms: {
    total: number,
    active: number,
    acknowledged: number
  },
  lastUpdated: string
}
```

#### Credential Utility Methods

**`getCredentialStatusDescription(credential)`** - Get human-readable status
**`isCredentialExpired(credential)`** - Check if credential is expired
**`getDaysUntilExpiration(credential)`** - Get days until expiration

---

## Usage Examples

### Complete Workflow Example
```javascript
// Initialize API
const api = new LenelS2NetBoxAPI(host, username, password);
await api.initialize();

// Search for active employees
const employees = await api.searchCardholders({
  status: 'Active',
  accessLevels: ['Employee'],
  limit: 50
});

// Get detailed information for first employee
if (employees.results.length > 0) {
  const employeeDetails = await api.getCardholderDetailsEnhanced(
    employees.results[0].id
  );
  console.log('Employee Details:', employeeDetails);
}

// Create a new access level
const newAccessLevel = await api.createAccessLevel({
  name: 'Temporary Contractor',
  description: 'Limited access for contractors'
});

// Assign to multiple users
const bulkResult = await api.bulkAssignAccessLevels(
  ['user1', 'user2', 'user3'],
  [newAccessLevel.accessLevelKey]
);

// Get system overview
const stats = await api.getSystemStatistics();
console.log('System Statistics:', stats);
```

## Testing

Run the enhanced features test:
```bash
node test_lenel_enhanced_features.js
```

This will test all new functionality including search, detailed views, CRUD operations, and utility functions.
