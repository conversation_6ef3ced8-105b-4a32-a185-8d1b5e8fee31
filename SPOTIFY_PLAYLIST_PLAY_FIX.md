# Spotify Playlist Play Fix

## Issue Description

The endpoint `https://portal.ukcsf.org/api/wiim/spotify/playlists/3dMgjy0e6R0SEJe8AZP3IE/play` was returning an error 'unknown command' when attempting to play a Spotify playlist.

## Root Cause

After investigating the codebase, I found that the `playSpotifyPlaylist` function in the WiiM API wrapper (`server/integrations/wiim/wiimAPI.js`) was using an incorrect command format when communicating with the WiiM device.

The function was using the command:
```
playSpotify:playlist:${playlistId}
```

However, this command format was inconsistent with the pattern used by other Spotify play functions in the same file:

- `playSpotifyTrack:${trackId}` - For playing tracks
- `playSpotifyAlbum:${albumId}` - For playing albums
- `playSpotifyArtist:${artistId}` - For playing artists

The WiiM device was returning 'unknown command' because it didn't recognize the `playSpotify:playlist:${playlistId}` command format.

## Solution

I updated the `playSpotifyPlaylist` function to use a command format that follows the same pattern as the other Spotify play functions:

```
playSpotifyPlaylist:${playlistId}
```

This change ensures consistency across all Spotify play commands and should resolve the 'unknown command' error.

## Changes Made

The following change was made to the `playSpotifyPlaylist` function in `/server/integrations/wiim/wiimAPI.js`:

```javascript
// Before:
const response = await this.axios.get(`/httpapi.asp?command=playSpotify:playlist:${playlistId}`);

// After:
const response = await this.axios.get(`/httpapi.asp?command=playSpotifyPlaylist:${playlistId}`);
```

No changes were needed to the client-side code as it was already correctly calling the API endpoint.

## Testing

To verify that this fix resolves the issue, you can:

1. Navigate to the WiiM page in the portal
2. Select the Spotify tab
3. Find a playlist and click the play button
4. Confirm that the playlist starts playing without any 'unknown command' errors

## Additional Notes

This fix maintains the existing authentication flow for Spotify, which uses environment variables for client credentials and refresh tokens. The only change is to the command format sent to the WiiM device.