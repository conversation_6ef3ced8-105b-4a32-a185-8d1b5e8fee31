# Form Duplication Feature Implementation

This document describes the implementation of the form duplication feature in the CSF Portal application.

## Overview

The form duplication feature allows users to create a copy of an existing form, which can then be modified as needed. This is useful for creating similar forms without having to start from scratch, saving time and effort.

## Implementation Details

### Backend Changes

1. **Form Controller**
   - Added a new `duplicateForm` method in `server/controllers/formController.js`
   - The method retrieves the original form, creates a copy with a new title and slug, resets statistics, and saves it as a new form
   - The new form is always created in draft status, regardless of the original form's status

2. **API Route**
   - Added a new route in `server/routes/api/forms.js`: `POST /api/forms/:id/duplicate`
   - The route requires authentication and the 'forms_create' permission
   - The route calls the `duplicateForm` controller method

### Frontend Changes

1. **Form Service**
   - Added a new `duplicateForm` method in `client/src/services/formService.js`
   - The method makes a POST request to the `/api/forms/:id/duplicate` endpoint

2. **Form Builder Page**
   - Added a "Duplicate" button to the form builder interface in `client/src/pages/Forms/FormBuilderPage.js`
   - The button appears only when editing an existing form (when the form has an ID)
   - Added a `duplicateForm` function that calls the form service method and navigates to the new form
   - Added appropriate error handling and user feedback

## Usage

To duplicate a form:

1. Open an existing form in the form builder
2. Click the "Duplicate" button in the top-right corner
3. A copy of the form will be created with "Copy of" prepended to the title
4. You will be automatically redirected to the new form in edit mode
5. The new form will be in draft status, even if the original was published

## Limitations

- The form must be saved before it can be duplicated
- Form submissions are not duplicated, only the form definition
- The duplicated form will have the same field IDs as the original, which means conditional logic will still work correctly

## Future Enhancements

Potential future enhancements for the form duplication feature:

- Option to duplicate form submissions
- Option to rename the form during duplication
- Ability to duplicate specific sections or field groups rather than the entire form
- Form templates system that builds upon this duplication functionality