  /**
   * Get portal details
   * @param {string} portalId Portal ID
   * @returns {Promise<Object>} Portal details
   */
  async getPortalDetails(portalId) {
    try {
      if (!this.sessionId) {
        await this.authenticate();
      }

      // Extract the portal key from the ID (format: portal_KEY or reader_KEY)
      const idParts = portalId.split('_');
      const type = idParts[0];
      const key = idParts[1];

      if (!key) {
        throw new Error(`Invalid portal ID format: ${portalId}`);
      }

      // Use XML API call for portal details, similar to getPortals method
      const xmlBody = `<NETBOX-API sessionid="${this.sessionId}">
<COMMAND name="GetPortalDetails" num="1">
<PARAMS>
<PORTALKEY>${key}</PORTALKEY>
</PARAMS>