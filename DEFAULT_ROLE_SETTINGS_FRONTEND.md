# Default Role Settings Frontend Configuration

This document describes the new feature that allows administrators to configure default roles for new users through the frontend interface instead of using environment variables.

## Overview

Previously, default roles for new users were configured using environment variables:
- `DEFAULT_ROLE_LOCAL_USERS`: Default role for new local users
- `DEFAULT_ROLE_GOOGLE_USERS`: Default role for new Google users
- `GOOGLE_GROUPS_ROLE_MAPPING`: JSON mapping of Google Groups to roles

With this update, these settings can now be configured through the frontend in the Role Management page, making it easier to manage without requiring server restarts or environment variable changes.

## Features

1. **Default Role for Local Users**: Set the default role assigned to new local users created through the admin interface.
2. **Default Role for Google Users**: Set the default role assigned to new Google users when they first sign in.
3. **Google Groups Role Mapping**: Map Google Groups to specific roles, so users who are members of those groups will automatically be assigned the corresponding roles.

## How to Access

1. Log in as an administrator
2. Navigate to the Role Management page
3. Click on the "Default Role Settings" tab

## How It Works

### Default Roles

When a new user is created, the system will:
1. For local users: Assign the default role specified in the settings (unless roles are explicitly provided)
2. For Google users: Assign the default role specified in the settings

### Google Groups Role Mapping

When a new Google user signs in, the system will:
1. Assign the default Google user role
2. Check if the user is a member of any Google Groups configured in the mapping
3. For each matching group, add the corresponding role to the user

If a user is a member of multiple groups, they will receive all the corresponding roles.

## Technical Implementation

The implementation includes:

1. **Database Schema**: A new `RoleSettings` model stores the configuration
2. **API Endpoints**: 
   - `GET /api/roles/settings`: Retrieves current settings
   - `PUT /api/roles/settings`: Updates settings
3. **Frontend Component**: A new tab in the Role Management page for configuring settings
4. **User Creation Logic**: Updated to use database settings instead of environment variables

## Environment Variables (Legacy Support)

The environment variables are still supported for backward compatibility but are now considered legacy. The database settings take precedence over environment variables.

If no settings are found in the database, the system will fall back to using environment variables, and if those are not set, it will use 'user' as the default role.

## Testing

Two test scripts are provided to verify the functionality:

1. `test-frontend-default-roles.js`: Tests the frontend API for managing settings
2. `test-user-creation-with-settings.js`: Tests that user creation uses the database settings correctly

## Troubleshooting

If roles are not being assigned as expected:

1. Check that the roles specified in the settings actually exist in the system
2. Verify that the Google Groups email addresses are correct and match exactly with the groups in Google Workspace
3. Ensure that the Google Admin API is properly configured and has permission to access group membership information

## Security Considerations

Only administrators can view and modify these settings. The settings are validated before being saved to ensure that:

1. The specified roles exist in the system
2. The Google Groups mapping is properly formatted
3. Required fields are provided

## Future Enhancements

Potential future enhancements include:

1. More granular control over default permissions
2. Role assignment based on email domains
3. Automatic role expiration or review periods
4. Audit logging for role assignment changes