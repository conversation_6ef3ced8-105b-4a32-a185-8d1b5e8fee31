# Integration Provisioning Configuration

This document describes the implementation of user provisioning configuration features in the CSF Portal.

## Overview

The CSF Portal now supports configuring which integrations support user provisioning and whether provisioning is enabled for each integration. This allows administrators to control which integrations users can provision accounts for.

## Implementation Details

### Backend Changes

1. **IntegrationSettings Model**
   - Added new fields to the `IntegrationSettings` model:
     - `supportsProvisioning`: <PERSON><PERSON><PERSON> indicating whether the integration supports user provisioning
     - `provisioningEnabled`: <PERSON><PERSON><PERSON> indicating whether provisioning is enabled for the integration
     - `provisioningConfig`: Object for storing additional provisioning configuration

2. **API Endpoints**
   - Updated the `PUT /api/integrations/settings/:id` endpoint to handle the new provisioning fields
   - The endpoint now accepts and stores the provisioning configuration for each integration

### Frontend Changes

1. **Admin Integrations Page**
   - Created a new `AdminIntegrationsPage.js` component for managing integration settings
   - The page displays a table of all integrations with their current settings
   - Administrators can configure basic settings (Required, Read Only, Global Config)
   - Administrators can configure provisioning settings (Supports Provisioning, Enable Provisioning)
   - Advanced provisioning configuration options are available for integrations that support provisioning

2. **User Integrations Page**
   - Updated the `supportsProvisioning` function to use the backend settings instead of hardcoding which integrations support provisioning
   - The UI now dynamically reflects the provisioning settings configured by administrators

3. **Routing and Navigation**
   - Added a route for the new AdminIntegrationsPage in App.js
   - Added a link to the admin navigation menu in Layout.js

## Usage

### For Administrators

1. Navigate to the Admin > Manage Integrations page
2. For each integration, configure whether it supports provisioning and whether provisioning is enabled
3. For integrations that support provisioning, configure advanced settings such as default roles and auto-provisioning

### For Users

1. Navigate to the My Integrations page
2. For integrations that support provisioning and have provisioning enabled, provisioning buttons will be displayed
3. Users can provision and deprovision accounts for these integrations

## Supported Integrations

Currently, the following integrations have built-in support for user provisioning:

- Lenel S2 NetBox
- Google Admin

Administrators can configure additional integrations to support provisioning as needed.

## Future Enhancements

Potential future enhancements to the provisioning system include:

1. Bulk provisioning/deprovisioning of user accounts
2. Scheduled provisioning/deprovisioning
3. Provisioning templates for different user types
4. Audit logging for provisioning actions