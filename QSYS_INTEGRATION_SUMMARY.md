# Q-sys Core Manager Integration

This document provides an overview of the Q-sys Core Manager integration for the CSF Portal.

## Overview

The Q-sys Core Manager integration allows you to control and monitor Q-sys audio-visual systems through the CSF Portal. It provides a RESTful API for interacting with Q-sys Core Manager devices, allowing you to:

- Get device status and health information
- List components and controls
- Get and set control values
- Configure the integration

## Configuration

The Q-sys integration is configured using environment variables. Add the following variables to your `.env` file:

```
# Q-sys Core Manager configuration
QSYS_HOST=your_qsys_host
QSYS_PORT=1710
QSYS_USERNAME=your_qsys_username
QSYS_PASSWORD=your_qsys_password
QSYS_PROTOCOL=tcp
```

### Environment Variables

- `QSYS_HOST`: The hostname or IP address of the Q-sys Core Manager (required)
- `QSYS_PORT`: The port number (optional, defaults to 1710 for TCP, 443 for HTTPS)
- `QSYS_USERNAME`: The username for authentication (optional)
- `QSYS_PASSWORD`: The password for authentication (optional)
- `QSYS_PROTOCOL`: The protocol to use (tcp or https, defaults to tcp)

## Testing the Integration

A test script is provided to verify that the integration is working correctly. Run the script with:

```
node test-qsys-integration.js
```

The script will:
1. Connect to the Q-sys Core Manager using the configured environment variables
2. Get the device status
3. List components and controls
4. Get control values
5. Check the health status

If the script runs successfully, the integration is working correctly.

## API Endpoints

The following API endpoints are available for the Q-sys integration:

### Status and Health

- `GET /api/qsys/status`: Get the status of the Q-sys Core Manager
- `GET /api/qsys/health`: Get the health status of the Q-sys Core Manager

### Components and Controls

- `GET /api/qsys/components`: Get a list of components in the Q-sys Core Manager
- `GET /api/qsys/components/:componentName/controls`: Get controls for a component
- `GET /api/qsys/components/:componentName/controls/:controlName`: Get the value of a control
- `POST /api/qsys/components/:componentName/controls/:controlName`: Set the value of a control

### Configuration

- `GET /api/qsys/config`: Get the current Q-sys configuration
- `POST /api/qsys/config`: Save Q-sys configuration (disabled, use environment variables)
- `POST /api/qsys/one-click-setup`: Set up Q-sys with one click (disabled, use environment variables)

## Usage Examples

### Getting Status

```javascript
fetch('/api/qsys/status', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  },
  credentials: 'include'
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
```

### Setting a Control Value

```javascript
fetch('/api/qsys/components/MyComponent/controls/MyControl', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  credentials: 'include',
  body: JSON.stringify({ value: 50 })
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
```

## Troubleshooting

If you encounter issues with the Q-sys integration:

1. Check that the environment variables are set correctly
2. Verify that the Q-sys Core Manager is accessible from the server
3. Check the server logs for error messages
4. Run the test script to verify the integration

## Technical Details

The integration uses the Q-sys JSON-RPC API to communicate with the Q-sys Core Manager. It supports both TCP and HTTPS protocols, with TCP being the default.

The integration includes:

- `qsysAPI.js`: The API implementation
- `qsysController.js`: The controller for handling API requests
- `qsys.js`: The routes for the API endpoints

The API implementation handles authentication, connection management, and error handling, providing a reliable interface to the Q-sys Core Manager.