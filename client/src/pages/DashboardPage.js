import React, { useState, useCallback, useRef } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Button,
  Divider,
  Paper,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  TextField
} from '@mui/material';
import { 
  Add as AddIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useDashboard } from '../context/DashboardContext';
import { renderWidget, widgetTypes } from '../components/widgets/WidgetRegistry';
import WidgetSettingsForm from '../components/widgets/WidgetSettingsForm';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { debounce } from '../utils/performance';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

// Create a responsive grid layout
const ResponsiveGridLayout = WidthProvider(Responsive);

const DashboardPage = () => {
  const { user } = useAuth();
  const { 
    preferences, 
    unsavedPreferences,
    loading, 
    error, 
    isEditMode, 
    hasUnsavedChanges,
    toggleEditMode, 
    addWidget, 
    updateWidget, 
    removeWidget, 
    resetDashboard, 
    updateLayout: originalUpdateLayout,
    saveDashboardChanges,
    discardChanges
  } = useDashboard();

  // Track if a resize operation is in progress
  const [isResizing, setIsResizing] = useState(false);

  // Create a debounced function to reset the resizing flag
  const debouncedResetResizing = useCallback(
    debounce(() => {
      setIsResizing(false);
    }, 500),
    []
  );

  // State for add widget dialog
  const [addWidgetOpen, setAddWidgetOpen] = useState(false);

  // State for widget settings dialog
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [currentWidget, setCurrentWidget] = useState(null);

  // State for dashboard menu
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const menuOpen = Boolean(menuAnchorEl);

  // Handle opening the add widget dialog
  const handleAddWidgetOpen = () => {
    setAddWidgetOpen(true);
  };

  // Handle closing the add widget dialog
  const handleAddWidgetClose = () => {
    setAddWidgetOpen(false);
  };

  // Handle opening the settings dialog
  const handleSettingsOpen = (widget) => {
    setCurrentWidget(widget);
    setWidgetTitle(widget.title || '');
    setWidgetFormSettings(widget.settings || {});
    setSettingsOpen(true);
  };

  // Handle closing the settings dialog
  const handleSettingsClose = () => {
    setSettingsOpen(false);
    setCurrentWidget(null);
    setWidgetTitle('');
    setWidgetFormSettings({});
  };

  // Handle opening the dashboard menu
  const handleMenuOpen = (event) => {
    setMenuAnchorEl(event.currentTarget);
  };

  // Handle closing the dashboard menu
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  // Handle adding a new widget
  const handleAddWidget = (type) => {
    const widgetType = widgetTypes.find(w => w.type === type);
    if (!widgetType) return;

    // Use unsavedPreferences when in edit mode, otherwise use preferences
    const currentPreferences = isEditMode && unsavedPreferences ? unsavedPreferences : preferences;

    // Find the highest y position to place the new widget at the bottom
    let maxY = 0;
    if (currentPreferences?.widgets?.length > 0) {
      maxY = Math.max(...currentPreferences.widgets.map(w => w.position.y + w.position.h));
    }

    addWidget(
      type, 
      widgetType.title, 
      { x: 0, y: maxY, w: 6, h: 2 }
    );

    handleAddWidgetClose();
  };

  // Handle removing a widget
  const handleRemoveWidget = (widgetId) => {
    if (window.confirm('Are you sure you want to remove this widget?')) {
      removeWidget(widgetId);
    }
  };

  // Handle editing a widget
  const handleEditWidget = (widgetId) => {
    // Use unsavedPreferences when in edit mode, otherwise use preferences
    const currentPreferences = isEditMode && unsavedPreferences ? unsavedPreferences : preferences;
    const widget = currentPreferences?.widgets?.find(w => w._id === widgetId);
    if (widget) {
      handleSettingsOpen(widget);
    }
  };

  // State for widget settings form
  const [widgetFormSettings, setWidgetFormSettings] = useState({});
  const [widgetTitle, setWidgetTitle] = useState('');

  // Handle saving widget settings
  const handleSaveSettings = () => {
    if (!currentWidget) return;

    updateWidget(currentWidget._id, {
      title: widgetTitle || currentWidget.title,
      settings: widgetFormSettings
    });

    handleSettingsClose();
  };

  // Handle layout changes
  const handleLayoutChange = (layout, layouts) => {
    // Only allow layout changes in edit mode and if widgets exist
    if (!isEditMode || !unsavedPreferences?.widgets) return;

    // Set resizing flag to true to prevent loading indicator during resize
    setIsResizing(true);

    // Get the current breakpoint's layout
    // This is the layout that was just changed by the user
    const currentLayout = layout;

    // Convert the layout to the format expected by the API
    const updatedLayout = currentLayout.map(item => ({
      i: item.i,
      x: item.x,
      y: item.y,
      w: item.w,
      h: item.h
    }));

    // Update layout immediately to track changes
    originalUpdateLayout(updatedLayout);
    
    // Reset the resizing flag after a delay to prevent loading indicator during resize
    debouncedResetResizing();
  };

  // Handle saving dashboard changes
  const handleSaveDashboard = async () => {
    if (hasUnsavedChanges) {
      try {
        await saveDashboardChanges();
        // Pass true to toggleEditMode to skip saving again
        toggleEditMode(true);
      } catch (err) {
        console.error('Error saving dashboard:', err);
        // You might want to show an error message to the user here
        return; // Don't exit edit mode if saving fails
      }
    } else {
      // If no unsaved changes, just toggle edit mode
      toggleEditMode();
    }
  };

  // Handle resetting the dashboard
  const handleResetDashboard = () => {
    if (window.confirm('Are you sure you want to reset your dashboard to default?')) {
      resetDashboard();
      handleMenuClose();
    }
  };

  // Convert widgets to layout items for react-grid-layout
  const getLayoutItems = (breakpoint = 'lg') => {
    // Use unsavedPreferences when in edit mode, otherwise use preferences
    const currentPreferences = isEditMode && unsavedPreferences ? unsavedPreferences : preferences;
    if (!currentPreferences?.widgets) return [];

    // Define column counts for each breakpoint
    const cols = { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 };

    // Scale factor for the current breakpoint relative to lg
    const scaleFactor = cols[breakpoint] / cols.lg;

    return currentPreferences.widgets.map(widget => {
      // For shortcuts and recentFiles widgets, ensure they're half width at all breakpoints
      let w = widget.position.w;
      let x = widget.position.x;

      if (widget.type === 'shortcuts' || widget.type === 'recentFiles') {
        // For smaller breakpoints, make these widgets full width
        if (breakpoint === 'sm' || breakpoint === 'xs' || breakpoint === 'xxs') {
          w = cols[breakpoint]; // Full width
          x = 0; // Start at the beginning of the row
        } else {
          // For larger breakpoints, make them half width
          w = Math.floor(cols[breakpoint] / 2);
          // Position shortcuts at the start, recentFiles at the middle
          x = widget.type === 'shortcuts' ? 0 : w;
        }
      } else {
        // For other widgets, scale width proportionally
        w = Math.max(Math.floor(widget.position.w * scaleFactor), 1);
        x = Math.min(Math.floor(widget.position.x * scaleFactor), cols[breakpoint] - w);
      }

      return {
        i: widget._id.toString(),
        x: x,
        y: widget.position.y,
        w: w,
        h: widget.position.h,
        minW: Math.min(3, cols[breakpoint]),
        minH: 2
      };
    });
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Welcome, {user?.name}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Here's your dashboard with quick access to important resources
          </Typography>
        </Box>
        <Box>
          <Tooltip title={isEditMode ? "Save Layout" : "Customize Dashboard"}>
            <IconButton 
              color={isEditMode ? "primary" : "default"} 
              onClick={isEditMode ? handleSaveDashboard : () => toggleEditMode()}
              sx={{ mr: 1 }}
            >
              {isEditMode ? <SaveIcon /> : <EditIcon />}
            </IconButton>
          </Tooltip>
          <Tooltip title="Dashboard Options">
            <IconButton
              onClick={handleMenuOpen}
              aria-controls={menuOpen ? 'dashboard-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={menuOpen ? 'true' : undefined}
            >
              <DashboardIcon />
            </IconButton>
          </Tooltip>
          <Menu
            id="dashboard-menu"
            anchorEl={menuAnchorEl}
            open={menuOpen}
            onClose={handleMenuClose}
            MenuListProps={{
              'aria-labelledby': 'dashboard-menu-button',
            }}
          >
            <MenuItem onClick={handleResetDashboard}>
              <ListItemIcon>
                <RefreshIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Reset Dashboard</ListItemText>
            </MenuItem>
          </Menu>
        </Box>
      </Box>

      {loading && !isResizing ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>
      ) : (isEditMode && unsavedPreferences ? unsavedPreferences : preferences)?.widgets?.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Your dashboard is empty
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Add widgets to customize your dashboard
          </Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={handleAddWidgetOpen}
          >
            Add Widget
          </Button>
        </Paper>
      ) : (
        <Box sx={{ position: 'relative', pb: 8 }}>
          <ResponsiveGridLayout
            className="layout"
            layouts={{ 
              lg: getLayoutItems('lg'),
              md: getLayoutItems('md'),
              sm: getLayoutItems('sm'),
              xs: getLayoutItems('xs'),
              xxs: getLayoutItems('xxs')
            }}
            breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
            cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
            rowHeight={100}
            isDraggable={isEditMode}
            isResizable={isEditMode}
            onLayoutChange={handleLayoutChange}
            margin={[16, 16]}
          >
            {(isEditMode && unsavedPreferences ? unsavedPreferences : preferences)?.widgets?.map(widget => (
              <div key={widget._id.toString()}>
                {renderWidget(widget, handleRemoveWidget, handleEditWidget)}
              </div>
            ))}
          </ResponsiveGridLayout>

          {isEditMode && (
            <Tooltip title="Add Widget">
              <Fab 
                color="primary" 
                aria-label="add widget"
                onClick={handleAddWidgetOpen}
                sx={{ position: 'fixed', bottom: 20, right: 20 }}
              >
                <AddIcon />
              </Fab>
            </Tooltip>
          )}
        </Box>
      )}

      {/* Add Widget Dialog */}
      <Dialog 
        open={addWidgetOpen} 
        onClose={handleAddWidgetClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Add Widget
          <IconButton
            aria-label="close"
            onClick={handleAddWidgetClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <List>
            {widgetTypes.map((widgetType) => (
              <ListItemButton 
                key={widgetType.type}
                onClick={() => handleAddWidget(widgetType.type)}
              >
                <ListItemText 
                  primary={widgetType.title} 
                  secondary={widgetType.description} 
                />
              </ListItemButton>
            ))}
          </List>
        </DialogContent>
      </Dialog>

      {/* Widget Settings Dialog */}
      {currentWidget && (
        <Dialog 
          open={settingsOpen} 
          onClose={handleSettingsClose}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Widget Settings
            <IconButton
              aria-label="close"
              onClick={handleSettingsClose}
              sx={{
                position: 'absolute',
                right: 8,
                top: 8,
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            <Typography variant="subtitle1" gutterBottom>
              Title
            </Typography>
            <TextField 
              fullWidth
              margin="normal"
              value={widgetTitle}
              onChange={(e) => setWidgetTitle(e.target.value)}
              placeholder="Widget Title"
              sx={{ mb: 3 }}
            />

            <Divider sx={{ my: 2 }} />
            
            <Typography variant="subtitle1" gutterBottom>
              Settings
            </Typography>
            <WidgetSettingsForm 
              widgetType={currentWidget.type}
              initialSettings={currentWidget.settings}
              onChange={setWidgetFormSettings}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleSettingsClose}>Cancel</Button>
            <Button 
              onClick={handleSaveSettings}
              variant="contained"
            >
              Save
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </Container>
  );
};

export default DashboardPage;
