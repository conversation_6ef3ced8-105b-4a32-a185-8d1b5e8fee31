import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Avatar,
  Button,
  Card,
  CardContent,
  Grid,
  Divider,
  Chip,
  TextField,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Paper,
  Tab,
  Tabs
} from '@mui/material';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  LocalOffer as TagIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelCircleIcon
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';

const UserProfilePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user: currentUser } = useAuth();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editing, setEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    avatar: '',
    jobTitle: '',
    department: '',
    phoneNumber: '',
    location: '',
    bio: '',
    skills: [],
    tags: []
  });
  const [newSkill, setNewSkill] = useState('');
  const [newTag, setNewTag] = useState('');
  const [skillDialogOpen, setSkillDialogOpen] = useState(false);
  const [tagDialogOpen, setTagDialogOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [serviceProvisioning, setServiceProvisioning] = useState({});
  const [provisioningLoading, setProvisioningLoading] = useState(false);
  const [provisioningError, setProvisioningError] = useState(null);
  const [serviceDefinitions, setServiceDefinitions] = useState({});
  
  // State for user preferences
  const [userPreferences, setUserPreferences] = useState({});
  const [preferencesLoading, setPreferencesLoading] = useState(false);
  const [preferencesError, setPreferencesError] = useState(null);
  const [quickLinksToolbarEnabled, setQuickLinksToolbarEnabled] = useState(true);

  // Fetch user data
  useEffect(() => {
    const fetchUser = async () => {
      setLoading(true);
      try {
        const res = await axios.get(`/api/staff-directory/users/${id}`);
        setUser(res.data);
        setFormData({
          name: res.data.name || '',
          avatar: res.data.avatar || '',
          jobTitle: res.data.jobTitle || '',
          department: res.data.department || '',
          phoneNumber: res.data.phoneNumber || '',
          location: res.data.location || '',
          bio: res.data.bio || '',
          skills: res.data.skills || [],
          tags: res.data.tags || []
        });
        
        // Fetch service provisioning settings
        try {
          const provisioningRes = await axios.get(`/api/staff-directory/users/${id}/provisioning`);
          setServiceProvisioning(provisioningRes.data || {});
          
          // Fetch service definitions to get display names
          try {
            const servicesRes = await axios.get('/api/provisioning/services');
            const servicesMap = {};
            servicesRes.data.forEach(service => {
              servicesMap[service.id] = {
                displayName: service.displayName || service.id,
                description: service.description
              };
            });
            setServiceDefinitions(servicesMap);
          } catch (servicesErr) {
            console.error('Error fetching service definitions:', servicesErr);
            // Don't set an error for this, as it's not critical
          }
        } catch (provErr) {
          console.error('Error fetching provisioning settings:', provErr);
          // Don't set an error for this, as it's not critical
          setServiceProvisioning({});
        }
        
        // Fetch user widget preferences
        try {
          setPreferencesLoading(true);
          const preferencesRes = await axios.get('/api/users/me/widget-preferences');
          setUserPreferences(preferencesRes.data || {});
          
          // Set quick links toolbar enabled state
          if (preferencesRes.data && preferencesRes.data.floatingShortcut) {
            setQuickLinksToolbarEnabled(preferencesRes.data.floatingShortcut.enabled !== false);
          }
          
          setPreferencesError(null);
        } catch (prefErr) {
          console.error('Error fetching widget preferences:', prefErr);
          // Don't set an error for this, as it's not critical
          setPreferencesError(null);
        } finally {
          setPreferencesLoading(false);
        }
        
        setError(null);
      } catch (err) {
        console.error('Error fetching user:', err);
        setError('Failed to load user profile. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [id]);

  // Check if current user can edit this profile
  const canEdit = currentUser && (currentUser._id === id || currentUser.roles.includes('admin'));

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      let res;

      // Use the new /api/users/me endpoint when editing own profile
      if (currentUser._id === id) {
        res = await axios.put('/api/users/me', formData);
      } else {
        // For admins editing other users' profiles
        res = await axios.put(`/api/staff-directory/users/${id}/profile`, formData);
      }

      setUser(res.data);
      setEditing(false);
      setError(null);
    } catch (err) {
      console.error('Error updating profile:', err);
      setError('Failed to update profile. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle adding a skill
  const handleAddSkill = () => {
    if (newSkill.trim() && !formData.skills.includes(newSkill.trim())) {
      setFormData({
        ...formData,
        skills: [...formData.skills, newSkill.trim()]
      });
      setNewSkill('');
    }
    setSkillDialogOpen(false);
  };

  // Handle removing a skill
  const handleRemoveSkill = (skillToRemove) => {
    setFormData({
      ...formData,
      skills: formData.skills.filter(skill => skill !== skillToRemove)
    });
  };

  // Handle adding a tag
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, newTag.trim()]
      });
      setNewTag('');
    }
    setTagDialogOpen(false);
  };

  // Handle removing a tag
  const handleRemoveTag = (tagToRemove) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(tag => tag !== tagToRemove)
    });
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // Handle toggling service provisioning
  const handleToggleProvisioning = async (service, enabled) => {
    setProvisioningLoading(true);
    setProvisioningError(null);
    
    try {
      const res = await axios.put(`/api/staff-directory/users/${id}/provisioning/${service}`, {
        enabled
      });
      
      setServiceProvisioning(prev => ({
        ...prev,
        [service]: {
          ...prev[service],
          enabled
        }
      }));
    } catch (err) {
      const serviceName = serviceDefinitions[service]?.displayName || service;
      console.error(`Error toggling ${serviceName} provisioning:`, err);
      setProvisioningError(`Failed to update ${serviceName} provisioning. Please try again.`);
    } finally {
      setProvisioningLoading(false);
    }
  };
  
  // Handle toggling quick links toolbar
  const handleToggleQuickLinksToolbar = async (enabled) => {
    setPreferencesLoading(true);
    setPreferencesError(null);
    
    try {
      // Update local state
      setQuickLinksToolbarEnabled(enabled);
      
      // Save to server
      await axios.put('/api/users/me/widget-preferences', {
        widgetType: 'floatingShortcut',
        preferences: {
          ...userPreferences.floatingShortcut,
          enabled: enabled
        }
      });
      
      // Update local storage for immediate effect
      localStorage.setItem('floatingShortcutWidgetEnabled', enabled ? 'true' : 'false');
      
      setPreferencesError(null);
    } catch (err) {
      console.error('Error updating quick links toolbar preference:', err);
      setPreferencesError('Failed to update quick links toolbar preference. Please try again.');
      
      // Revert local state on error
      setQuickLinksToolbarEnabled(!enabled);
    } finally {
      setPreferencesLoading(false);
    }
  };

  // Handle sync profile with Google
  const handleSyncProfile = async () => {
    if (currentUser._id !== id) return;

    setLoading(true);
    try {
      const res = await axios.post('/api/staff-directory/google/sync-profile');
      setUser(res.data);
      setFormData({
        jobTitle: res.data.jobTitle || '',
        department: res.data.department || '',
        phoneNumber: res.data.phoneNumber || '',
        location: res.data.location || '',
        bio: res.data.bio || '',
        skills: res.data.skills || [],
        tags: res.data.tags || []
      });
      setError(null);
    } catch (err) {
      console.error('Error syncing profile with Google:', err);
      setError('Failed to sync profile with Google. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !user) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !user) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error}</Typography>
        <Button variant="contained" onClick={() => navigate('/staff-directory')} sx={{ mt: 2 }}>
          Back to Staff Directory
        </Button>
      </Box>
    );
  }

  if (!user) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>User not found</Typography>
        <Button variant="contained" onClick={() => navigate('/staff-directory')} sx={{ mt: 2 }}>
          Back to Staff Directory
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          User Profile
        </Typography>
        <Box>
          {currentUser._id === id && (
            <Button
              variant="outlined"
              onClick={handleSyncProfile}
              disabled={loading}
              sx={{ mr: 1 }}
            >
              Sync with Google
            </Button>
          )}
          <Button
            variant="contained"
            onClick={() => navigate('/staff-directory')}
          >
            Back to Directory
          </Button>
        </Box>
      </Box>

      {error && (
        <Box sx={{ mb: 3 }}>
          <Typography color="error">{error}</Typography>
        </Box>
      )}

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab icon={<PersonIcon />} label="Profile" />
          <Tab icon={<GroupIcon />} label="Teams & Groups" />
          <Tab icon={<SettingsIcon />} label="Service Provisioning" />
          <Tab icon={<SettingsIcon />} label="Settings" />
        </Tabs>
      </Paper>

      {tabValue === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Avatar
                  src={user.avatar ? `/api/auth/avatar-proxy?url=${encodeURIComponent(user.avatar)}` : undefined}
                  alt={user.name}
                  sx={{ width: 120, height: 120, mx: 'auto', mb: 2 }}
                />
                <Typography variant="h5" gutterBottom>
                  {user.name}
                </Typography>
                {formData.jobTitle && !editing && (
                  <Typography variant="subtitle1" color="text.secondary">
                    {formData.jobTitle}
                  </Typography>
                )}
                {formData.department && !editing && (
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    {formData.department}
                  </Typography>
                )}

                {!editing && canEdit && (
                  <Button
                    variant="contained"
                    startIcon={<EditIcon />}
                    onClick={() => setEditing(true)}
                    sx={{ mt: 2 }}
                  >
                    Edit Profile
                  </Button>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                {editing ? (
                  <form onSubmit={handleSubmit}>
                    <Typography variant="h6" gutterBottom>
                      Edit Profile
                    </Typography>

                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Avatar URL"
                          name="avatar"
                          value={formData.avatar}
                          onChange={handleInputChange}
                          margin="normal"
                          helperText="Enter a URL for your profile picture"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Job Title"
                          name="jobTitle"
                          value={formData.jobTitle}
                          onChange={handleInputChange}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Department"
                          name="department"
                          value={formData.department}
                          onChange={handleInputChange}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Phone Number"
                          name="phoneNumber"
                          value={formData.phoneNumber}
                          onChange={handleInputChange}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Location"
                          name="location"
                          value={formData.location}
                          onChange={handleInputChange}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Bio"
                          name="bio"
                          value={formData.bio}
                          onChange={handleInputChange}
                          margin="normal"
                          multiline
                          rows={4}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <Typography variant="subtitle1">Skills</Typography>
                          <Button
                            size="small"
                            startIcon={<AddIcon />}
                            onClick={() => setSkillDialogOpen(true)}
                          >
                            Add Skill
                          </Button>
                        </Box>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {formData.skills.map((skill, index) => (
                            <Chip
                              key={index}
                              label={skill}
                              onDelete={() => handleRemoveSkill(skill)}
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Grid>

                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <Typography variant="subtitle1">Tags</Typography>
                          <Button
                            size="small"
                            startIcon={<AddIcon />}
                            onClick={() => setTagDialogOpen(true)}
                          >
                            Add Tag
                          </Button>
                        </Box>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {formData.tags.map((tag, index) => (
                            <Chip
                              key={index}
                              label={tag}
                              onDelete={() => handleRemoveTag(tag)}
                              color="primary"
                            />
                          ))}
                        </Box>
                      </Grid>
                    </Grid>

                    <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                      <Button
                        variant="outlined"
                        startIcon={<CancelIcon />}
                        onClick={() => {
                          setEditing(false);
                          setFormData({
                            name: user.name || '',
                            avatar: user.avatar || '',
                            jobTitle: user.jobTitle || '',
                            department: user.department || '',
                            phoneNumber: user.phoneNumber || '',
                            location: user.location || '',
                            bio: user.bio || '',
                            skills: user.skills || [],
                            tags: user.tags || []
                          });
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={<SaveIcon />}
                        disabled={loading}
                      >
                        Save
                      </Button>
                    </Box>
                  </form>
                ) : (
                  <>
                    <Typography variant="h6" gutterBottom>
                      Contact Information
                    </Typography>

                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mb: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body1" component="a" href={`mailto:${user.email}`} sx={{ textDecoration: 'none', color: 'primary.main' }}>
                          {user.email}
                        </Typography>
                      </Box>

                      {formData.phoneNumber && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body1" component="a" href={`tel:${formData.phoneNumber}`} sx={{ textDecoration: 'none', color: 'primary.main' }}>
                            {formData.phoneNumber}
                          </Typography>
                        </Box>
                      )}

                      {formData.department && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <BusinessIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body1">
                            {formData.department}
                          </Typography>
                        </Box>
                      )}

                      {formData.location && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <LocationIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body1">
                            {formData.location}
                          </Typography>
                        </Box>
                      )}
                    </Box>

                    {formData.bio && (
                      <>
                        <Typography variant="h6" gutterBottom>
                          Bio
                        </Typography>
                        <Typography variant="body1" paragraph>
                          {formData.bio}
                        </Typography>
                      </>
                    )}

                    {formData.skills && formData.skills.length > 0 && (
                      <>
                        <Typography variant="h6" gutterBottom>
                          Skills
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 3 }}>
                          {formData.skills.map((skill, index) => (
                            <Chip
                              key={index}
                              label={skill}
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </>
                    )}

                    {formData.tags && formData.tags.length > 0 && (
                      <>
                        <Typography variant="h6" gutterBottom>
                          Tags
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {formData.tags.map((tag, index) => (
                            <Chip
                              key={index}
                              label={tag}
                              color="primary"
                            />
                          ))}
                        </Box>
                      </>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Teams
                </Typography>

                {user.teams && user.teams.length > 0 ? (
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {user.teams.map((team) => (
                      <Card key={team._id} variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle1" component="div">
                          {team.name}
                        </Typography>
                        {team.description && (
                          <Typography variant="body2" color="text.secondary">
                            {team.description}
                          </Typography>
                        )}
                      </Card>
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Not a member of any teams
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Groups
                </Typography>

                {user.groups && user.groups.length > 0 ? (
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {user.groups.map((group) => (
                      <Card key={group._id} variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle1" component="div">
                          {group.name}
                        </Typography>
                        {group.description && (
                          <Typography variant="body2" color="text.secondary">
                            {group.description}
                          </Typography>
                        )}
                      </Card>
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Not a member of any groups
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {tabValue === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Service Provisioning
                  </Typography>
                  {canEdit && (
                    <Typography variant="body2" color="text.secondary">
                      Enable or disable automatic provisioning for various services
                    </Typography>
                  )}
                </Box>
                
                {provisioningError && (
                  <Box sx={{ mb: 3 }}>
                    <Typography color="error">{provisioningError}</Typography>
                  </Box>
                )}
                
                {provisioningLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <Box>
                    {Object.keys(serviceProvisioning).length === 0 ? (
                      <Typography variant="body2" color="text.secondary">
                        No service provisioning settings available
                      </Typography>
                    ) : (
                      <Grid container spacing={2}>
                        {Object.entries(serviceProvisioning).map(([service, settings]) => (
                          <Grid item xs={12} sm={6} md={4} key={service}>
                            <Card variant="outlined" sx={{ p: 2 }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                <Typography variant="subtitle1">
                                  {serviceDefinitions[service]?.displayName || settings.displayName || service}
                                </Typography>
                                {settings.enabled ? (
                                  <Chip 
                                    icon={<CheckCircleIcon />} 
                                    label="Enabled" 
                                    color="success" 
                                    size="small" 
                                  />
                                ) : (
                                  <Chip 
                                    icon={<CancelCircleIcon />} 
                                    label="Disabled" 
                                    color="default" 
                                    size="small" 
                                  />
                                )}
                              </Box>
                              
                              {(serviceDefinitions[service]?.description || settings.description) && (
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                  {serviceDefinitions[service]?.description || settings.description}
                                </Typography>
                              )}
                              
                              {canEdit && (
                                <Button
                                  variant="outlined"
                                  color={settings.enabled ? "error" : "primary"}
                                  size="small"
                                  onClick={() => handleToggleProvisioning(service, !settings.enabled)}
                                  disabled={provisioningLoading}
                                  fullWidth
                                >
                                  {settings.enabled ? "Disable" : "Enable"} Provisioning
                                </Button>
                              )}
                            </Card>
                          </Grid>
                        ))}
                      </Grid>
                    )}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    User Interface Settings
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Customize your portal experience
                  </Typography>
                </Box>
                
                {preferencesError && (
                  <Box sx={{ mb: 3 }}>
                    <Typography color="error">{preferencesError}</Typography>
                  </Box>
                )}
                
                {preferencesLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <Box>
                    <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1">
                          Quick Links Toolbar
                        </Typography>
                        {quickLinksToolbarEnabled ? (
                          <Chip 
                            icon={<CheckCircleIcon />} 
                            label="Enabled" 
                            color="success" 
                            size="small" 
                          />
                        ) : (
                          <Chip 
                            icon={<CancelCircleIcon />} 
                            label="Disabled" 
                            color="default" 
                            size="small" 
                          />
                        )}
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Show the quick links toolbar at the bottom of the screen for easy access to your favorite links
                      </Typography>
                      
                      <Button
                        variant="outlined"
                        color={quickLinksToolbarEnabled ? "error" : "primary"}
                        size="small"
                        onClick={() => handleToggleQuickLinksToolbar(!quickLinksToolbarEnabled)}
                        disabled={preferencesLoading}
                        fullWidth
                      >
                        {quickLinksToolbarEnabled ? "Disable" : "Enable"} Quick Links Toolbar
                      </Button>
                    </Card>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Dialog for adding a skill */}
      <Dialog open={skillDialogOpen} onClose={() => setSkillDialogOpen(false)}>
        <DialogTitle>Add Skill</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Skill"
            fullWidth
            value={newSkill}
            onChange={(e) => setNewSkill(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSkillDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddSkill} color="primary">Add</Button>
        </DialogActions>
      </Dialog>

      {/* Dialog for adding a tag */}
      <Dialog open={tagDialogOpen} onClose={() => setTagDialogOpen(false)}>
        <DialogTitle>Add Tag</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Tag"
            fullWidth
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTagDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddTag} color="primary">Add</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserProfilePage;
