import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  ListItemButton,
  Breadcrumbs,
  Link,
  Button,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Tooltip,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  ToggleButton,
  ToggleButtonGroup,
  TextField,
  InputAdornment
} from '@mui/material';
import {
  Folder as FolderIcon,
  InsertDriveFile as FileIcon,
  ArrowUpward as UpIcon,
  Refresh as RefreshIcon,
  Share as ShareIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  ViewList as ListViewIcon,
  ViewModule as GridViewIcon,
  Sort as SortIcon,
  Search as SearchIcon,
  KeyboardArrowUp as SortAscIcon,
  KeyboardArrowDown as SortDescIcon
} from '@mui/icons-material';
import synologyService from '../../services/synologyService';
import { formatFileSize, formatDate } from '../../utils/formatters';

const SynologyFileBrowser = () => {
  // Path and file state
  const [currentPath, setCurrentPath] = useState('/');
  const [files, setFiles] = useState([]);
  
  // Loading and error states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  
  // Sharing states
  const [sharingLink, setSharingLink] = useState(null);
  const [sharingLoading, setSharingLoading] = useState(false);
  const [sharingError, setSharingError] = useState(null);
  
  // Pagination states
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 50,
    hasMore: false,
    total: 0
  });
  
  // Sorting states
  const [sortBy, setSortBy] = useState('name');
  const [sortDirection, setSortDirection] = useState('ASC');
  
  // View mode state (list or grid)
  const [viewMode, setViewMode] = useState('list');
  
  // Search state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(null);
  
  // Path history for navigation
  const [pathHistory, setPathHistory] = useState(['/']);
  const [historyIndex, setHistoryIndex] = useState(0);
  
  // Selected file for keyboard navigation
  const [selectedFileIndex, setSelectedFileIndex] = useState(-1);
  
  // Ref for the file browser container (for keyboard focus)
  const fileBrowserRef = React.useRef(null);
  
  const navigate = useNavigate();

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await synologyService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Fetch files when currentPath, pagination, or sorting changes
  useEffect(() => {
    if (configStatus) {
      fetchFiles(currentPath, pagination.page, pagination.pageSize);
    }
  }, [currentPath, pagination.page, pagination.pageSize, sortBy, sortDirection, configStatus]);
  
  // Set up keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Skip if we're in an input field or if there are no files
      if (
        e.target.tagName === 'INPUT' || 
        e.target.tagName === 'TEXTAREA' || 
        e.target.isContentEditable || 
        files.length === 0 || 
        loading
      ) {
        return;
      }
      
      // Initialize selection if none exists
      if (selectedFileIndex === -1 && files.length > 0) {
        setSelectedFileIndex(0);
        return;
      }
      
      switch (e.key) {
        case 'ArrowDown':
          // In list view, move down one item
          // In grid view, move down one row (depends on screen width)
          e.preventDefault();
          if (viewMode === 'list') {
            setSelectedFileIndex(prev => Math.min(prev + 1, files.length - 1));
          } else {
            // For grid view, estimate items per row based on screen width
            // This is a rough estimate and might need adjustment
            const itemsPerRow = Math.floor(window.innerWidth / 200);
            setSelectedFileIndex(prev => Math.min(prev + itemsPerRow, files.length - 1));
          }
          break;
          
        case 'ArrowUp':
          // In list view, move up one item
          // In grid view, move up one row
          e.preventDefault();
          if (viewMode === 'list') {
            setSelectedFileIndex(prev => Math.max(prev - 1, 0));
          } else {
            const itemsPerRow = Math.floor(window.innerWidth / 200);
            setSelectedFileIndex(prev => Math.max(prev - itemsPerRow, 0));
          }
          break;
          
        case 'ArrowRight':
          // In list view, open folder if selected
          // In grid view, move right one item
          e.preventDefault();
          if (viewMode === 'list' && files[selectedFileIndex]?.isdir) {
            handleFileClick(files[selectedFileIndex]);
          } else if (viewMode === 'grid') {
            setSelectedFileIndex(prev => Math.min(prev + 1, files.length - 1));
          }
          break;
          
        case 'ArrowLeft':
          // In list view, go to parent directory
          // In grid view, move left one item
          e.preventDefault();
          if (viewMode === 'list' && currentPath !== '/') {
            handleUpClick();
          } else if (viewMode === 'grid') {
            setSelectedFileIndex(prev => Math.max(prev - 1, 0));
          }
          break;
          
        case 'Enter':
          // Open folder or download file
          e.preventDefault();
          if (selectedFileIndex >= 0 && selectedFileIndex < files.length) {
            const selectedFile = files[selectedFileIndex];
            if (selectedFile.isdir) {
              handleFileClick(selectedFile);
            } else {
              // Download the file
              window.open(synologyService.getDownloadUrl(selectedFile.path), '_blank');
            }
          }
          break;
          
        case 'Backspace':
          // Go to parent directory
          e.preventDefault();
          if (currentPath !== '/') {
            handleUpClick();
          }
          break;
          
        case 'Home':
          // Go to first file
          e.preventDefault();
          setSelectedFileIndex(0);
          break;
          
        case 'End':
          // Go to last file
          e.preventDefault();
          setSelectedFileIndex(files.length - 1);
          break;
          
        case 'PageUp':
          // Go to previous page
          e.preventDefault();
          if (pagination.page > 1) {
            setPagination(prev => ({
              ...prev,
              page: prev.page - 1
            }));
          }
          break;
          
        case 'PageDown':
          // Go to next page
          e.preventDefault();
          if (pagination.hasMore) {
            setPagination(prev => ({
              ...prev,
              page: prev.page + 1
            }));
          }
          break;
          
        case 'Escape':
          // Clear selection
          e.preventDefault();
          setSelectedFileIndex(-1);
          break;
          
        default:
          // For letter keys, jump to first file starting with that letter
          if (e.key.length === 1 && e.key.match(/[a-zA-Z0-9]/)) {
            const key = e.key.toLowerCase();
            const index = files.findIndex(file => 
              file.name.toLowerCase().startsWith(key)
            );
            if (index !== -1) {
              setSelectedFileIndex(index);
            }
          }
          break;
      }
    };
    
    // Focus the file browser container when it's mounted
    if (fileBrowserRef.current) {
      fileBrowserRef.current.focus();
    }
    
    // Add event listener for keyboard navigation
    window.addEventListener('keydown', handleKeyDown);
    
    // Clean up event listener
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [
    files, 
    selectedFileIndex, 
    currentPath, 
    viewMode, 
    pagination.page, 
    pagination.hasMore, 
    loading
  ]);

  /**
   * Fetch files from the server with pagination and sorting
   * @param {string} path - The path to fetch files from
   * @param {number} page - The page number (1-based)
   * @param {number} pageSize - The number of items per page
   */
  const fetchFiles = async (path, page = 1, pageSize = 50) => {
    setLoading(true);
    setError(null);
    setSharingLink(null);
    setSharingError(null);
    
    // Reset search results when changing directories
    if (searchResults && path !== currentPath) {
      setSearchQuery('');
      setSearchResults(null);
    }

    try {
      // Prepare options with pagination and sorting parameters
      const options = {
        page,
        pageSize,
        sortBy,
        sortDirection
      };
      
      // If we have a search query, add it to the options
      if (searchQuery) {
        options.pattern = searchQuery;
      }
      
      // Fetch files with pagination and sorting
      const result = await synologyService.listFiles(path, options);
      
      // Update state with the response
      setFiles(result.files || []);
      
      // Update pagination state
      setPagination({
        page: result.pagination?.page || 1,
        pageSize: result.pagination?.pageSize || result.files.length,
        hasMore: result.pagination?.hasMore || false,
        total: result.pagination?.total || result.files.length
      });
      
      // Update path history if this is a new path
      if (path !== currentPath) {
        // If we're not at the end of the history, truncate it
        if (historyIndex < pathHistory.length - 1) {
          setPathHistory(prev => [...prev.slice(0, historyIndex + 1), path]);
        } else {
          // Otherwise, just add the new path
          setPathHistory(prev => [...prev, path]);
        }
        setHistoryIndex(prev => prev + 1);
      }
    } catch (err) {
      console.error('Error fetching files:', err);
      
      // Check if this is a 2FA error
      if (err.requires2FA) {
        setError(
          'Two-factor authentication (2FA) is enabled on this Synology account. ' +
          'Please disable 2FA for this account or use a different account without 2FA. ' +
          'Details: ' + (err.details || err.message)
        );
      } else if (err.pagination) {
        // Handle pagination-specific errors
        setError(`Failed to load files: ${err.message}. Please try a different page or page size.`);
        // Reset pagination to safe values
        setPagination(prev => ({
          ...prev,
          page: 1,
          hasMore: false
        }));
      } else {
        setError('Failed to load files. Please check your connection and try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle file or folder click
   * @param {Object} file - The file or folder object
   */
  const handleFileClick = (file) => {
    if (file.isdir) {
      // Navigate to the folder
      setCurrentPath(file.path);
      // Reset pagination when navigating to a new folder
      setPagination(prev => ({
        ...prev,
        page: 1
      }));
    } else {
      // For files, do nothing (download is handled by the download button)
    }
  };

  /**
   * Navigate to the parent directory
   */
  const handleUpClick = () => {
    if (currentPath === '/') return;

    // Get parent directory
    const pathParts = currentPath.split('/').filter(Boolean);
    pathParts.pop();
    const parentPath = pathParts.length === 0 ? '/' : '/' + pathParts.join('/');

    setCurrentPath(parentPath);
    // Reset pagination when navigating to a new folder
    setPagination(prev => ({
      ...prev,
      page: 1
    }));
  };

  /**
   * Refresh the current directory
   */
  const handleRefreshClick = () => {
    fetchFiles(currentPath, pagination.page, pagination.pageSize);
  };

  /**
   * Navigate to a specific path from breadcrumbs
   * @param {string} path - The path to navigate to
   */
  const handleBreadcrumbClick = (path) => {
    setCurrentPath(path);
    // Reset pagination when navigating to a new folder
    setPagination(prev => ({
      ...prev,
      page: 1
    }));
  };
  
  /**
   * Handle page change in pagination
   * @param {Object} event - The event object
   * @param {number} page - The new page number
   */
  const handlePageChange = (event, page) => {
    setPagination(prev => ({
      ...prev,
      page
    }));
  };
  
  /**
   * Handle page size change
   * @param {Object} event - The event object
   */
  const handlePageSizeChange = (event) => {
    const newPageSize = parseInt(event.target.value, 10);
    setPagination(prev => ({
      ...prev,
      pageSize: newPageSize,
      page: 1 // Reset to first page when changing page size
    }));
  };
  
  /**
   * Handle sort field change
   * @param {Object} event - The event object
   */
  const handleSortByChange = (event) => {
    setSortBy(event.target.value);
  };
  
  /**
   * Toggle sort direction between ASC and DESC
   */
  const handleSortDirectionToggle = () => {
    setSortDirection(prev => prev === 'ASC' ? 'DESC' : 'ASC');
  };
  
  /**
   * Handle view mode change (list or grid)
   * @param {Object} event - The event object
   * @param {string} newViewMode - The new view mode
   */
  const handleViewModeChange = (event, newViewMode) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
    }
  };

  // Navigate to configuration page
  const handleGoToConfig = () => {
    navigate('/synology/setup');
  };

  const handleShareClick = async (file) => {
    setSharingLoading(true);
    setSharingError(null);
    setSharingLink(null);

    try {
      const linkInfo = await synologyService.createSharingLink(file.path);
      setSharingLink(linkInfo);
    } catch (err) {
      console.error('Error creating sharing link:', err);
      
      // Check if this is a 2FA error
      if (err.requires2FA) {
        setSharingError(
          'Two-factor authentication (2FA) is enabled on this Synology account. ' +
          'Please disable 2FA for this account or use a different account without 2FA. ' +
          'Details: ' + (err.details || err.message)
        );
      } else {
        setSharingError('Failed to create sharing link. Please try again.');
      }
    } finally {
      setSharingLoading(false);
    }
  };

  // Generate breadcrumbs from current path
  const generateBreadcrumbs = () => {
    const pathParts = currentPath.split('/').filter(Boolean);

    return (
      <Breadcrumbs aria-label="breadcrumb">
        <Link 
          color="inherit" 
          href="#" 
          onClick={(e) => {
            e.preventDefault();
            handleBreadcrumbClick('/');
          }}
        >
          Root
        </Link>

        {pathParts.map((part, index) => {
          const path = '/' + pathParts.slice(0, index + 1).join('/');
          const isLast = index === pathParts.length - 1;

          return isLast ? (
            <Typography color="text.primary" key={path}>
              {part}
            </Typography>
          ) : (
            <Link
              color="inherit"
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleBreadcrumbClick(path);
              }}
              key={path}
            >
              {part}
            </Link>
          );
        })}
      </Breadcrumbs>
    );
  };

  if (loadingConfig) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!configStatus) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Synology File Browser
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<SettingsIcon />}
            onClick={handleGoToConfig}
            fullWidth
            size="large"
            sx={{ py: 2 }}
          >
            Configure Synology
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Synology File Browser
        </Typography>

        <Paper sx={{ p: 2, mb: 2 }}>
          {/* Navigation Bar */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{ flexGrow: 1 }}>
              {generateBreadcrumbs()}
            </Box>
            <Box>
              <Tooltip title="Go up">
                <IconButton 
                  onClick={handleUpClick} 
                  disabled={currentPath === '/'}
                  size="small"
                >
                  <UpIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Refresh">
                <IconButton 
                  onClick={handleRefreshClick}
                  size="small"
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Alerts */}
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {sharingLink && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Sharing link created: <Link href={sharingLink.url} target="_blank">{sharingLink.url}</Link>
            </Alert>
          )}

          {sharingError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {sharingError}
            </Alert>
          )}

          {/* Toolbar with controls */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 2 }}>
            {/* View Mode Toggle */}
            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={handleViewModeChange}
              aria-label="view mode"
              size="small"
            >
              <ToggleButton value="list" aria-label="list view">
                <ListViewIcon />
              </ToggleButton>
              <ToggleButton value="grid" aria-label="grid view">
                <GridViewIcon />
              </ToggleButton>
            </ToggleButtonGroup>

            {/* Sorting Controls */}
            <FormControl variant="outlined" size="small" sx={{ minWidth: 120 }}>
              <InputLabel id="sort-by-label">Sort By</InputLabel>
              <Select
                labelId="sort-by-label"
                id="sort-by"
                value={sortBy}
                onChange={handleSortByChange}
                label="Sort By"
              >
                <MenuItem value="name">Name</MenuItem>
                <MenuItem value="size">Size</MenuItem>
                <MenuItem value="time">Date</MenuItem>
                <MenuItem value="type">Type</MenuItem>
              </Select>
            </FormControl>

            <Tooltip title={sortDirection === 'ASC' ? 'Ascending' : 'Descending'}>
              <IconButton onClick={handleSortDirectionToggle} size="small">
                {sortDirection === 'ASC' ? <SortAscIcon /> : <SortDescIcon />}
              </IconButton>
            </Tooltip>

            {/* Search Input */}
            <TextField
              size="small"
              placeholder="Search files..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ flexGrow: 1 }}
            />

            {/* Page Size Selector */}
            <FormControl variant="outlined" size="small" sx={{ minWidth: 80 }}>
              <InputLabel id="page-size-label">Show</InputLabel>
              <Select
                labelId="page-size-label"
                id="page-size"
                value={pagination.pageSize}
                onChange={handlePageSizeChange}
                label="Show"
              >
                <MenuItem value={20}>20</MenuItem>
                <MenuItem value={50}>50</MenuItem>
                <MenuItem value={100}>100</MenuItem>
                <MenuItem value={200}>200</MenuItem>
              </Select>
            </FormControl>
          </Box>

          {/* File List or Grid */}
          <Box 
            ref={fileBrowserRef} 
            tabIndex={0} 
            sx={{ 
              outline: 'none', // Remove outline when focused
              width: '100%'
            }}
          >
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : viewMode === 'list' ? (
              // List View
              <List>
                {files.length === 0 ? (
                  <ListItem>
                    <ListItemText primary="No files found in this directory" />
                  </ListItem>
                ) : (
                  files.map((file, index) => (
                    <React.Fragment key={file.path}>
                      <ListItem
                        disablePadding
                        selected={index === selectedFileIndex}
                        secondaryAction={
                          <Box>
                            {!file.isdir && (
                              <Tooltip title="Download">
                                <IconButton 
                                  edge="end" 
                                  aria-label="download"
                                  href={synologyService.getDownloadUrl(file.path)}
                                  download
                                >
                                  <DownloadIcon />
                                </IconButton>
                              </Tooltip>
                            )}
                            <Tooltip title="Share">
                              <IconButton 
                                edge="end" 
                                aria-label="share"
                                onClick={() => handleShareClick(file)}
                                disabled={sharingLoading}
                              >
                                {sharingLoading ? <CircularProgress size={24} /> : <ShareIcon />}
                              </IconButton>
                            </Tooltip>
                          </Box>
                        }
                      >
                        <ListItemButton 
                          onClick={() => handleFileClick(file)}
                          onMouseEnter={() => setSelectedFileIndex(index)}
                          sx={{
                            bgcolor: index === selectedFileIndex ? 'action.selected' : 'inherit',
                            '&:hover': {
                              bgcolor: index === selectedFileIndex ? 'action.selected' : 'action.hover'
                            }
                          }}
                        >
                          <ListItemIcon>
                            {file.isdir ? <FolderIcon /> : <FileIcon />}
                          </ListItemIcon>
                          <ListItemText 
                            primary={file.name} 
                            secondary={
                              <React.Fragment>
                                {file.isdir ? 'Folder' : formatFileSize(file.size)}
                                {file.time && ` • ${formatDate(file.time.mtime * 1000)}`}
                              </React.Fragment>
                            } 
                          />
                        </ListItemButton>
                      </ListItem>
                      <Divider />
                    </React.Fragment>
                  ))
                )}
              </List>
            ) : (
              // Grid View
              <Grid container spacing={2}>
                {files.length === 0 ? (
                  <Grid item xs={12}>
                    <Typography variant="body1" align="center" sx={{ p: 3 }}>
                      No files found in this directory
                    </Typography>
                  </Grid>
                ) : (
                  files.map((file, index) => (
                    <Grid item xs={6} sm={4} md={3} lg={2} key={file.path}>
                      <Paper 
                        elevation={index === selectedFileIndex ? 4 : 2} 
                        sx={{ 
                          p: 2, 
                          display: 'flex', 
                          flexDirection: 'column', 
                          alignItems: 'center',
                          height: '100%',
                          cursor: 'pointer',
                          bgcolor: index === selectedFileIndex ? 'action.selected' : 'inherit',
                          border: index === selectedFileIndex ? '2px solid' : 'none',
                          borderColor: 'primary.main',
                          '&:hover': {
                            bgcolor: index === selectedFileIndex ? 'action.selected' : 'action.hover'
                          }
                        }}
                        onClick={() => handleFileClick(file)}
                        onMouseEnter={() => setSelectedFileIndex(index)}
                      >
                        {file.isdir ? 
                          <FolderIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} /> : 
                          <FileIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                        }
                        <Typography 
                          variant="body2" 
                          align="center" 
                          sx={{ 
                            wordBreak: 'break-word',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            mb: 1,
                            fontWeight: index === selectedFileIndex ? 'bold' : 'normal'
                          }}
                        >
                          {file.name}
                        </Typography>
                        <Typography 
                          variant="caption" 
                          color={index === selectedFileIndex ? 'text.primary' : 'text.secondary'} 
                          align="center"
                        >
                          {file.isdir ? 'Folder' : formatFileSize(file.size)}
                        </Typography>
                        <Box sx={{ mt: 'auto', pt: 1, display: 'flex', gap: 1 }}>
                          {!file.isdir && (
                            <Tooltip title="Download">
                              <IconButton 
                                size="small"
                                href={synologyService.getDownloadUrl(file.path)}
                                download
                                onClick={(e) => e.stopPropagation()}
                              >
                                <DownloadIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                          <Tooltip title="Share">
                            <IconButton 
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleShareClick(file);
                              }}
                              disabled={sharingLoading}
                            >
                              {sharingLoading ? <CircularProgress size={16} /> : <ShareIcon fontSize="small" />}
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Paper>
                    </Grid>
                  ))
                )}
              </Grid>
            )}
          </Box>
          
          {/* Pagination Controls */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, mb: 1 }}>
            <Pagination 
              count={pagination.hasMore ? pagination.page + 1 : pagination.page} 
              page={pagination.page}
              onChange={handlePageChange}
              color="primary"
              showFirstButton
              showLastButton
              disabled={loading}
            />
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default SynologyFileBrowser;
