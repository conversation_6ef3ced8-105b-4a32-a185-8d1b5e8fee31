import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button, 
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
  Link
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import unifiAccessService from '../../services/unifiAccessService';
import { useAuth } from '../../context/AuthContext';

const UnifiAccessPage = () => {
  const { hasIntegration } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [doors, setDoors] = useState([]);

  // Fetch configuration status and doors on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get configuration status
        const config = await unifiAccessService.getConfig();
        setConfigStatus(config);

        // If configured, get doors
        if (config) {
          const doorList = await unifiAccessService.getDoors();
          setDoors(doorList);
        }
      } catch (err) {
        console.error('Error fetching UniFi Access data:', err);
        setError('Failed to load UniFi Access data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // All integrations are now assumed to be active for all users
  // Access is controlled by roles and permissions instead

  if (loading) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // If not configured, show configuration required message
  if (!configStatus) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            UniFi Access
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              UniFi Access integration is not configured yet.
            </Alert>
            <Typography variant="body1" paragraph>
              To use the UniFi Access integration, an administrator needs to configure it first.
            </Typography>
            <Typography variant="body1" paragraph>
              Please contact your system administrator to set up the required environment variables.
            </Typography>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          UniFi Access Control
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Doors
          </Typography>

          {doors.length > 0 ? (
            <List>
              {doors.map((door, index) => (
                <React.Fragment key={door.id}>
                  <ListItem>
                    <ListItemText 
                      primary={door.name} 
                      secondary={`Status: ${door.status} | Location: ${door.location}`} 
                    />
                    <Button 
                      variant="outlined" 
                      size="small"
                      onClick={() => {/* Handle door action */}}
                    >
                      View Details
                    </Button>
                  </ListItem>
                  {index < doors.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <Typography variant="body1">
              No doors found.
            </Typography>
          )}
        </Paper>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Typography variant="caption" color="textSecondary">
            Configuration is managed through environment variables
          </Typography>
        </Box>
      </Box>
    </Container>
  );
};

export default UnifiAccessPage;
