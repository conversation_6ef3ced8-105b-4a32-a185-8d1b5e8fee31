import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Divider,
  Card,
  CardContent,
  Grid,
  Chip,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  MenuItem,
  Select,
  FormControl,
  InputLabel
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import DevicesIcon from '@mui/icons-material/Devices';
import SettingsIcon from '@mui/icons-material/Settings';
import LaptopMacIcon from '@mui/icons-material/LaptopMac';
import PhoneIphoneIcon from '@mui/icons-material/PhoneIphone';
import TabletMacIcon from '@mui/icons-material/TabletMac';
import LinkIcon from '@mui/icons-material/Link';
import LinkOffIcon from '@mui/icons-material/LinkOff';
import appleBusinessManagerService from '../../services/appleBusinessManagerService';
import { useAuth } from '../../context/AuthContext';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`apple-business-manager-tabpanel-${index}`}
      aria-labelledby={`apple-business-manager-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const AppleBusinessManagerPage = () => {
  const { hasIntegration } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [devices, setDevices] = useState([]);
  const [selectedItem, setSelectedItem] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  const [mdmAssignment, setMdmAssignment] = useState(null);
  const [loadingMdm, setLoadingMdm] = useState(false);
  const [mdmError, setMdmError] = useState(null);
  const [openAssignDialog, setOpenAssignDialog] = useState(false);
  const [openReassignDialog, setOpenReassignDialog] = useState(false);
  const [openUnassignDialog, setOpenUnassignDialog] = useState(false);
  const [mdmServerId, setMdmServerId] = useState('');
  const [mdmServers, setMdmServers] = useState([]);
  const [loadingMdmServers, setLoadingMdmServers] = useState(false);
  const [mdmServersError, setMdmServersError] = useState(null);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setSelectedItem(null);
  };

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await appleBusinessManagerService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Load MDM servers data
  useEffect(() => {
    const fetchMdmServers = async () => {
      if (!configStatus) return;

      setLoadingMdmServers(true);
      setMdmServersError(null);

      try {
        const mdmServersData = await appleBusinessManagerService.getMdmServers();
        setMdmServers(mdmServersData);
      } catch (err) {
        setMdmServersError('Failed to load MDM servers from Apple Business Manager. Please check your connection and try again.');
        console.error('Error loading Apple Business Manager MDM servers:', err);
      } finally {
        setLoadingMdmServers(false);
      }
    };

    fetchMdmServers();
  }, [configStatus]);

  // Load devices data
  useEffect(() => {
    const fetchData = async () => {
      if (!configStatus) return;

      setLoading(true);
      setError(null);

      try {
        const devicesData = await appleBusinessManagerService.getDevices();
        setDevices(devicesData);
      } catch (err) {
        setError('Failed to load data from Apple Business Manager. Please check your connection and try again.');
        console.error('Error loading Apple Business Manager data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [configStatus]);

  // Load MDM assignment information when a device is selected
  useEffect(() => {
    const fetchMdmAssignment = async () => {
      if (!selectedItem || !selectedItem.id) return;

      setLoadingMdm(true);
      setMdmError(null);

      try {
        const mdmData = await appleBusinessManagerService.getDeviceMdmAssignment(selectedItem.id);
        setMdmAssignment(mdmData);
      } catch (err) {
        setMdmError('Failed to load MDM assignment information. Please try again.');
        console.error('Error loading MDM assignment information:', err);
      } finally {
        setLoadingMdm(false);
      }
    };

    fetchMdmAssignment();
  }, [selectedItem]);

  // Handle MDM assignment dialog
  const handleOpenAssignDialog = () => {
    setMdmServerId('');
    setOpenAssignDialog(true);
  };

  const handleCloseAssignDialog = () => {
    setOpenAssignDialog(false);
  };

  // Handle MDM reassignment dialog
  const handleOpenReassignDialog = () => {
    setMdmServerId(mdmAssignment?.mdmServerId || '');
    setOpenReassignDialog(true);
  };

  const handleCloseReassignDialog = () => {
    setOpenReassignDialog(false);
  };

  // Handle MDM unassignment dialog
  const handleOpenUnassignDialog = () => {
    setOpenUnassignDialog(true);
  };

  const handleCloseUnassignDialog = () => {
    setOpenUnassignDialog(false);
  };

  // Handle MDM assignment
  const handleAssignDeviceToMdm = async () => {
    if (!selectedItem || !mdmServerId) return;

    setLoadingMdm(true);
    setMdmError(null);

    try {
      const result = await appleBusinessManagerService.assignDeviceToMdm(selectedItem.id, mdmServerId);
      setMdmAssignment(result);
      handleCloseAssignDialog();
    } catch (err) {
      setMdmError('Failed to assign device to MDM service. Please try again.');
      console.error('Error assigning device to MDM service:', err);
    } finally {
      setLoadingMdm(false);
    }
  };

  // Handle MDM reassignment
  const handleReassignDeviceToMdm = async () => {
    if (!selectedItem || !mdmServerId) return;

    setLoadingMdm(true);
    setMdmError(null);

    try {
      const result = await appleBusinessManagerService.reassignDeviceToMdm(selectedItem.id, mdmServerId);
      setMdmAssignment(result);
      handleCloseReassignDialog();
    } catch (err) {
      setMdmError('Failed to reassign device to MDM service. Please try again.');
      console.error('Error reassigning device to MDM service:', err);
    } finally {
      setLoadingMdm(false);
    }
  };

  // Handle MDM unassignment
  const handleUnassignDeviceFromMdm = async () => {
    if (!selectedItem) return;

    setLoadingMdm(true);
    setMdmError(null);

    try {
      const result = await appleBusinessManagerService.unassignDeviceFromMdm(selectedItem.id);
      setMdmAssignment(null);
      handleCloseUnassignDialog();
    } catch (err) {
      setMdmError('Failed to unassign device from MDM service. Please try again.');
      console.error('Error unassigning device from MDM service:', err);
    } finally {
      setLoadingMdm(false);
    }
  };

  // Handle item selection
  const handleItemSelect = (item) => {
    setSelectedItem(item);
  };

  // Get device icon based on product family
  const getDeviceIcon = (productFamily) => {
    switch (productFamily?.toLowerCase()) {
      case 'mac':
      case 'macbook':
      case 'imac':
      case 'macmini':
        return <LaptopMacIcon />;
      case 'iphone':
        return <PhoneIphoneIcon />;
      case 'ipad':
        return <TabletMacIcon />;
      default:
        return <DevicesIcon />;
    }
  };

  // All integrations are now assumed to be active for all users
  // Access is controlled by roles and permissions instead

  if (loadingConfig) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!configStatus) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Apple Business Manager
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              Apple Business Manager integration is not configured. Please contact your administrator to set up the required environment variables.
            </Alert>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Box sx={{ mb: 2 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Apple Business Manager
          </Typography>
        </Box>

        <Paper sx={{ width: '100%', mb: 2 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            centered
          >
            <Tab icon={<DevicesIcon />} label="Devices" />
          </Tabs>

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TabPanel value={tabValue} index={0}>
                {devices.length > 0 ? (
                  <Box sx={{ display: 'flex' }}>
                    <Box sx={{ width: '30%', borderRight: 1, borderColor: 'divider', pr: 2 }}>
                      <Typography variant="h6" gutterBottom>
                        Your Devices
                      </Typography>
                      <List>
                        {Array.isArray(devices) ? devices.map((device) => (
                          <ListItem
                            button
                            key={device.id}
                            onClick={() => handleItemSelect(device)}
                            sx={{
                              backgroundColor: selectedItem && selectedItem.id === device.id
                                ? 'action.selected'
                                : 'transparent',
                              '&:hover': {
                                backgroundColor: selectedItem && selectedItem.id === device.id
                                  ? 'action.selected'
                                  : 'action.hover'
                              }
                            }}
                          >
                            <ListItemIcon>
                              {getDeviceIcon(device.attributes?.productFamily)}
                            </ListItemIcon>
                            <ListItemText 
                              primary={device.attributes?.deviceModel || 'Unknown Device'} 
                              secondary={device.attributes?.serialNumber || device.id} 
                            />
                          </ListItem>
                        )) : null}
                      </List>
                    </Box>
                    <Box sx={{ width: '70%', pl: 2 }}>
                      {selectedItem ? (
                        <>
                          <Typography variant="h6" gutterBottom>
                            {selectedItem.attributes?.deviceModel || 'Device Details'}
                          </Typography>
                          <Card>
                            <CardContent>
                              <Grid container spacing={2}>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Serial Number</Typography>
                                  <Typography variant="body1">{selectedItem.attributes?.serialNumber || 'N/A'}</Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Model</Typography>
                                  <Typography variant="body1">{selectedItem.attributes?.deviceModel || 'N/A'}</Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Product Family</Typography>
                                  <Typography variant="body1">{selectedItem.attributes?.productFamily || 'N/A'}</Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Status</Typography>
                                  <Chip 
                                    label={selectedItem.attributes?.status || 'Unknown'} 
                                    color={selectedItem.attributes?.status === 'ASSIGNED' ? 'success' : 'default'} 
                                    size="small" 
                                  />
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Product Type</Typography>
                                  <Typography variant="body1">{selectedItem.attributes?.productType || 'N/A'}</Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Capacity</Typography>
                                  <Typography variant="body1">{selectedItem.attributes?.deviceCapacity || 'N/A'}</Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Color</Typography>
                                  <Typography variant="body1">{selectedItem.attributes?.color || 'N/A'}</Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Part Number</Typography>
                                  <Typography variant="body1">{selectedItem.attributes?.partNumber || 'N/A'}</Typography>
                                </Grid>
                                <Grid item xs={12}>
                                  <Divider sx={{ my: 1 }} />
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Order Number</Typography>
                                  <Typography variant="body1">{selectedItem.attributes?.orderNumber || 'N/A'}</Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Order Date</Typography>
                                  <Typography variant="body1">
                                    {selectedItem.attributes?.orderDateTime ? 
                                      new Date(selectedItem.attributes.orderDateTime).toLocaleDateString() : 
                                      'N/A'}
                                  </Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Added to Organization</Typography>
                                  <Typography variant="body1">
                                    {selectedItem.attributes?.addedToOrgDateTime ? 
                                      new Date(selectedItem.attributes.addedToOrgDateTime).toLocaleDateString() : 
                                      'N/A'}
                                  </Typography>
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                  <Typography variant="subtitle2">Last Updated</Typography>
                                  <Typography variant="body1">
                                    {selectedItem.attributes?.updatedDateTime ? 
                                      new Date(selectedItem.attributes.updatedDateTime).toLocaleDateString() : 
                                      'N/A'}
                                  </Typography>
                                </Grid>
                                <Grid item xs={12}>
                                  <Divider sx={{ my: 1 }} />
                                </Grid>
                                <Grid item xs={12}>
                                  <Typography variant="h6" gutterBottom>
                                    MDM Assignment
                                  </Typography>
                                  {loadingMdm ? (
                                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                      <CircularProgress size={24} />
                                    </Box>
                                  ) : mdmError ? (
                                    <Alert severity="error" sx={{ mb: 2 }}>
                                      {mdmError}
                                    </Alert>
                                  ) : (
                                    <>
                                      {mdmAssignment ? (
                                        <Box>
                                          <Grid container spacing={2}>
                                            <Grid item xs={12} sm={6}>
                                              <Typography variant="subtitle2">MDM Server</Typography>
                                              <Typography variant="body1">
                                                {loadingMdmServers ? (
                                                  <CircularProgress size={16} />
                                                ) : (
                                                  Array.isArray(mdmServers) ?
                                                    mdmServers.find(server => server.id === mdmAssignment.mdmServerId)?.attributes?.name ||
                                                    mdmAssignment.mdmServerId ||
                                                    'N/A' :
                                                    mdmAssignment.mdmServerId || 'N/A'
                                                )}
                                              </Typography>
                                            </Grid>
                                            <Grid item xs={12} sm={6}>
                                              <Typography variant="subtitle2">Assignment Date</Typography>
                                              <Typography variant="body1">
                                                {mdmAssignment.assignmentDate ? 
                                                  new Date(mdmAssignment.assignmentDate).toLocaleDateString() : 
                                                  'N/A'}
                                              </Typography>
                                            </Grid>
                                            <Grid item xs={12}>
                                              <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
                                                <Button 
                                                  variant="outlined" 
                                                  color="primary" 
                                                  startIcon={<LinkIcon />}
                                                  onClick={handleOpenReassignDialog}
                                                >
                                                  Reassign to Different MDM
                                                </Button>
                                                <Button 
                                                  variant="outlined" 
                                                  color="error" 
                                                  startIcon={<LinkOffIcon />}
                                                  onClick={handleOpenUnassignDialog}
                                                >
                                                  Unassign from MDM
                                                </Button>
                                              </Box>
                                            </Grid>
                                          </Grid>
                                        </Box>
                                      ) : (
                                        <Box>
                                          <Alert severity="info" sx={{ mb: 2 }}>
                                            This device is not assigned to any MDM server.
                                          </Alert>
                                          <Button 
                                            variant="contained" 
                                            color="primary" 
                                            startIcon={<LinkIcon />}
                                            onClick={handleOpenAssignDialog}
                                          >
                                            Assign to MDM
                                          </Button>
                                        </Box>
                                      )}
                                    </>
                                  )}
                                </Grid>
                              </Grid>
                            </CardContent>
                          </Card>

                          {/* MDM Assignment Dialog */}
                          <Dialog open={openAssignDialog} onClose={handleCloseAssignDialog}>
                            <DialogTitle>Assign Device to MDM Server</DialogTitle>
                            <DialogContent>
                              <DialogContentText>
                                Select an MDM server to assign this device to.
                              </DialogContentText>
                              {mdmServersError && (
                                <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
                                  {mdmServersError}
                                </Alert>
                              )}
                              {loadingMdmServers ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                  <CircularProgress size={24} />
                                </Box>
                              ) : (
                                <FormControl fullWidth sx={{ mt: 2 }}>
                                  <InputLabel id="mdm-server-select-label">MDM Server</InputLabel>
                                  <Select
                                    labelId="mdm-server-select-label"
                                    id="mdm-server-select"
                                    value={mdmServerId}
                                    label="MDM Server"
                                    variant="outlined"
                                    onChange={(e) => setMdmServerId(e.target.value)}
                                  >
                                    {Array.isArray(mdmServers) ? mdmServers.map((server) => (
                                      <MenuItem key={server.id} value={server.id}>
                                        {server.attributes?.name || server.id}
                                      </MenuItem>
                                    )) : null}
                                  </Select>
                                </FormControl>
                              )}
                            </DialogContent>
                            <DialogActions>
                              <Button onClick={handleCloseAssignDialog}>Cancel</Button>
                              <Button 
                                onClick={handleAssignDeviceToMdm} 
                                disabled={!mdmServerId || loadingMdmServers}
                                variant="contained"
                              >
                                Assign
                              </Button>
                            </DialogActions>
                          </Dialog>

                          {/* MDM Reassignment Dialog */}
                          <Dialog open={openReassignDialog} onClose={handleCloseReassignDialog}>
                            <DialogTitle>Reassign Device to Different MDM Server</DialogTitle>
                            <DialogContent>
                              <DialogContentText>
                                Select a different MDM server to reassign this device to.
                              </DialogContentText>
                              {mdmServersError && (
                                <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
                                  {mdmServersError}
                                </Alert>
                              )}
                              {loadingMdmServers ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                                  <CircularProgress size={24} />
                                </Box>
                              ) : (
                                <FormControl fullWidth sx={{ mt: 2 }}>
                                  <InputLabel id="mdm-server-reassign-label">MDM Server</InputLabel>
                                  <Select
                                    labelId="mdm-server-reassign-label"
                                    id="mdm-server-reassign"
                                    value={mdmServerId}
                                    label="MDM Server"
                                    variant="outlined"
                                    onChange={(e) => setMdmServerId(e.target.value)}
                                  >
                                    {Array.isArray(mdmServers) ? mdmServers.map((server) => (
                                      <MenuItem key={server.id} value={server.id}>
                                        {server.attributes?.name || server.id}
                                      </MenuItem>
                                    )) : null}
                                  </Select>
                                </FormControl>
                              )}
                            </DialogContent>
                            <DialogActions>
                              <Button onClick={handleCloseReassignDialog}>Cancel</Button>
                              <Button 
                                onClick={handleReassignDeviceToMdm} 
                                disabled={!mdmServerId || loadingMdmServers}
                                variant="contained"
                              >
                                Reassign
                              </Button>
                            </DialogActions>
                          </Dialog>

                          {/* MDM Unassignment Dialog */}
                          <Dialog open={openUnassignDialog} onClose={handleCloseUnassignDialog}>
                            <DialogTitle>Unassign Device from MDM Server</DialogTitle>
                            <DialogContent>
                              <DialogContentText>
                                Are you sure you want to unassign this device from its current MDM server?
                                This action cannot be undone.
                              </DialogContentText>
                            </DialogContent>
                            <DialogActions>
                              <Button onClick={handleCloseUnassignDialog}>Cancel</Button>
                              <Button 
                                onClick={handleUnassignDeviceFromMdm} 
                                color="error"
                                variant="contained"
                              >
                                Unassign
                              </Button>
                            </DialogActions>
                          </Dialog>
                        </>
                      ) : (
                        <Typography>Select a device to view its details</Typography>
                      )}
                    </Box>
                  </Box>
                ) : (
                  <Alert severity="info">No devices found in your Apple Business Manager account.</Alert>
                )}
              </TabPanel>
            </>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default AppleBusinessManagerPage;