import React, { useState, useEffect, useCallback } from 'react';
import { 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  CircularProgress, 
  Alert,
  Box,
  Button
} from '@mui/material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { 
  Folder as FolderIcon, 
  Description as FileIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import googleDriveService from '../../services/googleDriveService';
import Widget from './Widget';

const RecentFilesWidget = ({ id, title = 'Recent Files', onRemove, onEdit, settings = {} }) => {
  const navigate = useNavigate();
  const [recentFiles, setRecentFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);

  // Number of files to display, default to 5 if not specified in settings
  const limit = settings.limit || 5;

  // Fetch files from Google Drive
  const fetchFiles = useCallback(async (isMountedRef = { current: true }) => {
    try {
      setLoading(true);
      // Use the same query approach as GoogleDriveFilesPage
      // This filters files from the root folder by default
      const options = { 
        q: "'root' in parents",
        orderBy: "modifiedTime desc" // Sort by most recently modified
      };
      console.log('RecentFilesWidget: Fetching files with options:', JSON.stringify(options));
      const data = await googleDriveService.listFiles(options);
      console.log(`RecentFilesWidget: Successfully retrieved ${data.length} files`);
      
      // Check if component is still mounted before updating state
      if (isMountedRef.current) {
        setRecentFiles(data.slice(0, limit)); // Get top N
        setLoading(false);
      }
    } catch (err) {
      console.error('Error fetching files from Google Drive:', err);
      
      // Check if component is still mounted before updating state
      if (isMountedRef.current) {
        setError('Failed to load recent files');
        setLoading(false);
      }
    }
  }, [limit]);

  // Directly try to fetch files instead of checking config first
  useEffect(() => {
    // Create a ref to track if component is mounted
    const isMountedRef = { current: true };
    
    const loadFiles = async () => {
      try {
        console.log('RecentFilesWidget: Directly fetching files');
        
        // Pass the isMountedRef to fetchFiles
        await fetchFiles(isMountedRef);
        
        // If we successfully fetched files, we're authenticated
        if (isMountedRef.current) {
          setConfigStatus({ isAuthenticated: true });
        }
      } catch (err) {
        console.error('Error fetching files from Google Drive:', err);
        
        if (isMountedRef.current) {
          // If we get a 401 or 403 error, we're not authenticated
          if (err.response && (err.response.status === 401 || err.response.status === 403)) {
            console.log('RecentFilesWidget: Google Drive not authenticated');
            setConfigStatus({ isAuthenticated: false });
          } else {
            setError('Failed to load recent files');
          }
          setLoading(false);
        }
      }
    };

    loadFiles();
    
    // Cleanup function to prevent state updates if component unmounts
    return () => {
      isMountedRef.current = false;
    };
  }, [fetchFiles]);

  // Handle navigation to Google Drive config page
  const handleGoToConfig = () => {
    navigate('/google-drive/config');
  };

  return (
    <Widget 
      id={id} 
      title={title} 
      onRemove={onRemove} 
      onEdit={onEdit}
      settings={settings}
      showSettings={true}
    >
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : !configStatus || !configStatus.isAuthenticated ? (
        <Box sx={{ my: 2 }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<SettingsIcon />}
            onClick={handleGoToConfig}
            fullWidth
            size="medium"
          >
            Configure Google Drive
          </Button>
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : recentFiles.length === 0 ? (
        <Alert severity="info">No recent files</Alert>
      ) : (
        <>
          <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
            <List>
              {recentFiles.map((file) => (
                <ListItem 
                  key={file.id}
                  component={RouterLink}
                  to={`/google-drive/view/${file.id}`}
                  button
                  divider
                >
                  <ListItemIcon>
                    {file.mimeType.includes('folder') ? <FolderIcon /> : <FileIcon />}
                  </ListItemIcon>
                  <ListItemText 
                    primary={file.name} 
                    secondary={new Date(file.modifiedTime).toLocaleDateString()}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button 
              component={RouterLink} 
              to="/google-drive" 
              size="small"
            >
              View All
            </Button>
          </Box>
        </>
      )}
    </Widget>
  );
};

export default RecentFilesWidget;
