import React from 'react';
import ShortcutsWidget from './ShortcutsWidget';
import RecentFilesWidget from './RecentFilesWidget';
import QuickActionsWidget from './QuickActionsWidget';
import GoogleCalendarWidget from './GoogleCalendarWidget';
import UnifiProtectWidget from './UnifiProtectWidget';
import WiimWidget from './WiimWidget';
import NotesWidget from './NotesWidget';
import NewsWidget from './NewsWidget';

// Registry of all available widgets
export const widgetTypes = [
  {
    type: 'notes',
    title: 'Notes',
    description: 'Create and manage personal notes and to-dos',
    icon: 'note',
    defaultSettings: {
      limit: 5,
      filterType: 'all',
      showCompleted: true
    },
    settingsSchema: {
      limit: {
        type: 'number',
        label: 'Number of notes to display',
        min: 1,
        max: 20
      },
      filterType: {
        type: 'select',
        label: 'Filter by type',
        options: [
          { value: 'all', label: 'All' },
          { value: 'note', label: 'Notes only' },
          { value: 'todo', label: 'To-dos only' }
        ]
      },
      showCompleted: {
        type: 'boolean',
        label: 'Show completed to-dos'
      }
    }
  },
  {
    type: 'shortcuts',
    title: 'Popular Shortcuts',
    description: 'Display most frequently used shortcuts',
    icon: 'link',
    defaultSettings: {
      limit: 5
    },
    settingsSchema: {
      limit: {
        type: 'number',
        label: 'Number of shortcuts to display',
        min: 1,
        max: 10
      }
    }
  },
  {
    type: 'recentFiles',
    title: 'Recent Files',
    description: 'Display recently accessed Google Drive files',
    icon: 'description',
    defaultSettings: {
      limit: 5
    },
    settingsSchema: {
      limit: {
        type: 'number',
        label: 'Number of files to display',
        min: 1,
        max: 10
      }
    }
  },
  {
    type: 'quickActions',
    title: 'Quick Actions',
    description: 'Display quick access buttons to various features',
    icon: 'apps',
    defaultSettings: {
      enabledActions: [
        'shortcuts', 'drive', 'canva', 'glpi', 'planningCenter', 'synology', 
        'unifiAccess', 'unifiNetwork', 'unifiProtect', 'dreo', 'mosyleBusiness', 
        'lenelS2NetBox', 'googleCalendar', 'googleDrive', 'appleBusinessManager',
        'buildingManagement', 'googleAdmin', 'googleForms', 'lgThinq', 'marketplace',
        'people', 'radius', 'roomBooking', 'staffDirectory', 'tasks', 'wiim'
      ]
    },
    settingsSchema: {
      enabledActions: {
        type: 'multiselect',
        label: 'Enabled actions',
        options: [
          { value: 'shortcuts', label: 'Shortcuts' },
          { value: 'drive', label: 'Drive Files' },
          { value: 'canva', label: 'Canva' },
          { value: 'glpi', label: 'GLPI' },
          { value: 'planningCenter', label: 'Planning Center' },
          { value: 'synology', label: 'Synology' },
          { value: 'unifiAccess', label: 'Unifi Access' },
          { value: 'unifiNetwork', label: 'Unifi Network' },
          { value: 'unifiProtect', label: 'Unifi Protect' },
          { value: 'dreo', label: 'Dreo' },
          { value: 'mosyleBusiness', label: 'Mosyle Business' },
          { value: 'lenelS2NetBox', label: 'Lenel S2 NetBox' },
          { value: 'googleCalendar', label: 'Google Calendar' },
          { value: 'googleDrive', label: 'Google Drive' },
          { value: 'appleBusinessManager', label: 'Apple Business Manager' },
          { value: 'buildingManagement', label: 'Building Management' },
          { value: 'googleAdmin', label: 'Google Admin' },
          { value: 'googleForms', label: 'Google Forms' },
          { value: 'lgThinq', label: 'LG ThinQ' },
          { value: 'marketplace', label: 'Marketplace' },
          { value: 'people', label: 'People' },
          { value: 'radius', label: 'Radius' },
          { value: 'roomBooking', label: 'Room Booking' },
          { value: 'staffDirectory', label: 'Staff Directory' },
          { value: 'tasks', label: 'Tasks' },
          { value: 'wiim', label: 'WiiM Media Hub' }
        ]
      }
    }
  },
  {
    type: 'googleCalendar',
    title: 'Upcoming Events',
    description: 'Display upcoming events from Google Calendar',
    icon: 'event',
    defaultSettings: {
      limit: 5,
      calendarIds: ['primary']
    },
    settingsSchema: {
      limit: {
        type: 'number',
        label: 'Number of events to display',
        min: 1,
        max: 10
      },
      calendarIds: {
        type: 'multiselect',
        label: 'Calendars to display',
        options: [], // This will be populated dynamically
        async: true,
        asyncOptionsUrl: '/api/google-calendar/calendars',
        asyncOptionMapping: {
          value: 'id',
          label: 'summary'
        }
      }
    }
  },
  {
    type: 'unifiProtect',
    title: 'Camera Feeds',
    description: 'Display camera feeds from Unifi Protect',
    icon: 'videocam',
    defaultSettings: {
      limit: 4,
      cameraIds: []
    },
    settingsSchema: {
      limit: {
        type: 'number',
        label: 'Number of cameras to display',
        min: 1,
        max: 8
      },
      cameraIds: {
        type: 'multiselect',
        label: 'Specific cameras to display (leave empty for all)',
        options: [], // This will be populated dynamically
        async: true,
        asyncOptionsUrl: '/api/unifi-protect/cameras',
        asyncOptionMapping: {
          value: 'id',
          label: 'name'
        }
      }
    }
  },
  {
    type: 'wiim',
    title: 'WiiM Media Player',
    description: 'Control your WiiM media player and see what\'s playing',
    icon: 'music_note',
    defaultSettings: {
      refreshInterval: 10
    },
    settingsSchema: {
      refreshInterval: {
        type: 'number',
        label: 'Refresh interval (seconds)',
        min: 5,
        max: 60
      }
    }
  },
  {
    type: 'news',
    title: 'Latest News',
    description: 'Display latest news posts from the organization',
    icon: 'article',
    defaultSettings: {
      limit: 5,
      showCategory: true,
      showAuthor: true
    },
    settingsSchema: {
      limit: {
        type: 'number',
        label: 'Number of news posts to display',
        min: 1,
        max: 10
      },
      showCategory: {
        type: 'boolean',
        label: 'Show category'
      },
      showAuthor: {
        type: 'boolean',
        label: 'Show author'
      }
    }
  }
];

// Function to get widget component by type
export const getWidgetComponent = (type) => {
  switch (type) {
    case 'notes':
      return NotesWidget;
    case 'shortcuts':
      return ShortcutsWidget;
    case 'recentFiles':
      return RecentFilesWidget;
    case 'quickActions':
      return QuickActionsWidget;
    case 'googleCalendar':
      return GoogleCalendarWidget;
    case 'unifiProtect':
      return UnifiProtectWidget;
    case 'wiim':
      return WiimWidget;
    case 'news':
      return NewsWidget;
    default:
      console.error(`Unknown widget type: ${type}`);
      return null;
  }
};

// Function to get default settings for a widget type
export const getDefaultSettings = (type) => {
  const widgetType = widgetTypes.find(w => w.type === type);
  return widgetType ? widgetType.defaultSettings : {};
};

// Function to render a widget based on its configuration
export const renderWidget = (widget, onRemove, onEdit) => {
  const WidgetComponent = getWidgetComponent(widget.type);

  if (!WidgetComponent) {
    return null;
  }

  return (
    <WidgetComponent
      key={widget._id}
      id={widget._id}
      title={widget.title}
      settings={widget.settings}
      onRemove={onRemove}
      onEdit={onEdit}
    />
  );
};

export default {
  widgetTypes,
  getWidgetComponent,
  getDefaultSettings,
  renderWidget
};
