import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Badge,
  Collapse,
  Grid,
  Stack,
  Card,
  CardContent,
  Divider,
  Switch,
  FormControlLabel,
  Tooltip,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  InputAdornment,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import {
  BugReport as BugReportIcon,
  Close as CloseIcon,
  Clear as ClearIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Timeline as TimelineIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  AccessTime as AccessTimeIcon,
  Language as LanguageIcon,
  Storage as StorageIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { useDebug } from '../../context/DebugContext';
import './DebugWidget.css';

const DebugWidget = () => {
  const { user } = useAuth();
  const { 
    requests, 
    stats, 
    isConnected, 
    clearRequests, 
    refreshStats,
    subscribe,
    unsubscribe,
    isPaused,
    togglePause
  } = useDebug();
  
  const [open, setOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [inputValue, setInputValue] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    type: 'all',
    status: 'all',
    method: 'all',
    timeRange: 'all'
  });
  const searchTimeoutRef = useRef(null);
  const [expandedRows, setExpandedRows] = useState(new Set());
  const [autoScroll, setAutoScroll] = useState(false);
  const [showOnlyErrors, setShowOnlyErrors] = useState(false);
  const [groupByDomain, setGroupByDomain] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'timeline'
  const tableRef = useRef(null);

  // Debounced search function
  const debouncedSearch = useCallback((value) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    searchTimeoutRef.current = setTimeout(() => {
      setSearchQuery(value);
    }, 300); // 300ms delay
  }, []);

  // Initialize inputValue with searchQuery
  useEffect(() => {
    setInputValue(searchQuery);
  }, []);

  // Clear timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (open) {
      subscribe();
    } else {
      unsubscribe();
    }
    
    return () => {
      unsubscribe();
    };
  }, [open, subscribe, unsubscribe]);

  useEffect(() => {
    if (autoScroll && tableRef.current) {
      tableRef.current.scrollTop = 0;
    }
  }, [requests, autoScroll]);

  // Only show debug widget for admin users
  if (!user || !user.roles.includes('admin')) {
    return null;
  }

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleClearRequests = async () => {
    await clearRequests();
    setExpandedRows(new Set());
    setSelectedRequest(null);
  };

  const handleRefreshStats = async () => {
    await refreshStats();
  };

  const toggleRowExpansion = (requestId) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(requestId)) {
      newExpanded.delete(requestId);
    } else {
      newExpanded.add(requestId);
    }
    setExpandedRows(newExpanded);
  };

  const handleFilterChange = (filterType, value) => {
    setSelectedFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const getStatusColor = (statusCode, isError = false) => {
    if (isError || !statusCode) return 'error';
    if (statusCode >= 200 && statusCode < 300) return 'success';
    if (statusCode >= 300 && statusCode < 400) return 'info';
    if (statusCode >= 400 && statusCode < 500) return 'warning';
    if (statusCode >= 500) return 'error';
    return 'default';
  };

  const getStatusIcon = (statusCode, isError = false) => {
    if (isError) return <ErrorIcon fontSize="small" />;
    if (!statusCode) return <WarningIcon fontSize="small" />;
    if (statusCode >= 200 && statusCode < 300) return <CheckCircleIcon fontSize="small" />;
    if (statusCode >= 300 && statusCode < 400) return <LanguageIcon fontSize="small" />;
    if (statusCode >= 400) return <ErrorIcon fontSize="small" />;
    return <WarningIcon fontSize="small" />;
  };

  const formatJson = (obj) => {
    if (!obj) return '';
    try {
      return JSON.stringify(obj, null, 2);
    } catch (e) {
      return String(obj);
    }
  };

  const extractDomain = (url) => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch {
      return 'localhost';
    }
  };

  const getMethodColor = (method) => {
    switch (method?.toUpperCase()) {
      case 'GET': return 'primary';
      case 'POST': return 'success';
      case 'PUT': return 'warning';
      case 'DELETE': return 'error';
      case 'PATCH': return 'info';
      default: return 'default';
    }
  };

  const formatDuration = (duration) => {
    if (duration < 1000) return `${duration}ms`;
    return `${(duration / 1000).toFixed(2)}s`;
  };

  const getTimeAgo = (timestamp) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;
    
    if (diff < 60000) return `${Math.floor(diff / 1000)}s ago`;
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    return `${Math.floor(diff / 3600000)}h ago`;
  };

  // Enhanced filtering logic
  const filteredRequests = requests.filter(request => {
    // Search query filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesSearch = 
        request.url?.toLowerCase().includes(query) ||
        request.method?.toLowerCase().includes(query) ||
        request.statusCode?.toString().includes(query) ||
        extractDomain(request.url).toLowerCase().includes(query);
      
      if (!matchesSearch) return false;
    }

    // Type filter
    if (selectedFilters.type !== 'all') {
      const isExternal = request.type === 'external_api';
      if (selectedFilters.type === 'api' && isExternal) return false;
      if (selectedFilters.type === 'external' && !isExternal) return false;
    }

    // Status filter
    if (selectedFilters.status !== 'all') {
      const isError = request.error || (request.statusCode >= 400);
      const isSuccess = request.statusCode >= 200 && request.statusCode < 300;
      
      if (selectedFilters.status === 'success' && !isSuccess) return false;
      if (selectedFilters.status === 'error' && !isError) return false;
      if (selectedFilters.status === 'redirect' && (request.statusCode < 300 || request.statusCode >= 400)) return false;
    }

    // Method filter
    if (selectedFilters.method !== 'all' && request.method?.toUpperCase() !== selectedFilters.method) {
      return false;
    }

    // Time range filter
    if (selectedFilters.timeRange !== 'all') {
      const now = new Date();
      const requestTime = new Date(request.timestamp);
      const diff = now - requestTime;
      
      switch (selectedFilters.timeRange) {
        case '1m': if (diff > 60000) return false; break;
        case '5m': if (diff > 300000) return false; break;
        case '15m': if (diff > 900000) return false; break;
        default: break;
      }
    }

    // Show only errors filter
    if (showOnlyErrors) {
      const isError = request.error || (request.statusCode >= 400);
      if (!isError) return false;
    }

    return true;
  });

  // Group requests by domain if enabled
  const groupedRequests = groupByDomain ? 
    filteredRequests.reduce((groups, request) => {
      const domain = extractDomain(request.url);
      if (!groups[domain]) groups[domain] = [];
      groups[domain].push(request);
      return groups;
    }, {}) : 
    { 'All Requests': filteredRequests };

  const getUniqueValues = (key) => {
    const values = requests.map(r => r[key]).filter(Boolean);
    return [...new Set(values)];
  };

  // Enhanced Filter Bar Component
  const FilterBar = () => (
    <Card className="debug-filter-card" sx={{ mb: 2 }}>
      <CardContent sx={{ pb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              className="debug-search-field"
              fullWidth
              size="small"
              placeholder="Search requests, domains, methods..."
              value={inputValue}
              onChange={(e) => {
                const value = e.target.value;
                setInputValue(value);
                debouncedSearch(value);
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
            />
          </Grid>
          
          <Grid item xs={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Type</InputLabel>
              <Select
                value={selectedFilters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                label="Type"
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="api">Internal API</MenuItem>
                <MenuItem value="external">External API</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Status</InputLabel>
              <Select
                value={selectedFilters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                label="Status"
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="success">Success (2xx)</MenuItem>
                <MenuItem value="redirect">Redirect (3xx)</MenuItem>
                <MenuItem value="error">Error (4xx/5xx)</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Method</InputLabel>
              <Select
                value={selectedFilters.method}
                onChange={(e) => handleFilterChange('method', e.target.value)}
                label="Method"
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="GET">GET</MenuItem>
                <MenuItem value="POST">POST</MenuItem>
                <MenuItem value="PUT">PUT</MenuItem>
                <MenuItem value="DELETE">DELETE</MenuItem>
                <MenuItem value="PATCH">PATCH</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Time</InputLabel>
              <Select
                value={selectedFilters.timeRange}
                onChange={(e) => handleFilterChange('timeRange', e.target.value)}
                label="Time"
              >
                <MenuItem value="all">All Time</MenuItem>
                <MenuItem value="1m">Last 1 min</MenuItem>
                <MenuItem value="5m">Last 5 min</MenuItem>
                <MenuItem value="15m">Last 15 min</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box mt={2} display="flex" gap={2} alignItems="center" flexWrap="wrap">
          <FormControlLabel
            control={
              <Switch
                checked={showOnlyErrors}
                onChange={(e) => setShowOnlyErrors(e.target.checked)}
                color="error"
              />
            }
            label="Errors Only"
          />

          <FormControlLabel
            control={
              <Switch
                checked={groupByDomain}
                onChange={(e) => setGroupByDomain(e.target.checked)}
                color="primary"
              />
            }
            label="Group by Domain"
          />

          <FormControlLabel
            control={
              <Switch
                checked={autoScroll}
                onChange={(e) => setAutoScroll(e.target.checked)}
                color="primary"
              />
            }
            label="Auto Scroll"
          />

          <Tooltip title={isPaused ? "Resume monitoring" : "Pause monitoring"}>
            <IconButton
              onClick={togglePause}
              color={isPaused ? "error" : "primary"}
              size="small"
            >
              {isPaused ? <PlayArrowIcon /> : <PauseIcon />}
            </IconButton>
          </Tooltip>

          <Button
            variant="outlined"
            startIcon={<ClearIcon />}
            onClick={handleClearRequests}
            size="small"
            color="error"
          >
            Clear ({filteredRequests.length})
          </Button>
        </Box>
      </CardContent>
    </Card>
  );

  // Enhanced Request Card Component
  const RequestCard = ({ request }) => {
    const isExpanded = expandedRows.has(request.id);
    const isExternal = request.type === 'external_api';
    const isError = request.error || (request.statusCode >= 400);
    const domain = extractDomain(request.url);

    return (
      <Card 
        sx={{ 
          mb: 1, 
          cursor: 'pointer',
          border: isError ? '1px solid' : 'none',
          borderColor: isError ? 'error.main' : 'transparent',
          '&:hover': {
            boxShadow: 2
          }
        }}
        onClick={() => toggleRowExpansion(request.id)}
      >
        <CardContent sx={{ pb: 1 }}>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
            <Box display="flex" alignItems="center" gap={1}>
              <Avatar sx={{ width: 24, height: 24, bgcolor: getMethodColor(request.method) }}>
                <Typography variant="caption" fontWeight="bold">
                  {request.method?.[0]}
                </Typography>
              </Avatar>
              
              <Chip 
                label={request.method} 
                size="small" 
                color={getMethodColor(request.method)}
                variant="outlined"
              />
              
              <Chip 
                icon={getStatusIcon(request.statusCode, isError)}
                label={request.statusCode || 'ERR'} 
                size="small" 
                color={getStatusColor(request.statusCode, isError)}
              />
              
              {isExternal && (
                <Chip 
                  label="EXT" 
                  size="small" 
                  color="info" 
                  variant="outlined"
                  icon={<LanguageIcon />}
                />
              )}
            </Box>

            <Box display="flex" alignItems="center" gap={2}>
              <Chip
                icon={<AccessTimeIcon />}
                label={formatDuration(request.duration)}
                size="small"
                variant="outlined"
                color={request.duration > 2000 ? 'warning' : 'default'}
              />
              
              <Typography variant="caption" color="text.secondary">
                {getTimeAgo(request.timestamp)}
              </Typography>
              
              {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </Box>
          </Box>

          <Box mb={1}>
            <Typography 
              variant="body2" 
              noWrap
              sx={{ 
                fontFamily: 'monospace',
                bgcolor: 'grey.100',
                p: 0.5,
                borderRadius: 1
              }}
            >
              {request.url}
            </Typography>
          </Box>

          {domain !== 'localhost' && (
            <Chip
              label={domain}
              size="small"
              variant="outlined"
              color="secondary"
              sx={{ mb: 1 }}
            />
          )}

          <Collapse in={isExpanded}>
            <Divider sx={{ my: 2 }} />
            
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom color="primary">
                  📤 Request Details
                </Typography>
                <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <Typography variant="body2" component="pre" sx={{ 
                    whiteSpace: 'pre-wrap',
                    fontSize: '0.75rem',
                    maxHeight: 200,
                    overflow: 'auto'
                  }}>
                    {formatJson({
                      url: request.url,
                      method: request.method,
                      headers: request.headers || request.requestHeaders,
                      body: request.body || request.requestBody,
                      query: request.query,
                      params: request.params
                    })}
                  </Typography>
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom color="secondary">
                  📥 Response Details
                </Typography>
                <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <Typography variant="body2" component="pre" sx={{ 
                    whiteSpace: 'pre-wrap',
                    fontSize: '0.75rem',
                    maxHeight: 200,
                    overflow: 'auto'
                  }}>
                    {formatJson({
                      statusCode: request.statusCode,
                      statusText: request.statusText || request.statusMessage,
                      headers: request.responseHeaders,
                      body: request.responseBody
                    })}
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          </Collapse>
        </CardContent>
      </Card>
    );
  };

  // Enhanced Requests Tab
  const RequestsTab = () => (
    <Box>
      <FilterBar />
      
      {!isConnected && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          WebSocket connection lost. Real-time updates are disabled.
        </Alert>
      )}

      {isPaused && (
        <Alert severity="info" sx={{ mb: 2 }}>
          Monitoring is paused. New requests won't be displayed.
        </Alert>
      )}

      <Box sx={{ height: 'calc(100vh - 300px)', overflow: 'auto' }} ref={tableRef}>
        {Object.entries(groupedRequests).map(([groupName, groupRequests]) => (
          <Box key={groupName} mb={2}>
            {groupByDomain && groupName !== 'All Requests' && (
              <Typography variant="h6" gutterBottom sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: 1,
                mb: 2
              }}>
                <LanguageIcon />
                {groupName}
                <Chip label={groupRequests.length} size="small" />
              </Typography>
            )}
            
            {groupRequests.length === 0 ? (
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 4 }}>
                  <StorageIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    No requests found
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Try adjusting your filters or wait for new requests
                  </Typography>
                </CardContent>
              </Card>
            ) : (
              groupRequests.map((request) => (
                <RequestCard key={request.id} request={request} />
              ))
            )}
          </Box>
        ))}
      </Box>
    </Box>
  );

  // Enhanced Stats Tab
  const StatsTab = () => {
    const errorRequests = requests.filter(r => r.error || r.statusCode >= 400);
    const successRequests = requests.filter(r => r.statusCode >= 200 && r.statusCode < 300);
    const avgDuration = requests.reduce((sum, r) => sum + (r.duration || 0), 0) / (requests.length || 1);

    return (
      <Box>
        <Grid container spacing={3}>
          {/* Summary Cards */}
          <Grid item xs={6} md={3}>
            <Card sx={{ textAlign: 'center', bgcolor: 'primary.light', color: 'white' }}>
              <CardContent>
                <Typography variant="h4" fontWeight="bold">
                  {stats?.debugRequests?.total || requests.length}
                </Typography>
                <Typography variant="body2">
                  Total Requests
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} md={3}>
            <Card sx={{ textAlign: 'center', bgcolor: 'success.light', color: 'white' }}>
              <CardContent>
                <Typography variant="h4" fontWeight="bold">
                  {successRequests.length}
                </Typography>
                <Typography variant="body2">
                  Successful
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} md={3}>
            <Card sx={{ textAlign: 'center', bgcolor: 'error.light', color: 'white' }}>
              <CardContent>
                <Typography variant="h4" fontWeight="bold">
                  {errorRequests.length}
                </Typography>
                <Typography variant="body2">
                  Errors
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} md={3}>
            <Card sx={{ textAlign: 'center', bgcolor: 'warning.light', color: 'white' }}>
              <CardContent>
                <Typography variant="h4" fontWeight="bold">
                  {Math.round(avgDuration)}ms
                </Typography>
                <Typography variant="body2">
                  Avg Duration
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          {/* Domain Breakdown */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Top Domains
                </Typography>
                <List dense>
                  {Object.entries(
                    requests.reduce((acc, r) => {
                      const domain = extractDomain(r.url);
                      acc[domain] = (acc[domain] || 0) + 1;
                      return acc;
                    }, {})
                  )
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 5)
                    .map(([domain, count]) => (
                      <ListItem key={domain}>
                        <ListItemIcon>
                          <LanguageIcon />
                        </ListItemIcon>
                        <ListItemText 
                          primary={domain} 
                          secondary={`${count} requests`}
                        />
                        <Chip label={count} size="small" />
                      </ListItem>
                    ))
                  }
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Method Breakdown */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  HTTP Methods
                </Typography>
                <List dense>
                  {Object.entries(
                    requests.reduce((acc, r) => {
                      const method = r.method || 'UNKNOWN';
                      acc[method] = (acc[method] || 0) + 1;
                      return acc;
                    }, {})
                  )
                    .sort(([,a], [,b]) => b - a)
                    .map(([method, count]) => (
                      <ListItem key={method}>
                        <ListItemIcon>
                          <Avatar sx={{ 
                            width: 32, 
                            height: 32, 
                            bgcolor: getMethodColor(method) 
                          }}>
                            <Typography variant="caption" fontWeight="bold">
                              {method[0]}
                            </Typography>
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText 
                          primary={method} 
                          secondary={`${count} requests`}
                        />
                        <Chip label={count} size="small" />
                      </ListItem>
                    ))
                  }
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* WebSocket Info */}
          {stats?.websocket && (
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    WebSocket Status
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6} md={3}>
                      <Typography variant="body2" color="text.secondary">
                        Connected Clients
                      </Typography>
                      <Typography variant="h6">
                        {stats.websocket.connectedClients || 0}
                      </Typography>
                    </Grid>
                    <Grid item xs={6} md={3}>
                      <Typography variant="body2" color="text.secondary">
                        Total Subscriptions
                      </Typography>
                      <Typography variant="h6">
                        {stats.websocket.totalSubscriptions || 0}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Event Subscriptions
                      </Typography>
                      <Box display="flex" gap={1} flexWrap="wrap">
                        {Object.entries(stats.websocket.subscriptions || {}).map(([event, count]) => (
                          <Chip 
                            key={event} 
                            label={`${event}: ${count}`} 
                            size="small" 
                            variant="outlined"
                          />
                        ))}
                      </Box>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  };

  return (
    <>
      <Fab
        color="secondary"
        aria-label="debug"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          zIndex: 1000,
          animation: requests.length > 0 ? 'pulse 2s infinite' : 'none',
          '@keyframes pulse': {
            '0%': { transform: 'scale(1)' },
            '50%': { transform: 'scale(1.05)' },
            '100%': { transform: 'scale(1)' }
          }
        }}
        onClick={() => setOpen(true)}
      >
        <Badge 
          badgeContent={filteredRequests.length} 
          color={showOnlyErrors && filteredRequests.some(r => r.error || r.statusCode >= 400) ? "error" : "primary"}
          max={999}
          invisible={filteredRequests.length === 0}
        >
          <BugReportIcon />
        </Badge>
      </Fab>

      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="xl"
        fullWidth
        PaperProps={{
          sx: { 
            height: '90vh',
            bgcolor: 'grey.50'
          }
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: 'primary.dark', 
          color: 'white',
          pb: 1
        }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box display="flex" alignItems="center" gap={2}>
              <BugReportIcon />
              <Typography variant="h6" fontWeight="bold">
                🐛 Debug Console
              </Typography>
              <Chip 
                icon={isConnected ? <CheckCircleIcon /> : <ErrorIcon />}
                label={isConnected ? 'Live' : 'Disconnected'} 
                color={isConnected ? 'success' : 'error'} 
                size="small"
                variant={isConnected ? 'filled' : 'outlined'}
              />
              {isPaused && (
                <Chip 
                  icon={<PauseIcon />}
                  label="Paused" 
                  color="warning" 
                  size="small"
                />
              )}
            </Box>
            
            <Box display="flex" alignItems="center" gap={1}>
              <Tooltip title="Refresh stats">
                <IconButton onClick={handleRefreshStats} size="small" sx={{ color: 'white' }}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              
              <IconButton onClick={() => setOpen(false)} sx={{ color: 'white' }}>
                <CloseIcon />
              </IconButton>
            </Box>
          </Box>
          
          <Box mt={1}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange}
              textColor="inherit"
              indicatorColor="secondary"
              sx={{
                '& .MuiTab-root': {
                  color: 'rgba(255,255,255,0.7)',
                  '&.Mui-selected': {
                    color: 'white'
                  }
                }
              }}
            >
              <Tab 
                icon={<TimelineIcon />}
                iconPosition="start"
                label={`Requests (${filteredRequests.length})`} 
              />
              <Tab 
                icon={<StorageIcon />}
                iconPosition="start"
                label="Analytics" 
              />
            </Tabs>
          </Box>
        </DialogTitle>
        
        <DialogContent sx={{ p: 2, bgcolor: 'grey.50' }}>
          {tabValue === 0 && <RequestsTab />}
          {tabValue === 1 && <StatsTab />}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DebugWidget;