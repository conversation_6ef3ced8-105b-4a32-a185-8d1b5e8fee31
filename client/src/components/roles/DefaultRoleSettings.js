import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import axios from 'axios';

/**
 * Component for managing default role settings
 * This replaces the environment variables:
 * - DEFAULT_ROLE_LOCAL_USERS
 * - DEFAULT_ROLE_GOOGLE_USERS
 * - GOOGLE_GROUPS_ROLE_MAPPING
 */
const DefaultRoleSettings = ({ roles }) => {
  const [settings, setSettings] = useState({
    defaultRoleLocalUsers: 'user',
    defaultRoleGoogleUsers: 'user',
    googleGroupsRoleMapping: []
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  // Dialog state for adding Google Group mapping
  const [openDialog, setOpenDialog] = useState(false);
  const [newMapping, setNewMapping] = useState({
    groupEmail: '',
    roleName: ''
  });
  
  // Fetch current settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);
  
  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/roles/settings');
      setSettings(response.data);
    } catch (err) {
      setError('Failed to fetch role settings');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  
  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      await axios.put('/api/roles/settings', settings);
      setSuccess('Settings saved successfully');
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (err) {
      setError(err.response?.data?.msg || 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };
  
  const handleChange = (e) => {
    setSettings({
      ...settings,
      [e.target.name]: e.target.value
    });
  };
  
  const handleOpenDialog = () => {
    setNewMapping({
      groupEmail: '',
      roleName: ''
    });
    setOpenDialog(true);
  };
  
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };
  
  const handleAddMapping = () => {
    // Validate inputs
    if (!newMapping.groupEmail || !newMapping.roleName) {
      setError('Group email and role are required');
      return;
    }
    
    // Check if mapping already exists
    const exists = settings.googleGroupsRoleMapping.some(
      mapping => mapping.groupEmail.toLowerCase() === newMapping.groupEmail.toLowerCase()
    );
    
    if (exists) {
      setError('A mapping for this group already exists');
      return;
    }
    
    // Add new mapping
    setSettings({
      ...settings,
      googleGroupsRoleMapping: [
        ...settings.googleGroupsRoleMapping,
        newMapping
      ]
    });
    
    // Close dialog
    handleCloseDialog();
  };
  
  const handleDeleteMapping = (index) => {
    const newMappings = [...settings.googleGroupsRoleMapping];
    newMappings.splice(index, 1);
    
    setSettings({
      ...settings,
      googleGroupsRoleMapping: newMappings
    });
  };
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Default Role Settings
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSaveSettings}
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Settings'}
        </Button>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}
      
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Default Roles for New Users
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel id="default-local-role-label">Default Role for Local Users</InputLabel>
            <Select
              labelId="default-local-role-label"
              id="default-local-role"
              name="defaultRoleLocalUsers"
              value={settings.defaultRoleLocalUsers}
              onChange={handleChange}
              label="Default Role for Local Users"
            >
              {roles.map(role => (
                <MenuItem key={role._id} value={role.name}>
                  {role.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel id="default-google-role-label">Default Role for Google Users</InputLabel>
            <Select
              labelId="default-google-role-label"
              id="default-google-role"
              name="defaultRoleGoogleUsers"
              value={settings.defaultRoleGoogleUsers}
              onChange={handleChange}
              label="Default Role for Google Users"
            >
              {roles.map(role => (
                <MenuItem key={role._id} value={role.name}>
                  {role.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>
      
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1">
            Google Groups Role Mapping
          </Typography>
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={handleOpenDialog}
          >
            Add Mapping
          </Button>
        </Box>
        
        <TableContainer component={Paper} variant="outlined">
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Google Group Email</TableCell>
                <TableCell>Role</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {settings.googleGroupsRoleMapping.length > 0 ? (
                settings.googleGroupsRoleMapping.map((mapping, index) => (
                  <TableRow key={index}>
                    <TableCell>{mapping.groupEmail}</TableCell>
                    <TableCell>{mapping.roleName}</TableCell>
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDeleteMapping(index)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={3} align="center">
                    No mappings configured
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Users who are members of these Google Groups will be assigned the corresponding role when they sign in.
          If a user is a member of multiple groups, they will receive all the corresponding roles.
        </Typography>
      </Box>
      
      {/* Dialog for adding new Google Group mapping */}
      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>Add Google Group Mapping</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            id="groupEmail"
            label="Google Group Email"
            type="email"
            fullWidth
            variant="outlined"
            value={newMapping.groupEmail}
            onChange={(e) => setNewMapping({ ...newMapping, groupEmail: e.target.value })}
            sx={{ mb: 2, mt: 1 }}
          />
          
          <FormControl fullWidth>
            <InputLabel id="role-select-label">Role</InputLabel>
            <Select
              labelId="role-select-label"
              id="role-select"
              value={newMapping.roleName}
              label="Role"
              onChange={(e) => setNewMapping({ ...newMapping, roleName: e.target.value })}
            >
              {roles.map(role => (
                <MenuItem key={role._id} value={role.name}>
                  {role.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleAddMapping} variant="contained">Add</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default DefaultRoleSettings;