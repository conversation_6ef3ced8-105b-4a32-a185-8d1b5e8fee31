import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Switch,
  FormControlLabel,
  Divider,
  Card,
  CardContent,
  CardHeader,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Chip
} from '@mui/material';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import SettingsIcon from '@mui/icons-material/Settings';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import ViewListIcon from '@mui/icons-material/ViewList';
import DashboardIcon from '@mui/icons-material/Dashboard';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

/**
 * DashboardLayoutManager component
 * Allows users to customize the layout of the access control dashboard
 */
const DashboardLayoutManager = ({ onLayoutChange }) => {
  // Available widgets for the dashboard
  const availableWidgets = [
    { id: 'recent-events', name: 'Recent Events', description: 'Shows recent access control events', defaultSize: 'large', defaultPosition: 0 },
    { id: 'door-status', name: 'Door Status', description: 'Shows status of all doors', defaultSize: 'medium', defaultPosition: 1 },
    { id: 'user-stats', name: 'User Statistics', description: 'Shows user statistics', defaultSize: 'small', defaultPosition: 2 },
    { id: 'access-level-summary', name: 'Access Level Summary', description: 'Shows summary of access levels', defaultSize: 'small', defaultPosition: 3 },
    { id: 'system-status', name: 'System Status', description: 'Shows status of access control systems', defaultSize: 'medium', defaultPosition: 4 },
    { id: 'schedule-overview', name: 'Schedule Overview', description: 'Shows overview of active schedules', defaultSize: 'medium', defaultPosition: 5 },
    { id: 'quick-actions', name: 'Quick Actions', description: 'Provides quick access to common actions', defaultSize: 'small', defaultPosition: 6 },
    { id: 'alerts', name: 'Alerts', description: 'Shows important alerts and notifications', defaultSize: 'medium', defaultPosition: 7 }
  ];

  // State for layout configuration
  const [layoutConfig, setLayoutConfig] = useState({
    widgets: [],
    layout: 'grid', // 'grid' or 'list'
    columns: 3,
    savedLayouts: []
  });

  // State for dialogs
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [saveLayoutDialogOpen, setSaveLayoutDialogOpen] = useState(false);
  const [loadLayoutDialogOpen, setLoadLayoutDialogOpen] = useState(false);
  const [newLayoutName, setNewLayoutName] = useState('');

  // Initialize layout with default widgets
  useEffect(() => {
    // Try to load saved layout from localStorage
    const savedConfig = localStorage.getItem('accessControlDashboardLayout');
    
    if (savedConfig) {
      try {
        const parsedConfig = JSON.parse(savedConfig);
        setLayoutConfig(parsedConfig);
      } catch (error) {
        console.error('Error parsing saved layout:', error);
        initializeDefaultLayout();
      }
    } else {
      initializeDefaultLayout();
    }
  }, []);

  // Initialize default layout
  const initializeDefaultLayout = () => {
    const defaultWidgets = availableWidgets.map(widget => ({
      id: widget.id,
      name: widget.name,
      size: widget.defaultSize,
      position: widget.defaultPosition,
      visible: true
    }));
    
    // Sort by default position
    defaultWidgets.sort((a, b) => a.position - b.position);
    
    setLayoutConfig({
      widgets: defaultWidgets,
      layout: 'grid',
      columns: 3,
      savedLayouts: []
    });
  };

  // Save layout to localStorage
  useEffect(() => {
    if (layoutConfig.widgets.length > 0) {
      localStorage.setItem('accessControlDashboardLayout', JSON.stringify(layoutConfig));
      
      // Notify parent component if callback provided
      if (onLayoutChange) {
        onLayoutChange(layoutConfig);
      }
    }
  }, [layoutConfig, onLayoutChange]);

  // Handle drag end event
  const handleDragEnd = (result) => {
    if (!result.destination) return;
    
    const items = Array.from(layoutConfig.widgets);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    // Update positions
    const updatedItems = items.map((item, index) => ({
      ...item,
      position: index
    }));
    
    setLayoutConfig({
      ...layoutConfig,
      widgets: updatedItems
    });
  };

  // Toggle widget visibility
  const toggleWidgetVisibility = (widgetId) => {
    setLayoutConfig({
      ...layoutConfig,
      widgets: layoutConfig.widgets.map(widget => 
        widget.id === widgetId 
          ? { ...widget, visible: !widget.visible } 
          : widget
      )
    });
  };

  // Change widget size
  const changeWidgetSize = (widgetId, newSize) => {
    setLayoutConfig({
      ...layoutConfig,
      widgets: layoutConfig.widgets.map(widget => 
        widget.id === widgetId 
          ? { ...widget, size: newSize } 
          : widget
      )
    });
  };

  // Change layout type
  const changeLayoutType = (newLayout) => {
    setLayoutConfig({
      ...layoutConfig,
      layout: newLayout
    });
  };

  // Change number of columns
  const changeColumns = (event) => {
    setLayoutConfig({
      ...layoutConfig,
      columns: event.target.value
    });
  };

  // Save current layout
  const saveCurrentLayout = () => {
    if (!newLayoutName.trim()) return;
    
    const newSavedLayout = {
      name: newLayoutName,
      timestamp: new Date().toISOString(),
      config: {
        widgets: [...layoutConfig.widgets],
        layout: layoutConfig.layout,
        columns: layoutConfig.columns
      }
    };
    
    setLayoutConfig({
      ...layoutConfig,
      savedLayouts: [...layoutConfig.savedLayouts, newSavedLayout]
    });
    
    setNewLayoutName('');
    setSaveLayoutDialogOpen(false);
  };

  // Load saved layout
  const loadSavedLayout = (savedLayout) => {
    setLayoutConfig({
      ...layoutConfig,
      widgets: [...savedLayout.config.widgets],
      layout: savedLayout.config.layout,
      columns: savedLayout.config.columns
    });
    
    setLoadLayoutDialogOpen(false);
  };

  // Delete saved layout
  const deleteSavedLayout = (layoutIndex) => {
    const updatedLayouts = [...layoutConfig.savedLayouts];
    updatedLayouts.splice(layoutIndex, 1);
    
    setLayoutConfig({
      ...layoutConfig,
      savedLayouts: updatedLayouts
    });
  };

  // Reset to default layout
  const resetToDefault = () => {
    initializeDefaultLayout();
    setConfigDialogOpen(false);
  };

  return (
    <>
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DashboardIcon sx={{ mr: 1 }} />
            <Typography variant="h6">Dashboard Layout</Typography>
          </Box>
          
          <Box>
            <Tooltip title="Grid View">
              <IconButton 
                color={layoutConfig.layout === 'grid' ? 'primary' : 'default'}
                onClick={() => changeLayoutType('grid')}
              >
                <ViewModuleIcon />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="List View">
              <IconButton 
                color={layoutConfig.layout === 'list' ? 'primary' : 'default'}
                onClick={() => changeLayoutType('list')}
              >
                <ViewListIcon />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Configure Layout">
              <IconButton onClick={() => setConfigDialogOpen(true)}>
                <SettingsIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        
        <Typography variant="body2" color="text.secondary" paragraph>
          Drag and drop widgets to rearrange them. Click the settings icon to configure visibility and size.
        </Typography>
        
        <Box sx={{ mt: 2 }}>
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="widgets">
              {(provided) => (
                <List
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  sx={{ 
                    bgcolor: 'background.paper',
                    border: '1px dashed',
                    borderColor: 'divider',
                    borderRadius: 1,
                    p: 1
                  }}
                >
                  {layoutConfig.widgets
                    .filter(widget => widget.visible)
                    .sort((a, b) => a.position - b.position)
                    .map((widget, index) => (
                      <Draggable key={widget.id} draggableId={widget.id} index={index}>
                        {(provided) => (
                          <ListItem
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            sx={{ 
                              bgcolor: 'background.default',
                              mb: 1,
                              borderRadius: 1,
                              border: '1px solid',
                              borderColor: 'divider'
                            }}
                          >
                            <ListItemIcon>
                              <DragIndicatorIcon />
                            </ListItemIcon>
                            
                            <ListItemText 
                              primary={widget.name} 
                              secondary={`Size: ${widget.size}`}
                            />
                            
                            <ListItemSecondaryAction>
                              <Tooltip title="Change Size">
                                <IconButton 
                                  edge="end" 
                                  onClick={() => {
                                    const sizes = ['small', 'medium', 'large'];
                                    const currentIndex = sizes.indexOf(widget.size);
                                    const nextSize = sizes[(currentIndex + 1) % sizes.length];
                                    changeWidgetSize(widget.id, nextSize);
                                  }}
                                  size="small"
                                >
                                  <Chip 
                                    label={widget.size} 
                                    size="small" 
                                    color="primary"
                                  />
                                </IconButton>
                              </Tooltip>
                              
                              <Tooltip title="Hide Widget">
                                <IconButton 
                                  edge="end" 
                                  onClick={() => toggleWidgetVisibility(widget.id)}
                                  size="small"
                                >
                                  <VisibilityOffIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </ListItemSecondaryAction>
                          </ListItem>
                        )}
                      </Draggable>
                    ))}
                  {provided.placeholder}
                </List>
              )}
            </Droppable>
          </DragDropContext>
        </Box>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
          <Button 
            variant="outlined" 
            startIcon={<SaveIcon />}
            onClick={() => setSaveLayoutDialogOpen(true)}
          >
            Save Layout
          </Button>
          
          <Button 
            variant="outlined" 
            startIcon={<ViewModuleIcon />}
            onClick={() => setLoadLayoutDialogOpen(true)}
          >
            Load Layout
          </Button>
        </Box>
      </Paper>
      
      {/* Configure Layout Dialog */}
      <Dialog
        open={configDialogOpen}
        onClose={() => setConfigDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Configure Dashboard Layout</DialogTitle>
        
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Layout Settings
            </Typography>
            
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Layout Type</InputLabel>
                  <Select
                    value={layoutConfig.layout}
                    onChange={(e) => changeLayoutType(e.target.value)}
                    label="Layout Type"
                  >
                    <MenuItem value="grid">Grid Layout</MenuItem>
                    <MenuItem value="list">List Layout</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Columns</InputLabel>
                  <Select
                    value={layoutConfig.columns}
                    onChange={changeColumns}
                    label="Columns"
                    disabled={layoutConfig.layout !== 'grid'}
                  >
                    <MenuItem value={1}>1 Column</MenuItem>
                    <MenuItem value={2}>2 Columns</MenuItem>
                    <MenuItem value={3}>3 Columns</MenuItem>
                    <MenuItem value={4}>4 Columns</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
          
          <Divider sx={{ my: 3 }} />
          
          <Typography variant="subtitle1" gutterBottom>
            Available Widgets
          </Typography>
          
          <List>
            {availableWidgets.map((widget) => {
              const isVisible = layoutConfig.widgets.find(w => w.id === widget.id)?.visible || false;
              
              return (
                <ListItem key={widget.id}>
                  <ListItemText 
                    primary={widget.name} 
                    secondary={widget.description}
                  />
                  
                  <ListItemSecondaryAction>
                    <Tooltip title={isVisible ? "Hide Widget" : "Show Widget"}>
                      <IconButton 
                        edge="end" 
                        onClick={() => toggleWidgetVisibility(widget.id)}
                      >
                        {isVisible ? <VisibilityIcon /> : <VisibilityOffIcon />}
                      </IconButton>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
              );
            })}
          </List>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setConfigDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={resetToDefault} color="warning">
            Reset to Default
          </Button>
          <Button onClick={() => setConfigDialogOpen(false)} variant="contained">
            Apply
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Save Layout Dialog */}
      <Dialog
        open={saveLayoutDialogOpen}
        onClose={() => setSaveLayoutDialogOpen(false)}
      >
        <DialogTitle>Save Current Layout</DialogTitle>
        
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Layout Name"
            fullWidth
            value={newLayoutName}
            onChange={(e) => setNewLayoutName(e.target.value)}
          />
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setSaveLayoutDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={saveCurrentLayout} variant="contained" disabled={!newLayoutName.trim()}>
            Save
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Load Layout Dialog */}
      <Dialog
        open={loadLayoutDialogOpen}
        onClose={() => setLoadLayoutDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Load Saved Layout</DialogTitle>
        
        <DialogContent>
          {layoutConfig.savedLayouts.length === 0 ? (
            <Typography variant="body1" sx={{ py: 2 }}>
              No saved layouts found.
            </Typography>
          ) : (
            <List>
              {layoutConfig.savedLayouts.map((layout, index) => (
                <ListItem key={index}>
                  <ListItemText 
                    primary={layout.name} 
                    secondary={new Date(layout.timestamp).toLocaleString()}
                  />
                  
                  <ListItemSecondaryAction>
                    <Tooltip title="Load Layout">
                      <IconButton 
                        edge="end" 
                        onClick={() => loadSavedLayout(layout)}
                      >
                        <ViewModuleIcon />
                      </IconButton>
                    </Tooltip>
                    
                    <Tooltip title="Delete Layout">
                      <IconButton 
                        edge="end" 
                        onClick={() => deleteSavedLayout(index)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setLoadLayoutDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DashboardLayoutManager;