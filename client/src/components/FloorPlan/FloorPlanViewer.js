import React, { useState, useEffect, useRef } from 'react';
import { 
  Box, 
  Typography, 
  CircularProgress, 
  Paper, 
  IconButton, 
  Tooltip, 
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Badge,
  Menu,
  ClickAwayListener
} from '@mui/material';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import RefreshIcon from '@mui/icons-material/Refresh';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import ThermostatIcon from '@mui/icons-material/Thermostat';
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import VideocamIcon from '@mui/icons-material/Videocam';
import RouterIcon from '@mui/icons-material/Router';
import WifiIcon from '@mui/icons-material/Wifi';
import AcUnitIcon from '@mui/icons-material/AcUnit';
import SecurityIcon from '@mui/icons-material/Security';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import SensorsIcon from '@mui/icons-material/Sensors';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import MeetingRoomOutlinedIcon from '@mui/icons-material/MeetingRoomOutlined';
import buildingManagementService from '../../services/buildingManagementService';
import websocketService from '../../services/websocketService';

/**
 * FloorPlanViewer component
 * Displays a floor plan with interactive icons that show device status
 */
const FloorPlanViewer = ({ 
  editMode = false, 
  onSave = null, 
  onCancel = null,
  refreshInterval = 30000 // 30 seconds default refresh interval
}) => {
  const [buildings, setBuildings] = useState([]);
  const [floors, setFloors] = useState([]);
  const [selectedBuilding, setSelectedBuilding] = useState('');
  const [selectedFloor, setSelectedFloor] = useState('');
  const [floorPlanUrl, setFloorPlanUrl] = useState('');
  const [icons, setIcons] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [zoom, setZoom] = useState(1);
  const [selectedIcon, setSelectedIcon] = useState(null);
  const [iconDetailsOpen, setIconDetailsOpen] = useState(false);
  const [doors, setDoors] = useState([]);
  const [climateDevices, setClimateDevices] = useState([]);
  const [cameras, setCameras] = useState([]);
  const [networkDevices, setNetworkDevices] = useState([]);
  const [isEditing, setIsEditing] = useState(editMode);
  const [newIcon, setNewIcon] = useState(null);
  const [iconToAdd, setIconToAdd] = useState(null);
  const [integrationDialogOpen, setIntegrationDialogOpen] = useState(false);
  const [availableDevices, setAvailableDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState('');
  const [doorStatusUpdates, setDoorStatusUpdates] = useState(0);
  const [contextMenu, setContextMenu] = useState(null);
  const [contextMenuIcon, setContextMenuIcon] = useState(null);
  
  const containerRef = useRef(null);
  const floorPlanRef = useRef(null);
  const refreshTimerRef = useRef(null);

  // Handle door status updates from WebSocket
  const handleDoorStatusUpdate = (doorStatuses) => {
    if (!doorStatuses || !Array.isArray(doorStatuses) || doorStatuses.length === 0) return;
    
    console.log('Received door status update:', doorStatuses);
    
    // Increment the update counter to trigger a re-render
    setDoorStatusUpdates(prev => prev + 1);
    
    // Update the icons with the new door status
    setIcons(prevIcons => {
      // Create a copy of the icons array
      const updatedIcons = [...prevIcons];
      
      // Update door icons with matching deviceId
      doorStatuses.forEach(doorStatus => {
        const doorIndex = updatedIcons.findIndex(icon => 
          icon.type === 'door' && 
          icon.deviceId === doorStatus.doorId
        );
        
        if (doorIndex !== -1) {
          // Update the icon data with the new status
          updatedIcons[doorIndex] = {
            ...updatedIcons[doorIndex],
            data: {
              value: doorStatus.status || doorStatus.state || 'unknown',
              lastUpdated: new Date().toISOString()
            },
            status: getDoorStatusCategory(doorStatus)
          };
        }
      });
      
      return updatedIcons;
    });
  };
  
  // Determine door status category (normal, warning, alert, inactive)
  const getDoorStatusCategory = (doorStatus) => {
    const status = doorStatus.status || doorStatus.state || '';
    
    if (status.toLowerCase().includes('alarm') || status.toLowerCase().includes('error')) {
      return 'alert';
    } else if (status.toLowerCase().includes('unlocked') || status.toLowerCase().includes('open')) {
      return 'warning';
    } else if (status.toLowerCase().includes('locked') || status.toLowerCase().includes('closed')) {
      return 'normal';
    } else {
      return 'inactive';
    }
  };

  // Set up WebSocket connection for real-time door status updates
  useEffect(() => {
    // Connect to WebSocket server
    websocketService.connect().then(() => {
      console.log('WebSocket connected for door status updates');
      
      // Subscribe to door status events
      websocketService.subscribe('door-status');
      
      // Add event listener for door status updates
      websocketService.addEventListener('door-status', handleDoorStatusUpdate);
    }).catch(error => {
      console.warn('WebSocket connection failed, falling back to polling:', error);
    });
    
    // Cleanup function
    return () => {
      // Remove event listener
      websocketService.removeEventListener('door-status', handleDoorStatusUpdate);
      
      // Clear refresh timer
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, []);
  
  // Load buildings on component mount
  useEffect(() => {
    const fetchBuildings = async () => {
      try {
        setLoading(true);
        const data = await buildingManagementService.getBuildings();
        setBuildings(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching buildings:', err);
        setError('Failed to load buildings. Please try again later.');
        setLoading(false);
      }
    };

    fetchBuildings();

    // Cleanup function
    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, []);

  // Load floors when a building is selected
  useEffect(() => {
    const fetchFloors = async () => {
      if (!selectedBuilding) {
        setFloors([]);
        setSelectedFloor('');
        return;
      }

      try {
        setLoading(true);
        const data = await buildingManagementService.getBuildingFloors(selectedBuilding);
        setFloors(data);
        setLoading(false);
        
        // Auto-select the first floor if available
        if (data.length > 0 && !selectedFloor) {
          setSelectedFloor(data[0]._id);
        } else {
          setSelectedFloor('');
        }
      } catch (err) {
        console.error('Error fetching floors:', err);
        setError('Failed to load floors. Please try again later.');
        setLoading(false);
      }
    };

    fetchFloors();
  }, [selectedBuilding]);

  // Load floor plan and icons when a floor is selected
  useEffect(() => {
    const fetchFloorPlanAndIcons = async () => {
      if (!selectedFloor) {
        setFloorPlanUrl('');
        setIcons([]);
        return;
      }

      try {
        setLoading(true);
        
        // Get floor plan URL
        const floorPlanUrl = buildingManagementService.getFloorplanUrl(selectedFloor);
        setFloorPlanUrl(floorPlanUrl);
        
        // Get floor icons
        const iconsData = await buildingManagementService.getFloorIcons(selectedFloor);
        setIcons(iconsData);
        
        setLoading(false);
        
        // Start the refresh timer
        startRefreshTimer();
      } catch (err) {
        console.error('Error fetching floor plan and icons:', err);
        setError('Failed to load floor plan. Please try again later.');
        setLoading(false);
      }
    };

    fetchFloorPlanAndIcons();
    
    // Fetch device data for integration selection
    const fetchDeviceData = async () => {
      try {
        const [doorsData, climateData, camerasData, networkData] = await Promise.all([
          buildingManagementService.getDoors(),
          buildingManagementService.getClimateDevices(),
          buildingManagementService.getCameras(),
          buildingManagementService.getNetworkDevices()
        ]);
        
        setDoors(doorsData);
        setClimateDevices(climateData);
        setCameras(camerasData);
        setNetworkDevices(networkData);
      } catch (err) {
        console.error('Error fetching device data:', err);
      }
    };
    
    fetchDeviceData();
    
    // Cleanup function
    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, [selectedFloor]);

  // Start the refresh timer
  const startRefreshTimer = () => {
    // Clear any existing timer
    if (refreshTimerRef.current) {
      clearInterval(refreshTimerRef.current);
    }
    
    // Set up a new timer
    refreshTimerRef.current = setInterval(() => {
      refreshIconData();
    }, refreshInterval);
  };

  // Refresh icon data
  const refreshIconData = async () => {
    if (!selectedFloor) return;
    
    try {
      const iconsData = await buildingManagementService.getFloorIcons(selectedFloor);
      setIcons(iconsData);
    } catch (err) {
      console.error('Error refreshing icon data:', err);
    }
  };

  // Handle manual refresh
  const handleRefresh = () => {
    refreshIconData();
    
    // Reset the refresh timer
    startRefreshTimer();
  };

  // Handle building selection
  const handleBuildingChange = (event) => {
    setSelectedBuilding(event.target.value);
  };

  // Handle floor selection
  const handleFloorChange = (event) => {
    setSelectedFloor(event.target.value);
  };

  // Handle zoom in
  const handleZoomIn = () => {
    setZoom(prevZoom => Math.min(prevZoom + 0.1, 2));
  };

  // Handle zoom out
  const handleZoomOut = () => {
    setZoom(prevZoom => Math.max(prevZoom - 0.1, 0.5));
  };

  // Handle icon click
  const handleIconClick = (icon, event) => {
    // Close any open context menu
    setContextMenu(null);
    
    if (isEditing) {
      // In edit mode, select the icon for editing
      setSelectedIcon(icon);
    } else {
      // In view mode
      if (event && event.type === 'contextmenu' && icon.type === 'door') {
        // Prevent the default context menu
        event.preventDefault();
        
        // Show our custom context menu for doors
        setContextMenuIcon(icon);
        setContextMenu(
          event.currentTarget ? {
            mouseX: event.clientX,
            mouseY: event.clientY,
          } : null,
        );
      } else {
        // Regular click - show icon details
        setSelectedIcon(icon);
        setIconDetailsOpen(true);
      }
    }
  };
  
  // Handle context menu close
  const handleContextMenuClose = () => {
    setContextMenu(null);
  };

  // Handle floor plan click in edit mode
  const handleFloorPlanClick = (event) => {
    if (!isEditing || !floorPlanRef.current || iconToAdd === null) return;
    
    // Get click coordinates relative to the floor plan
    const rect = floorPlanRef.current.getBoundingClientRect();
    const x = (event.clientX - rect.left) / zoom;
    const y = (event.clientY - rect.top) / zoom;
    
    // Create a new icon at the clicked position
    const newIconData = {
      type: iconToAdd,
      position: { x, y },
      name: `New ${iconToAdd}`,
      floorId: selectedFloor
    };
    
    setNewIcon(newIconData);
    
    // Show integration selection dialog
    setIntegrationDialogOpen(true);
    
    // Set available devices based on icon type
    switch (iconToAdd) {
      case 'door':
        setAvailableDevices(doors);
        break;
      case 'temperature':
      case 'hvac':
        setAvailableDevices(climateDevices);
        break;
      case 'camera':
        setAvailableDevices(cameras);
        break;
      case 'network':
        setAvailableDevices(networkDevices);
        break;
      default:
        setAvailableDevices([]);
    }
  };

  // Handle adding a new icon
  const handleAddIcon = async () => {
    if (!newIcon) return;
    
    try {
      // Add device ID and integration source to the new icon
      const iconData = {
        ...newIcon,
        deviceId: selectedDevice,
        integrationSource: getIntegrationSource(newIcon.type, selectedDevice)
      };
      
      // Create the icon
      const createdIcon = await buildingManagementService.createFloorplanIcon(iconData);
      
      // Add the new icon to the list
      setIcons(prevIcons => [...prevIcons, createdIcon]);
      
      // Reset state
      setNewIcon(null);
      setIconToAdd(null);
      setSelectedDevice('');
      setIntegrationDialogOpen(false);
    } catch (err) {
      console.error('Error creating icon:', err);
      setError('Failed to create icon. Please try again.');
    }
  };

  // Get integration source based on icon type and device ID
  const getIntegrationSource = (type, deviceId) => {
    // This is a simplified implementation
    // In a real application, you would determine the integration source based on the device ID
    switch (type) {
      case 'door':
        return 'lenelS2NetBox';
      case 'temperature':
      case 'hvac':
        return 'dreo';
      case 'camera':
        return 'unifiProtect';
      case 'network':
        return 'unifiNetwork';
      default:
        return '';
    }
  };

  // Handle icon deletion
  const handleDeleteIcon = async () => {
    if (!selectedIcon) return;
    
    try {
      await buildingManagementService.deleteFloorplanIcon(selectedIcon._id);
      
      // Remove the icon from the list
      setIcons(prevIcons => prevIcons.filter(icon => icon._id !== selectedIcon._id));
      
      // Reset state
      setSelectedIcon(null);
      setIconDetailsOpen(false);
    } catch (err) {
      console.error('Error deleting icon:', err);
      setError('Failed to delete icon. Please try again.');
    }
  };

  // Handle door unlock
  const handleDoorUnlock = async (doorIcon = null) => {
    // Use the provided doorIcon, or contextMenuIcon, or selectedIcon
    const icon = doorIcon || contextMenuIcon || selectedIcon;
    
    if (!icon || icon.type !== 'door') return;
    
    try {
      // Close context menu if it was used
      if (contextMenu) {
        handleContextMenuClose();
      }
      
      // Call the API to unlock the door
      const response = await buildingManagementService.unlockDoor(
        icon.deviceId,
        icon.integrationSource
      );
      
      // Update the icon data with the new status
      const updatedData = {
        ...icon.data,
        value: 'unlocked',
        lastUpdated: new Date().toISOString()
      };
      
      await buildingManagementService.updateFloorplanIconData(icon._id, updatedData);
      
      // Update the icon in the list
      setIcons(prevIcons => prevIcons.map(i => 
        i._id === icon._id 
          ? { ...i, data: updatedData, status: 'warning' } 
          : i
      ));
      
      // Update the selected icon if it's the same as the one we just updated
      if (selectedIcon && selectedIcon._id === icon._id) {
        setSelectedIcon({ 
          ...selectedIcon, 
          data: updatedData,
          status: 'warning'
        });
      }
      
      console.log('Door unlocked successfully:', response);
    } catch (err) {
      console.error('Error unlocking door:', err);
      setError('Failed to unlock door. Please try again.');
    }
  };
  
  // Handle door lock
  const handleDoorLock = async (doorIcon = null) => {
    // Use the provided doorIcon, or contextMenuIcon, or selectedIcon
    const icon = doorIcon || contextMenuIcon || selectedIcon;
    
    if (!icon || icon.type !== 'door') return;
    
    try {
      // Close context menu if it was used
      if (contextMenu) {
        handleContextMenuClose();
      }
      
      // Call the API to lock the door
      const response = await buildingManagementService.lockDoor(
        icon.deviceId,
        icon.integrationSource
      );
      
      // Update the icon data with the new status
      const updatedData = {
        ...icon.data,
        value: 'locked',
        lastUpdated: new Date().toISOString()
      };
      
      await buildingManagementService.updateFloorplanIconData(icon._id, updatedData);
      
      // Update the icon in the list
      setIcons(prevIcons => prevIcons.map(i => 
        i._id === icon._id 
          ? { ...i, data: updatedData, status: 'normal' } 
          : i
      ));
      
      // Update the selected icon if it's the same as the one we just updated
      if (selectedIcon && selectedIcon._id === icon._id) {
        setSelectedIcon({ 
          ...selectedIcon, 
          data: updatedData,
          status: 'normal'
        });
      }
      
      console.log('Door locked successfully:', response);
    } catch (err) {
      console.error('Error locking door:', err);
      setError('Failed to lock door. Please try again.');
    }
  };
  
  // Handle door passage mode
  const handleDoorPassageMode = async (doorIcon = null) => {
    // Use the provided doorIcon, or contextMenuIcon, or selectedIcon
    const icon = doorIcon || contextMenuIcon || selectedIcon;
    
    if (!icon || icon.type !== 'door') return;
    
    try {
      // Close context menu if it was used
      if (contextMenu) {
        handleContextMenuClose();
      }
      
      // Call the API to set the door to passage mode
      const response = await buildingManagementService.setDoorPassageMode(
        icon.deviceId,
        icon.integrationSource
      );
      
      // Update the icon data with the new status
      const updatedData = {
        ...icon.data,
        value: 'passage',
        lastUpdated: new Date().toISOString()
      };
      
      await buildingManagementService.updateFloorplanIconData(icon._id, updatedData);
      
      // Update the icon in the list
      setIcons(prevIcons => prevIcons.map(i => 
        i._id === icon._id 
          ? { ...i, data: updatedData, status: 'warning' } 
          : i
      ));
      
      // Update the selected icon if it's the same as the one we just updated
      if (selectedIcon && selectedIcon._id === icon._id) {
        setSelectedIcon({ 
          ...selectedIcon, 
          data: updatedData,
          status: 'warning'
        });
      }
      
      console.log('Door set to passage mode successfully:', response);
    } catch (err) {
      console.error('Error setting door to passage mode:', err);
      setError('Failed to set door to passage mode. Please try again.');
    }
  };

  // Get icon component based on type
  const getIconComponent = (type) => {
    switch (type) {
      case 'temperature':
        return <ThermostatIcon />;
      case 'door':
        return <MeetingRoomIcon />;
      case 'camera':
        return <VideocamIcon />;
      case 'network':
        return <RouterIcon />;
      case 'wifi':
        return <WifiIcon />;
      case 'hvac':
        return <AcUnitIcon />;
      case 'security':
        return <SecurityIcon />;
      case 'light':
        return <LightbulbIcon />;
      case 'motion':
        return <SensorsIcon />;
      default:
        return <SensorsIcon />;
    }
  };

  // Get icon color based on status
  const getIconColor = (icon) => {
    if (!icon.data || !icon.data.value) return 'inherit';
    
    switch (icon.status) {
      case 'alert':
        return 'error.main';
      case 'warning':
        return 'warning.main';
      case 'normal':
        return 'success.main';
      case 'inactive':
        return 'text.disabled';
      default:
        return 'inherit';
    }
  };

  // Get icon tooltip text
  const getIconTooltip = (icon) => {
    let tooltipText = icon.name;
    
    if (icon.data && icon.data.value) {
      tooltipText += `: ${icon.data.value}`;
      if (icon.data.unit) {
        tooltipText += ` ${icon.data.unit}`;
      }
    }
    
    return tooltipText;
  };

  // Render icon details dialog
  const renderIconDetailsDialog = () => {
    if (!selectedIcon) return null;
    
    return (
      <Dialog open={iconDetailsOpen} onClose={() => setIconDetailsOpen(false)}>
        <DialogTitle>{selectedIcon.name}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle1">Type: {selectedIcon.type}</Typography>
            </Grid>
            {selectedIcon.deviceId && (
              <Grid item xs={12}>
                <Typography variant="subtitle1">Device ID: {selectedIcon.deviceId}</Typography>
              </Grid>
            )}
            {selectedIcon.integrationSource && (
              <Grid item xs={12}>
                <Typography variant="subtitle1">Integration: {selectedIcon.integrationSource}</Typography>
              </Grid>
            )}
            {selectedIcon.data && selectedIcon.data.value && (
              <Grid item xs={12}>
                <Typography variant="subtitle1">
                  Value: {selectedIcon.data.value} {selectedIcon.data.unit || ''}
                </Typography>
              </Grid>
            )}
            {selectedIcon.data && selectedIcon.data.lastUpdated && (
              <Grid item xs={12}>
                <Typography variant="subtitle1">
                  Last Updated: {new Date(selectedIcon.data.lastUpdated).toLocaleString()}
                </Typography>
              </Grid>
            )}
            {selectedIcon.type === 'door' && (
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Button 
                    variant="contained" 
                    color="primary" 
                    onClick={handleDoorUnlock}
                    disabled={selectedIcon.data && selectedIcon.data.value === 'unlocked'}
                    startIcon={<MeetingRoomIcon />}
                  >
                    Unlock
                  </Button>
                  <Button 
                    variant="contained" 
                    color="secondary" 
                    onClick={handleDoorLock}
                    disabled={selectedIcon.data && selectedIcon.data.value === 'locked'}
                    startIcon={<LockIcon />}
                  >
                    Lock
                  </Button>
                  <Button 
                    variant="contained" 
                    color="info" 
                    onClick={handleDoorPassageMode}
                    disabled={selectedIcon.data && selectedIcon.data.value === 'passage'}
                    startIcon={<MeetingRoomIcon />}
                  >
                    Passage Mode
                  </Button>
                </Box>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          {isEditing && (
            <Button onClick={handleDeleteIcon} color="error">
              Delete
            </Button>
          )}
          <Button onClick={() => setIconDetailsOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Render integration selection dialog
  const renderIntegrationDialog = () => {
    return (
      <Dialog open={integrationDialogOpen} onClose={() => setIntegrationDialogOpen(false)}>
        <DialogTitle>Select Integration</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle1">
                Select a device to link to this {iconToAdd} icon:
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Device</InputLabel>
                <Select
                  value={selectedDevice}
                  onChange={(e) => setSelectedDevice(e.target.value)}
                  label="Device"
                >
                  {availableDevices.map(device => (
                    <MenuItem key={device.id} value={device.id}>
                      {device.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIntegrationDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleAddIcon} 
            color="primary" 
            disabled={!selectedDevice}
          >
            Add
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Render edit mode toolbar
  const renderEditToolbar = () => {
    if (!isEditing) return null;
    
    return (
      <Paper sx={{ p: 1, mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
        <Typography variant="subtitle1" sx={{ flexGrow: 1, alignSelf: 'center' }}>
          Add Icon:
        </Typography>
        <Tooltip title="Add Temperature Sensor">
          <IconButton 
            color={iconToAdd === 'temperature' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('temperature')}
          >
            <ThermostatIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Add Door">
          <IconButton 
            color={iconToAdd === 'door' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('door')}
          >
            <MeetingRoomIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Add Camera">
          <IconButton 
            color={iconToAdd === 'camera' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('camera')}
          >
            <VideocamIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Add HVAC">
          <IconButton 
            color={iconToAdd === 'hvac' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('hvac')}
          >
            <AcUnitIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Add WiFi">
          <IconButton 
            color={iconToAdd === 'wifi' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('wifi')}
          >
            <WifiIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Add Network Device">
          <IconButton 
            color={iconToAdd === 'network' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('network')}
          >
            <RouterIcon />
          </IconButton>
        </Tooltip>
        <Box sx={{ flexGrow: 1 }} />
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<SaveIcon />}
          onClick={() => {
            setIsEditing(false);
            if (onSave) onSave();
          }}
        >
          Save
        </Button>
        <Button 
          variant="outlined" 
          startIcon={<CancelIcon />}
          onClick={() => {
            setIsEditing(false);
            if (onCancel) onCancel();
          }}
        >
          Cancel
        </Button>
      </Paper>
    );
  };

  return (
    <Box>
      <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Building</InputLabel>
          <Select
            value={selectedBuilding}
            onChange={handleBuildingChange}
            label="Building"
          >
            {buildings.map(building => (
              <MenuItem key={building._id} value={building._id}>
                {building.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Floor</InputLabel>
          <Select
            value={selectedFloor}
            onChange={handleFloorChange}
            label="Floor"
            disabled={!selectedBuilding || floors.length === 0}
          >
            {floors.map(floor => (
              <MenuItem key={floor._id} value={floor._id}>
                {floor.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        <Box sx={{ flexGrow: 1 }} />
        
        <Tooltip title="Zoom In">
          <IconButton onClick={handleZoomIn}>
            <ZoomInIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Zoom Out">
          <IconButton onClick={handleZoomOut}>
            <ZoomOutIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Refresh">
          <IconButton onClick={handleRefresh}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
        
        {!isEditing && (
          <Tooltip title="Edit Floor Plan">
            <IconButton 
              color="primary" 
              onClick={() => setIsEditing(true)}
            >
              <EditIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>
      
      {renderEditToolbar()}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Typography color="error">{error}</Typography>
      ) : !selectedFloor ? (
        <Typography>Please select a building and floor to view the floor plan.</Typography>
      ) : (
        <Box 
          ref={containerRef}
          sx={{ 
            position: 'relative', 
            overflow: 'auto',
            border: '1px solid #ccc',
            borderRadius: 1,
            height: 'calc(100vh - 250px)',
            minHeight: 400
          }}
        >
          <Box 
            ref={floorPlanRef}
            onClick={handleFloorPlanClick}
            sx={{ 
              position: 'relative',
              transform: `scale(${zoom})`,
              transformOrigin: '0 0',
              backgroundImage: `url(${floorPlanUrl})`,
              backgroundSize: 'contain',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center top',
              width: '100%',
              height: '100%',
              cursor: isEditing && iconToAdd !== null ? 'crosshair' : 'default'
            }}
          >
            {icons.map(icon => (
              <Tooltip key={icon._id} title={getIconTooltip(icon)}>
                <IconButton
                  onClick={(e) => handleIconClick(icon, e)}
                  onContextMenu={(e) => handleIconClick(icon, e)}
                  sx={{
                    position: 'absolute',
                    left: `${icon.position.x}px`,
                    top: `${icon.position.y}px`,
                    transform: `rotate(${icon.position.rotation || 0}deg)`,
                    color: getIconColor(icon),
                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    },
                    width: icon.size?.width || 32,
                    height: icon.size?.height || 32,
                    p: 0.5
                  }}
                >
                  {getIconComponent(icon.type)}
                </IconButton>
              </Tooltip>
            ))}
          </Box>
        </Box>
      )}
      
      {renderIconDetailsDialog()}
      {renderIntegrationDialog()}
      
      {/* Door Control Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={handleContextMenuClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <Box sx={{ p: 1, minWidth: 200 }}>
          {contextMenuIcon && (
            <>
              <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'bold' }}>
                {contextMenuIcon.name}
              </Typography>
              
              <Typography variant="body2" sx={{ mb: 2 }}>
                Status: {contextMenuIcon.data?.value || 'Unknown'}
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<LockOpenIcon />}
                  onClick={() => handleDoorUnlock()}
                  disabled={contextMenuIcon.data && contextMenuIcon.data.value === 'unlocked'}
                  fullWidth
                >
                  Unlock Door
                </Button>
                
                <Button
                  variant="contained"
                  color="secondary"
                  startIcon={<LockIcon />}
                  onClick={() => handleDoorLock()}
                  disabled={contextMenuIcon.data && contextMenuIcon.data.value === 'locked'}
                  fullWidth
                >
                  Lock Door
                </Button>
                
                <Button
                  variant="contained"
                  color="info"
                  startIcon={<MeetingRoomOutlinedIcon />}
                  onClick={() => handleDoorPassageMode()}
                  disabled={contextMenuIcon.data && contextMenuIcon.data.value === 'passage'}
                  fullWidth
                >
                  Passage Mode
                </Button>
              </Box>
            </>
          )}
        </Box>
      </Menu>
    </Box>
  );
};

export default FloorPlanViewer;