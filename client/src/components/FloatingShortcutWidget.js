import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Paper, 
  IconButton, 
  Tooltip, 
  Popover, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  ListItemButton,
  Typography,
  Divider,
  CircularProgress,
  Alert,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Checkbox,
  FormControlLabel,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  AppBar,
  Toolbar,
  Drawer,
  Collapse
} from '@mui/material';
import { 
  Link as LinkIcon,
  Edit as EditIcon,
  Close as CloseIcon,
  Add as AddIcon,
  Settings as SettingsIcon,
  Search as SearchIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  CloudUpload as CloudUploadIcon,
  CloudOff as CloudOffIcon,
  Dashboard as DashboardIcon,
  Folder as FolderIcon,
  People as PeopleIcon,
  Event as CalendarIcon,
  Description as FormsIcon,
  ContactPage as StaffDirectoryIcon,
  Assignment as TasksIcon,
  MeetingRoom as RoomBookingIcon,
  HomeWork as BuildingManagementIcon,
  HelpOutline as HelpIcon,
  QuestionAnswer as FAQIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import axios from 'axios';

const FloatingShortcutWidget = () => {
  // State for the widget
  const [expanded, setExpanded] = useState(false); // Start collapsed by default
  const [shortcuts, setShortcuts] = useState([]);
  const [portalPages, setPortalPages] = useState([]);
  const [favoriteShortcuts, setFavoriteShortcuts] = useState([]);
  const [favoritePortalPages, setFavoritePortalPages] = useState([]);
  const [widgetEnabled, setWidgetEnabled] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [savingToDb, setSavingToDb] = useState(false);
  const [saveError, setSaveError] = useState(null);
  const [tabValue, setTabValue] = useState(0); // 0 for shortcuts, 1 for portal pages
  
  // Define portal pages
  const portalPagesList = [
    { id: 'dashboard', title: 'Dashboard', url: '/dashboard', icon: <DashboardIcon /> },
    { id: 'shortcuts', title: 'Shortcuts', url: '/shortcuts', icon: <LinkIcon /> },
    { id: 'drive', title: 'Drive Files', url: '/drive', icon: <FolderIcon /> },
    { id: 'google-calendar', title: 'Google Calendar', url: '/google-calendar', icon: <CalendarIcon /> },
    { id: 'google-forms', title: 'Google Forms', url: '/google-forms', icon: <FormsIcon /> },
    { id: 'staff-directory', title: 'Staff Directory', url: '/staff-directory', icon: <StaffDirectoryIcon /> },
    { id: 'people', title: 'People Directory', url: '/people', icon: <PeopleIcon /> },
    { id: 'tasks', title: 'Tasks', url: '/tasks', icon: <TasksIcon /> },
    { id: 'room-booking', title: 'Room Booking', url: '/room-booking', icon: <RoomBookingIcon /> },
    { id: 'building-management', title: 'Building Management', url: '/building-management', icon: <BuildingManagementIcon /> },
    { id: 'help', title: 'Help Center', url: '/help', icon: <HelpIcon /> },
    { id: 'faq', title: 'FAQs', url: '/help/faq', icon: <FAQIcon /> }
  ];
  
  // State for the settings dialog
  const [settingsOpen, setSettingsOpen] = useState(false);
  
  // Fetch shortcuts and preferences from the API
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch shortcuts
        const shortcutsRes = await axios.get('/api/shortcuts');
        setShortcuts(shortcutsRes.data);
        
        // Initialize portal pages
        setPortalPages(portalPagesList);
        
        // Try to fetch user preferences from the database
        try {
          const preferencesRes = await axios.get('/api/users/me/widget-preferences');
          const floatingShortcutPrefs = preferencesRes.data.floatingShortcut;
          
          if (floatingShortcutPrefs) {
            // If preferences exist in the database, use them and update localStorage
            setFavoriteShortcuts(floatingShortcutPrefs.favoriteShortcuts || []);
            setFavoritePortalPages(floatingShortcutPrefs.favoritePortalPages || []);
            setWidgetEnabled(floatingShortcutPrefs.enabled !== false); // Default to true if not specified
            
            // Update localStorage with the database values
            localStorage.setItem('favoriteShortcuts', JSON.stringify(floatingShortcutPrefs.favoriteShortcuts || []));
            localStorage.setItem('favoritePortalPages', JSON.stringify(floatingShortcutPrefs.favoritePortalPages || []));
            localStorage.setItem('floatingShortcutWidgetEnabled', floatingShortcutPrefs.enabled !== false ? 'true' : 'false');
          } else {
            // If no preferences in database, check localStorage
            loadFromLocalStorage(shortcutsRes.data);
          }
        } catch (err) {
          console.error('Error fetching widget preferences:', err);
          // If API call fails, fall back to localStorage
          loadFromLocalStorage(shortcutsRes.data);
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching shortcuts:', err);
        setError('Failed to load shortcuts');
        setLoading(false);
      }
    };
    
    // Helper function to load preferences from localStorage
    const loadFromLocalStorage = (shortcutsData) => {
      // Load favorite shortcuts from localStorage
      const storedFavorites = localStorage.getItem('favoriteShortcuts');
      const storedFavoritePortalPages = localStorage.getItem('favoritePortalPages');
      const storedWidgetEnabled = localStorage.getItem('floatingShortcutWidgetEnabled');
      
      let parsedFavorites = [];
      if (storedFavorites) {
        parsedFavorites = JSON.parse(storedFavorites);
        setFavoriteShortcuts(parsedFavorites);
      } else {
        // Default to top 5 most clicked shortcuts if no favorites are saved
        const sortedShortcuts = [...shortcutsData].sort((a, b) => b.clickCount - a.clickCount);
        parsedFavorites = sortedShortcuts.slice(0, 5).map(s => s._id);
        setFavoriteShortcuts(parsedFavorites);
        localStorage.setItem('favoriteShortcuts', JSON.stringify(parsedFavorites));
      }
      
      let parsedFavoritePortalPages = [];
      if (storedFavoritePortalPages) {
        parsedFavoritePortalPages = JSON.parse(storedFavoritePortalPages);
        setFavoritePortalPages(parsedFavoritePortalPages);
      } else {
        // Default to first 3 portal pages if no favorites are saved
        parsedFavoritePortalPages = portalPagesList.slice(0, 3).map(p => p.id);
        setFavoritePortalPages(parsedFavoritePortalPages);
        localStorage.setItem('favoritePortalPages', JSON.stringify(parsedFavoritePortalPages));
      }
      
      // Set widget enabled state
      const isEnabled = storedWidgetEnabled !== 'false';
      setWidgetEnabled(isEnabled);
      
      // Save to database in background
      savePreferencesToDatabase(parsedFavorites, parsedFavoritePortalPages, isEnabled);
    };
    
    fetchData();
  }, []);
  
  // Function to save preferences to the database
  const savePreferencesToDatabase = async (favorites, favoritePortals, enabled) => {
    try {
      setSavingToDb(true);
      setSaveError(null);
      
      await axios.put('/api/users/me/widget-preferences', {
        widgetType: 'floatingShortcut',
        preferences: {
          favoriteShortcuts: favorites,
          favoritePortalPages: favoritePortals,
          enabled: enabled
        }
      });
      
      setSavingToDb(false);
    } catch (err) {
      console.error('Error saving widget preferences to database:', err);
      setSaveError('Failed to sync preferences with server');
      setSavingToDb(false);
    }
  };
  
  // Handle toggling the expanded state of the widget
  const handleToggleExpand = () => {
    setExpanded(!expanded);
    
    // If collapsing and in edit mode, exit edit mode
    if (expanded && editMode) {
      setEditMode(false);
      setSearchQuery('');
      setTabValue(0); // Reset to shortcuts tab
    }
  };
  
  // Handle closing the edit mode
  const handleCloseEdit = () => {
    setEditMode(false);
    setSearchQuery('');
    setTabValue(0); // Reset to shortcuts tab
  };
  
  // Handle shortcut click
  const handleShortcutClick = async (id) => {
    try {
      await axios.post(`/api/shortcuts/${id}/click`);
    } catch (err) {
      console.error('Error tracking shortcut click:', err);
    }
    
    // No need to close the toolbar when clicking a shortcut
  };
  
  // Handle toggling edit mode
  const handleToggleEditMode = () => {
    setEditMode(!editMode);
    setSearchQuery('');
  };
  
  // Handle toggling a favorite shortcut
  const handleToggleFavorite = (id) => {
    let newFavorites;
    
    if (favoriteShortcuts.includes(id)) {
      // Remove from favorites
      newFavorites = favoriteShortcuts.filter(shortcutId => shortcutId !== id);
    } else {
      // Add to favorites
      newFavorites = [...favoriteShortcuts, id];
    }
    
    setFavoriteShortcuts(newFavorites);
    localStorage.setItem('favoriteShortcuts', JSON.stringify(newFavorites));
    
    // Save to database
    savePreferencesToDatabase(newFavorites, favoritePortalPages, widgetEnabled);
  };
  
  // Handle toggling a favorite portal page
  const handleToggleFavoritePortalPage = (id) => {
    let newFavorites;
    
    if (favoritePortalPages.includes(id)) {
      // Remove from favorites
      newFavorites = favoritePortalPages.filter(pageId => pageId !== id);
    } else {
      // Add to favorites
      newFavorites = [...favoritePortalPages, id];
    }
    
    setFavoritePortalPages(newFavorites);
    localStorage.setItem('favoritePortalPages', JSON.stringify(newFavorites));
    
    // Save to database
    savePreferencesToDatabase(favoriteShortcuts, newFavorites, widgetEnabled);
  };
  
  // Handle opening settings dialog
  const handleOpenSettings = () => {
    setSettingsOpen(true);
  };
  
  // Handle closing settings dialog
  const handleCloseSettings = () => {
    setSettingsOpen(false);
  };
  
  // Handle toggling widget enabled state
  const handleToggleWidgetEnabled = (enabled) => {
    setWidgetEnabled(enabled);
    localStorage.setItem('floatingShortcutWidgetEnabled', enabled ? 'true' : 'false');
    
    // Save to database
    savePreferencesToDatabase(favoriteShortcuts, favoritePortalPages, enabled);
  };
  
  // Filter shortcuts based on search query
  const filteredShortcuts = searchQuery 
    ? shortcuts.filter(shortcut => 
        shortcut.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (shortcut.description && shortcut.description.toLowerCase().includes(searchQuery.toLowerCase())))
    : shortcuts;
  
  // Get favorite shortcuts
  const getFavoriteShortcutsData = () => {
    return shortcuts.filter(shortcut => favoriteShortcuts.includes(shortcut._id));
  };
  
  // Get favorite portal pages
  const getFavoritePortalPagesData = () => {
    return portalPages.filter(page => favoritePortalPages.includes(page.id));
  };
  
  // Handle portal page click
  const handlePortalPageClick = (id) => {
    // No need to track clicks for portal pages
    // No need to close the toolbar when clicking a portal page
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // Filter portal pages based on search query
  const filteredPortalPages = searchQuery 
    ? portalPages.filter(page => 
        page.title.toLowerCase().includes(searchQuery.toLowerCase()))
    : portalPages;
  
  // Render the edit panel content
  const renderEditPanel = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }
    
    if (error) {
      return <Alert severity="error">{error}</Alert>;
    }
    
    return (
      <>
        <Box sx={{ p: 2 }}>
          <TextField
            fullWidth
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            size="small"
            sx={{ mb: 2 }}
          />
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            variant="fullWidth"
            sx={{ mb: 2 }}
          >
            <Tab label="Shortcuts" />
            <Tab label="Portal Pages" />
          </Tabs>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Select your favorites to appear in your toolbar.
          </Typography>
        </Box>
        <Divider />
        
        {tabValue === 0 ? (
          // Shortcuts tab
          <List sx={{ maxHeight: 300, overflow: 'auto' }}>
            {filteredShortcuts.map((shortcut) => (
              <ListItem 
                key={shortcut._id}
                disablePadding
                secondaryAction={
                  <Checkbox
                    edge="end"
                    icon={<StarBorderIcon />}
                    checkedIcon={<StarIcon />}
                    checked={favoriteShortcuts.includes(shortcut._id)}
                    onChange={() => handleToggleFavorite(shortcut._id)}
                  />
                }
              >
                <ListItemButton onClick={() => handleToggleFavorite(shortcut._id)}>
                  <ListItemIcon>
                    <LinkIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary={shortcut.title} 
                    secondary={shortcut.description}
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        ) : (
          // Portal Pages tab
          <List sx={{ maxHeight: 300, overflow: 'auto' }}>
            {filteredPortalPages.map((page) => (
              <ListItem 
                key={page.id}
                disablePadding
                secondaryAction={
                  <Checkbox
                    edge="end"
                    icon={<StarBorderIcon />}
                    checkedIcon={<StarIcon />}
                    checked={favoritePortalPages.includes(page.id)}
                    onChange={() => handleToggleFavoritePortalPage(page.id)}
                  />
                }
              >
                <ListItemButton onClick={() => handleToggleFavoritePortalPage(page.id)}>
                  <ListItemIcon>
                    {page.icon}
                  </ListItemIcon>
                  <ListItemText 
                    primary={page.title}
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        )}
      </>
    );
  };
  
  // Render the toolbar content
  const renderToolbarContent = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
          <CircularProgress size={24} />
        </Box>
      );
    }
    
    if (error) {
      return <Alert severity="error" sx={{ py: 0 }}>{error}</Alert>;
    }
    
    const favoriteShortcutsData = getFavoriteShortcutsData();
    const favoritePortalPagesData = getFavoritePortalPagesData();
    
    if (favoriteShortcutsData.length === 0 && favoritePortalPagesData.length === 0) {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', px: 2 }}>
          <Typography variant="body2" sx={{ flexGrow: 1 }}>
            No shortcuts selected
          </Typography>
          <Button 
            size="small"
            startIcon={<EditIcon />}
            onClick={handleToggleEditMode}
          >
            Customize
          </Button>
        </Box>
      );
    }
    
    return (
      <Box sx={{ display: 'flex', overflowX: 'auto', px: 1 }}>
        {/* Portal Pages */}
        {favoritePortalPagesData.map((page) => (
          <Box
            key={page.id}
            sx={{
              display: 'flex',
              alignItems: 'center',
              position: 'relative',
              mx: 0.5,
              transition: 'all 0.7s ease',
              '&:hover': {
                '& .icon-title': {
                  width: 'auto',
                  opacity: 1,
                  marginLeft: 1,
                  visibility: 'visible',
                },
                zIndex: 10
              }
            }}
          >
            <IconButton
              component="a"
              href={page.url}
              onClick={() => handlePortalPageClick(page.id)}
              size="medium"
            >
              {page.icon}
            </IconButton>
            <Typography
              className="icon-title"
              variant="body2"
              sx={{
                width: 0,
                opacity: 0,
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                transition: 'all 0.7s ease',
                visibility: 'hidden'
              }}
            >
              {page.title}
            </Typography>
          </Box>
        ))}
        
        {/* Shortcuts */}
        {favoriteShortcutsData.map((shortcut) => (
          <Box
            key={shortcut._id}
            sx={{
              display: 'flex',
              alignItems: 'center',
              position: 'relative',
              mx: 0.5,
              transition: 'all 0.7s ease',
              '&:hover': {
                '& .icon-title': {
                  width: 'auto',
                  opacity: 1,
                  marginLeft: 1,
                  visibility: 'visible',
                },
                zIndex: 10
              }
            }}
          >
            <IconButton
              component="a"
              href={shortcut.url}
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => handleShortcutClick(shortcut._id)}
              size="medium"
            >
              <LinkIcon />
            </IconButton>
            <Typography
              className="icon-title"
              variant="body2"
              sx={{
                width: 0,
                opacity: 0,
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                transition: 'all 0.7s ease',
                visibility: 'hidden'
              }}
            >
              {shortcut.title}
            </Typography>
          </Box>
        ))}
      </Box>
    );
  };
  
  return (
    <>
      {/* Bottom toolbar */}
      {widgetEnabled && (
        <AppBar 
          position="fixed" 
          color="default" 
          sx={{ 
            top: 'auto', 
            bottom: 0, 
            zIndex: 1000,
            boxShadow: 3,
            backgroundColor: 'background.paper'
          }}
        >
          <Toolbar variant="dense" sx={{ minHeight: 48, justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
              {renderToolbarContent()}
            </Box>
            <Box>
              <Tooltip title={editMode ? "Save" : "Customize"}>
                <IconButton onClick={handleToggleEditMode} size="small">
                  {editMode ? <CloseIcon /> : <EditIcon />}
                </IconButton>
              </Tooltip>
              <Tooltip title="Settings">
                <IconButton onClick={handleOpenSettings} size="small">
                  <SettingsIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title={expanded ? "Collapse" : "Expand"}>
                <IconButton onClick={handleToggleExpand} size="small">
                  {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Toolbar>
        </AppBar>
      )}
      
      {/* Edit panel drawer */}
      <Drawer
        anchor="bottom"
        open={editMode}
        onClose={handleCloseEdit}
        PaperProps={{
          sx: { maxHeight: '70vh' }
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2, pb: 1 }}>
          <Typography variant="h6">Customize Quick Links</Typography>
          <IconButton onClick={handleCloseEdit} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        {renderEditPanel()}
      </Drawer>
      
      {/* Expanded panel */}
      <Drawer
        anchor="bottom"
        open={expanded && !editMode}
        onClose={handleToggleExpand}
        PaperProps={{
          sx: { maxHeight: '50vh' }
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2, pb: 1 }}>
          <Typography variant="h6">Quick Links</Typography>
          <IconButton onClick={handleToggleExpand} size="small">
            <ExpandLessIcon />
          </IconButton>
        </Box>
        <Divider />
        <List>
          {/* Portal Pages */}
          {getFavoritePortalPagesData().map((page) => (
            <ListItem 
              key={page.id}
              component="a"
              href={page.url}
              onClick={() => handlePortalPageClick(page.id)}
              button
              divider
            >
              <ListItemIcon>
                {page.icon}
              </ListItemIcon>
              <ListItemText 
                primary={page.title}
              />
            </ListItem>
          ))}
          
          {/* Shortcuts */}
          {getFavoriteShortcutsData().map((shortcut) => (
            <ListItem 
              key={shortcut._id}
              component="a"
              href={shortcut.url}
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => handleShortcutClick(shortcut._id)}
              button
              divider
            >
              <ListItemIcon>
                <LinkIcon />
              </ListItemIcon>
              <ListItemText 
                primary={shortcut.title} 
                secondary={shortcut.description}
              />
            </ListItem>
          ))}
        </List>
      </Drawer>
      
      {/* Settings dialog */}
      <Dialog
        open={settingsOpen}
        onClose={handleCloseSettings}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          Quick Links Settings
          <IconButton
            aria-label="close"
            onClick={handleCloseSettings}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <FormControlLabel
            control={
              <Checkbox
                checked={widgetEnabled}
                onChange={(e) => handleToggleWidgetEnabled(e.target.checked)}
              />
            }
            label="Show quick links toolbar"
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            The quick links toolbar provides easy access to your favorite links from anywhere in the portal.
          </Typography>
          
          {savingToDb && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
              <CircularProgress size={16} sx={{ mr: 1 }} />
              <Typography variant="body2" color="text.secondary">
                Saving preferences...
              </Typography>
            </Box>
          )}
          
          {saveError && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              {saveError}
            </Alert>
          )}
          
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Your preferences are saved to your account and will be available on any device you log in from.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseSettings}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default FloatingShortcutWidget;