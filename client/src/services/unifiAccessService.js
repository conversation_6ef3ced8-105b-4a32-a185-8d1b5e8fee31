import axios from 'axios';

/**
 * UniFi Access API Service
 * Handles client-side API calls to the UniFi Access endpoints
 */
const unifiAccessService = {
  /**
   * Set up UniFi Access with one click
   * @returns {Promise<Object>} Response message
   * @deprecated UniFi Access now uses environment variables for configuration
   */
  oneClickSetup: async () => {
    try {
      const response = await axios.post('/api/unifi-access/one-click-setup');
      return response.data;
    } catch (error) {
      console.error('Error setting up UniFi Access with one click:', error);
      // If the error contains information about environment variables, log it
      if (error.response && error.response.data && error.response.data.environmentVariables) {
        console.info('UniFi Access now uses environment variables:', 
          error.response.data.environmentVariables);
      }
      throw error;
    }
  },
  /**
   * Get all doors
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of doors
   */
  getDoors: async (params = {}) => {
    try {
      const response = await axios.get('/api/unifi-access/doors', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access doors:', error);
      throw error;
    }
  },

  /**
   * Get door details
   * @param {string} doorId Door ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Door details
   */
  getDoor: async (doorId, params = {}) => {
    try {
      const response = await axios.get(`/api/unifi-access/doors/${doorId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Access door ${doorId}:`, error);
      throw error;
    }
  },

  /**
   * Get all access points
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of access points
   */
  getAccessPoints: async (params = {}) => {
    try {
      const response = await axios.get('/api/unifi-access/access-points', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access points:', error);
      throw error;
    }
  },

  /**
   * Get access point details
   * @param {string} accessPointId Access point ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Access point details
   */
  getAccessPoint: async (accessPointId, params = {}) => {
    try {
      const response = await axios.get(`/api/unifi-access/access-points/${accessPointId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Access point ${accessPointId}:`, error);
      throw error;
    }
  },

  /**
   * Get all users
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of users
   */
  getUsers: async (params = {}) => {
    try {
      const response = await axios.get('/api/unifi-access/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access users:', error);
      throw error;
    }
  },

  /**
   * Get user details
   * @param {string} userId User ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} User details
   */
  getUser: async (userId, params = {}) => {
    try {
      const response = await axios.get(`/api/unifi-access/users/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Access user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Get all access events
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of access events
   */
  getAccessEvents: async (params = {}) => {
    try {
      const response = await axios.get('/api/unifi-access/events', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access events:', error);
      throw error;
    }
  },

  /**
   * Control door
   * @param {string} doorId Door ID
   * @param {Object} command Command object with action and parameters
   * @returns {Promise<Object>} Response message
   */
  controlDoor: async (doorId, command) => {
    try {
      // Check the command action and call the appropriate endpoint
      if (command.action === 'unlock') {
        const response = await axios.post(`/api/unifi-access/doors/${doorId}/unlock`);
        return response.data;
      } else if (command.action === 'lock') {
        const response = await axios.post(`/api/unifi-access/doors/${doorId}/lock`);
        return response.data;
      } else {
        throw new Error(`Unsupported door action: ${command.action}`);
      }
    } catch (error) {
      console.error(`Error controlling UniFi Access door ${doorId}:`, error);
      throw error;
    }
  },

  /**
   * Save UniFi Access configuration
   * @param {Object} config Configuration object with credentials
   * @returns {Promise<Object>} Response message
   * @deprecated UniFi Access now uses environment variables for configuration
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/unifi-access/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving UniFi Access configuration:', error);
      // Pass through the error with the environment variables information
      throw error;
    }
  },

  /**
   * Get UniFi Access configuration status
   * @returns {Promise<Object>} Configuration status from environment variables
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/unifi-access/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found means environment variables are not set
        console.warn('UniFi Access environment variables not configured');
        return null;
      }
      console.error('Error fetching UniFi Access configuration:', error);
      throw error;
    }
  }
};

export default unifiAccessService;
