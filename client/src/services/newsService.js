import axios from 'axios';

/**
 * News Service
 * Provides methods for interacting with the news API
 */
const newsService = {
  /**
   * Get all news categories
   * @returns {Promise<Array>} List of categories
   */
  getAllCategories: async () => {
    try {
      const response = await axios.get('/api/news-categories');
      return response.data;
    } catch (error) {
      console.error('Error getting news categories:', error);
      throw error;
    }
  },

  /**
   * Get news categories accessible by the current user
   * @returns {Promise<Array>} List of accessible categories
   */
  getAccessibleCategories: async () => {
    try {
      const response = await axios.get('/api/news-categories/accessible');
      return response.data;
    } catch (error) {
      console.error('Error getting accessible news categories:', error);
      throw error;
    }
  },

  /**
   * Get category by ID
   * @param {string} id - Category ID
   * @returns {Promise<Object>} Category details
   */
  getCategoryById: async (id) => {
    try {
      const response = await axios.get(`/api/news-categories/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting news category:', error);
      throw error;
    }
  },

  /**
   * Create a new news category
   * @param {Object} categoryData - Category data
   * @returns {Promise<Object>} Created category
   */
  createCategory: async (categoryData) => {
    try {
      const response = await axios.post('/api/news-categories', categoryData);
      return response.data;
    } catch (error) {
      console.error('Error creating news category:', error);
      throw error;
    }
  },

  /**
   * Update a news category
   * @param {string} id - Category ID
   * @param {Object} categoryData - Category data
   * @returns {Promise<Object>} Updated category
   */
  updateCategory: async (id, categoryData) => {
    try {
      const response = await axios.put(`/api/news-categories/${id}`, categoryData);
      return response.data;
    } catch (error) {
      console.error('Error updating news category:', error);
      throw error;
    }
  },

  /**
   * Delete a news category
   * @param {string} id - Category ID
   * @returns {Promise<Object>} Response message
   */
  deleteCategory: async (id) => {
    try {
      const response = await axios.delete(`/api/news-categories/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting news category:', error);
      throw error;
    }
  },

  /**
   * Get all news posts with pagination and filtering
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Number of posts per page
   * @param {string} params.category - Category ID to filter by
   * @param {boolean} params.featured - Filter by featured status
   * @param {boolean} params.published - Filter by published status
   * @returns {Promise<Object>} Posts and pagination info
   */
  getAllPosts: async (params = {}) => {
    try {
      const response = await axios.get('/api/news-posts', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting news posts:', error);
      throw error;
    }
  },

  /**
   * Get latest news posts for dashboard widget
   * @param {Object} params - Query parameters
   * @param {number} params.limit - Number of posts to return
   * @returns {Promise<Array>} List of latest posts
   */
  getLatestPosts: async (params = {}) => {
    try {
      const response = await axios.get('/api/news-posts/latest', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting latest news posts:', error);
      throw error;
    }
  },

  /**
   * Get post by ID
   * @param {string} id - Post ID
   * @returns {Promise<Object>} Post details
   */
  getPostById: async (id) => {
    try {
      const response = await axios.get(`/api/news-posts/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting news post:', error);
      throw error;
    }
  },

  /**
   * Create a new news post
   * @param {Object} postData - Post data
   * @returns {Promise<Object>} Created post
   */
  createPost: async (postData) => {
    try {
      const response = await axios.post('/api/news-posts', postData);
      return response.data;
    } catch (error) {
      console.error('Error creating news post:', error);
      throw error;
    }
  },

  /**
   * Update a news post
   * @param {string} id - Post ID
   * @param {Object} postData - Post data
   * @returns {Promise<Object>} Updated post
   */
  updatePost: async (id, postData) => {
    try {
      const response = await axios.put(`/api/news-posts/${id}`, postData);
      return response.data;
    } catch (error) {
      console.error('Error updating news post:', error);
      throw error;
    }
  },

  /**
   * Delete a news post
   * @param {string} id - Post ID
   * @returns {Promise<Object>} Response message
   */
  deletePost: async (id) => {
    try {
      const response = await axios.delete(`/api/news-posts/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting news post:', error);
      throw error;
    }
  },

  /**
   * Toggle post featured status
   * @param {string} id - Post ID
   * @returns {Promise<Object>} Updated post
   */
  toggleFeatured: async (id) => {
    try {
      const response = await axios.put(`/api/news-posts/${id}/toggle-featured`);
      return response.data;
    } catch (error) {
      console.error('Error toggling news post featured status:', error);
      throw error;
    }
  },

  /**
   * Toggle post published status
   * @param {string} id - Post ID
   * @returns {Promise<Object>} Updated post
   */
  togglePublished: async (id) => {
    try {
      const response = await axios.put(`/api/news-posts/${id}/toggle-published`);
      return response.data;
    } catch (error) {
      console.error('Error toggling news post published status:', error);
      throw error;
    }
  }
};

export default newsService;