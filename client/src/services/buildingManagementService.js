import axios from 'axios';

/**
 * Service for interacting with the Building Management System API
 */
class BuildingManagementService {
  /**
   * Get the status of all building management integrations
   * @returns {Promise<Object>} Integration statuses
   */
  async getIntegrationsStatus() {
    try {
      const response = await axios.get('/api/integration-status');
      return response.data;
    } catch (error) {
      console.error('Error fetching building management integrations status:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Building Management API not found, returning mock data');
        return this.getMockIntegrationsStatus();
      }

      throw error;
    }
  }

  /**
   * Get all buildings
   * @returns {Promise<Array>} List of buildings
   */
  async getBuildings() {
    try {
      const response = await axios.get('/api/buildings');
      return response.data;
    } catch (error) {
      console.error('Error fetching buildings:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Buildings API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }

  /**
   * Get a building by ID
   * @param {string} id Building ID
   * @returns {Promise<Object>} Building details
   */
  async getBuilding(id) {
    try {
      const response = await axios.get(`/api/buildings/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching building ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new building
   * @param {Object} buildingData Building data
   * @returns {Promise<Object>} Created building
   */
  async createBuilding(buildingData) {
    try {
      const response = await axios.post('/api/buildings', buildingData);
      return response.data;
    } catch (error) {
      console.error('Error creating building:', error);
      throw error;
    }
  }

  /**
   * Update a building
   * @param {string} id Building ID
   * @param {Object} buildingData Building data
   * @returns {Promise<Object>} Updated building
   */
  async updateBuilding(id, buildingData) {
    try {
      const response = await axios.put(`/api/buildings/${id}`, buildingData);
      return response.data;
    } catch (error) {
      console.error(`Error updating building ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a building
   * @param {string} id Building ID
   * @returns {Promise<Object>} Response message
   */
  async deleteBuilding(id) {
    try {
      const response = await axios.delete(`/api/buildings/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting building ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all floors for a building
   * @param {string} buildingId Building ID
   * @returns {Promise<Array>} List of floors
   */
  async getBuildingFloors(buildingId) {
    try {
      const response = await axios.get(`/api/buildings/${buildingId}/floors`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching floors for building ${buildingId}:`, error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Floors API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }

  /**
   * Get all floors
   * @returns {Promise<Array>} List of floors
   */
  async getFloors() {
    try {
      const response = await axios.get('/api/floors');
      return response.data;
    } catch (error) {
      console.error('Error fetching floors:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Floors API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }

  /**
   * Get a floor by ID
   * @param {string} id Floor ID
   * @returns {Promise<Object>} Floor details
   */
  async getFloor(id) {
    try {
      const response = await axios.get(`/api/floors/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching floor ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new floor
   * @param {Object} floorData Floor data
   * @param {File} floorplanFile Floorplan image file
   * @returns {Promise<Object>} Created floor
   */
  async createFloor(floorData, floorplanFile) {
    try {
      const formData = new FormData();

      // Add floor data to form
      Object.keys(floorData).forEach(key => {
        if (key === 'dimensions' || key === 'metadata') {
          formData.append(key, JSON.stringify(floorData[key]));
        } else {
          formData.append(key, floorData[key]);
        }
      });

      // Add floorplan file if provided
      if (floorplanFile) {
        formData.append('floorplan', floorplanFile);
      }

      const response = await axios.post('/api/floors', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error creating floor:', error);
      throw error;
    }
  }

  /**
   * Update a floor
   * @param {string} id Floor ID
   * @param {Object} floorData Floor data
   * @param {File} floorplanFile Floorplan image file
   * @returns {Promise<Object>} Updated floor
   */
  async updateFloor(id, floorData, floorplanFile) {
    try {
      const formData = new FormData();

      // Add floor data to form
      Object.keys(floorData).forEach(key => {
        if (key === 'dimensions' || key === 'metadata') {
          formData.append(key, JSON.stringify(floorData[key]));
        } else {
          formData.append(key, floorData[key]);
        }
      });

      // Add floorplan file if provided
      if (floorplanFile) {
        formData.append('floorplan', floorplanFile);
      }

      const response = await axios.put(`/api/floors/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error(`Error updating floor ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a floor
   * @param {string} id Floor ID
   * @returns {Promise<Object>} Response message
   */
  async deleteFloor(id) {
    try {
      const response = await axios.delete(`/api/floors/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting floor ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get floorplan image URL for a floor
   * @param {string} id Floor ID
   * @returns {string} Floorplan image URL
   */
  getFloorplanUrl(id) {
    return `/api/floors/${id}/floorplan`;
  }

  /**
   * Get all icons for a floor
   * @param {string} floorId Floor ID
   * @returns {Promise<Array>} List of icons
   */
  async getFloorIcons(floorId) {
    try {
      const response = await axios.get(`/api/floors/${floorId}/icons`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching icons for floor ${floorId}:`, error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Icons API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }

  /**
   * Create a new floorplan icon
   * @param {Object} iconData Icon data
   * @returns {Promise<Object>} Created icon
   */
  async createFloorplanIcon(iconData) {
    try {
      // Convert objects to JSON strings
      const formattedData = { ...iconData };
      if (formattedData.position) formattedData.position = JSON.stringify(formattedData.position);
      if (formattedData.size) formattedData.size = JSON.stringify(formattedData.size);
      if (formattedData.data) formattedData.data = JSON.stringify(formattedData.data);
      if (formattedData.metadata) formattedData.metadata = JSON.stringify(formattedData.metadata);

      const response = await axios.post('/api/floorplan-icons', formattedData);
      return response.data;
    } catch (error) {
      console.error('Error creating floorplan icon:', error);
      throw error;
    }
  }

  /**
   * Update a floorplan icon
   * @param {string} id Icon ID
   * @param {Object} iconData Icon data
   * @returns {Promise<Object>} Updated icon
   */
  async updateFloorplanIcon(id, iconData) {
    try {
      // Convert objects to JSON strings
      const formattedData = { ...iconData };
      if (formattedData.position) formattedData.position = JSON.stringify(formattedData.position);
      if (formattedData.size) formattedData.size = JSON.stringify(formattedData.size);
      if (formattedData.data) formattedData.data = JSON.stringify(formattedData.data);
      if (formattedData.metadata) formattedData.metadata = JSON.stringify(formattedData.metadata);

      const response = await axios.put(`/api/floorplan-icons/${id}`, formattedData);
      return response.data;
    } catch (error) {
      console.error(`Error updating floorplan icon ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a floorplan icon
   * @param {string} id Icon ID
   * @returns {Promise<Object>} Response message
   */
  async deleteFloorplanIcon(id) {
    try {
      const response = await axios.delete(`/api/floorplan-icons/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting floorplan icon ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update floorplan icon data
   * @param {string} id Icon ID
   * @param {Object} data Icon data
   * @returns {Promise<Object>} Updated icon
   */
  async updateFloorplanIconData(id, data) {
    try {
      const response = await axios.patch(`/api/floorplan-icons/${id}/data`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating floorplan icon data ${id}:`, error);
      throw error;
    }
  }

  /**
   * Bulk create floorplan icons
   * @param {Array} icons Array of icon data
   * @returns {Promise<Array>} Created icons
   */
  async bulkCreateFloorplanIcons(icons) {
    try {
      const response = await axios.post('/api/floorplan-icons/bulk', { icons });
      return response.data;
    } catch (error) {
      console.error('Error bulk creating floorplan icons:', error);
      throw error;
    }
  }

  /**
   * Bulk update floorplan icon positions
   * @param {Array} icons Array of icons with positions
   * @returns {Promise<Array>} Updated icons
   */
  async bulkUpdateFloorplanIconPositions(icons) {
    try {
      // Ensure each icon has valid position with x and y coordinates
      const validatedIcons = icons.map(icon => {
        // Make a copy of the icon to avoid modifying the original
        const validatedIcon = { ...icon };

        // Ensure position exists and has x and y properties
        if (!validatedIcon.position) {
          validatedIcon.position = { x: 0, y: 0 };
        } else {
          // Ensure x and y are defined
          validatedIcon.position = {
            ...validatedIcon.position,
            x: validatedIcon.position.x !== undefined ? validatedIcon.position.x : 0,
            y: validatedIcon.position.y !== undefined ? validatedIcon.position.y : 0
          };
        }

        return validatedIcon;
      });

      const response = await axios.put('/api/floorplan-icons/bulk/positions', { icons: validatedIcons });
      return response.data;
    } catch (error) {
      console.error('Error bulk updating floorplan icon positions:', error);
      throw error;
    }
  }

  /**
   * Get the status of all building systems
   * @returns {Promise<Object>} System statuses
   */
  async getSystemStatus() {
    try {
      const response = await axios.get('/api/building-management/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching building management system status:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Building Management API not found, returning mock data');
        return this.getMockSystemStatus();
      }

      throw error;
    }
  }

  /**
   * Get a list of all doors from access control systems
   * @returns {Promise<Array>} List of doors
   */
  async getDoors() {
    try {
      const response = await axios.get('/api/building-management/doors');
      return response.data;
    } catch (error) {
      console.error('Error fetching doors:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Building Management API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }
  
  /**
   * Control a door (lock, unlock, passage mode)
   * @param {string} doorId - ID of the door to control
   * @param {string} action - Action to perform ('lock', 'unlock', 'passage')
   * @param {string} source - Source system ('lenelS2NetBox', 'unifiAccess')
   * @returns {Promise<Object>} Response data
   */
  async controlDoor(doorId, action, source) {
    try {
      const response = await axios.post(`/api/building-management/doors/${doorId}/control`, {
        action,
        source
      });
      return response.data;
    } catch (error) {
      console.error(`Error controlling door ${doorId} (${action}):`, error);
      
      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Building Management API not found, returning mock data');
        return {
          success: true,
          doorId,
          action,
          source,
          status: action === 'unlock' ? 'unlocked' : action === 'lock' ? 'locked' : 'passage',
          timestamp: new Date().toISOString()
        };
      }
      
      throw error;
    }
  }
  
  /**
   * Unlock a door
   * @param {string} doorId - ID of the door to unlock
   * @param {string} source - Source system ('lenelS2NetBox', 'unifiAccess')
   * @returns {Promise<Object>} Response data
   */
  async unlockDoor(doorId, source) {
    return this.controlDoor(doorId, 'unlock', source);
  }
  
  /**
   * Lock a door
   * @param {string} doorId - ID of the door to lock
   * @param {string} source - Source system ('lenelS2NetBox', 'unifiAccess')
   * @returns {Promise<Object>} Response data
   */
  async lockDoor(doorId, source) {
    return this.controlDoor(doorId, 'lock', source);
  }
  
  /**
   * Set a door to passage mode
   * @param {string} doorId - ID of the door to set to passage mode
   * @param {string} source - Source system ('lenelS2NetBox', 'unifiAccess')
   * @returns {Promise<Object>} Response data
   */
  async setDoorPassageMode(doorId, source) {
    return this.controlDoor(doorId, 'passage', source);
  }

  /**
   * Get a list of all climate control devices
   * @returns {Promise<Array>} List of climate devices
   */
  async getClimateDevices() {
    try {
      const response = await axios.get('/api/building-management/climate-devices');
      return response.data;
    } catch (error) {
      console.error('Error fetching climate devices:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Building Management API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }

  /**
   * Get a list of all security cameras
   * @returns {Promise<Array>} List of cameras
   */
  async getCameras() {
    try {
      const response = await axios.get('/api/building-management/cameras');
      return response.data;
    } catch (error) {
      console.error('Error fetching cameras:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Building Management API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }

  /**
   * Get a list of all network devices
   * @returns {Promise<Array>} List of network devices
   */
  async getNetworkDevices() {
    try {
      const response = await axios.get('/api/building-management/network-devices');
      return response.data;
    } catch (error) {
      console.error('Error fetching network devices:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Building Management API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }

  /**
   * Save building management settings
   * @param {Object} settings Settings to save
   * @returns {Promise<Object>} Saved settings
   */
  async saveSettings(settings) {
    try {
      const response = await axios.post('/api/building-management/settings', settings);
      return response.data;
    } catch (error) {
      console.error('Error saving building management settings:', error);
      throw error;
    }
  }

  /**
   * Get building management settings
   * @returns {Promise<Object>} Settings
   */
  async getSettings() {
    try {
      const response = await axios.get('/api/building-management/settings');
      return response.data;
    } catch (error) {
      console.error('Error fetching building management settings:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Building Management API not found, returning mock data');
        return {
          dashboardRefreshInterval: 60,
          defaultView: 'dashboard',
          notifications: {
            email: true,
            push: true
          },
          automation: {
            enabled: true
          }
        };
      }

      throw error;
    }
  }

  /**
   * Get automation rules
   * @returns {Promise<Array>} List of automation rules
   */
  async getAutomationRules() {
    try {
      const response = await axios.get('/api/building-management/automation/rules');
      return response.data;
    } catch (error) {
      console.error('Error fetching automation rules:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Building Management API not found, returning mock data');
        return this.getMockAutomationRules();
      }

      throw error;
    }
  }

  /**
   * Save an automation rule
   * @param {Object} rule Rule to save
   * @returns {Promise<Object>} Saved rule
   */
  async saveAutomationRule(rule) {
    try {
      const response = await axios.post('/api/building-management/automation/rules', rule);
      return response.data;
    } catch (error) {
      console.error('Error saving automation rule:', error);
      throw error;
    }
  }

  /**
   * Update an automation rule
   * @param {string} ruleId ID of the rule to update
   * @param {Object} rule Updated rule data
   * @returns {Promise<Object>} Updated rule
   */
  async updateAutomationRule(ruleId, rule) {
    try {
      const response = await axios.put(`/api/building-management/automation/rules/${ruleId}`, rule);
      return response.data;
    } catch (error) {
      console.error(`Error updating automation rule ${ruleId}:`, error);
      throw error;
    }
  }

  /**
   * Delete an automation rule
   * @param {string} ruleId ID of the rule to delete
   * @returns {Promise<Object>} Response data
   */
  async deleteAutomationRule(ruleId) {
    try {
      const response = await axios.delete(`/api/building-management/automation/rules/${ruleId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting automation rule ${ruleId}:`, error);
      throw error;
    }
  }

  /**
   * Get historical data for building systems
   * @param {Object} params Query parameters (timeRange, systems, etc.)
   * @returns {Promise<Object>} Historical data
   */
  async getHistoricalData(params = {}) {
    try {
      const response = await axios.get('/api/building-management/historical-data', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching historical data:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Building Management API not found, returning mock data');
        return this.getMockHistoricalData(params);
      }

      throw error;
    }
  }

  // Mock data methods for development
  getMockIntegrationsStatus() {
    return {
      dreo: { 
        active: true,
        lastChecked: new Date().toISOString(),
        message: 'Connected to Dreo API'
      },
      lenelS2NetBox: { 
        active: true,
        lastChecked: new Date().toISOString(),
        message: 'Connected to Lenel S2 NetBox'
      },
      unifiAccess: { 
        active: true,
        lastChecked: new Date().toISOString(),
        message: 'Connected to UniFi Access'
      },
      unifiNetwork: { 
        active: true,
        lastChecked: new Date().toISOString(),
        message: 'Connected to UniFi Network'
      },
      unifiProtect: { 
        active: true,
        lastChecked: new Date().toISOString(),
        message: 'Connected to UniFi Protect'
      },
      WiiM: { 
        active: true,
        lastChecked: new Date().toISOString(),
        message: 'Connected to WiiM'
      }
    };
  }

  getMockSystemStatus() {
    return {
      accessControl: { 
        status: 'operational', 
        message: 'All access control systems are operational' 
      },
      climate: { 
        status: 'operational', 
        message: 'All climate control systems are operational' 
      },
      security: { 
        status: 'warning', 
        message: '2 cameras offline' 
      },
      network: { 
        status: 'operational', 
        message: 'Network is fully operational' 
      }
    };
  }

  getMockAutomationRules() {
    return [
      {
        id: '1',
        name: 'Weekday Morning Warm-up',
        description: 'Increase temperature in office areas on weekday mornings',
        enabled: true,
        conditions: {
          time: {
            days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            startTime: '07:00',
            endTime: '09:00'
          }
        },
        actions: {
          climate: {
            targetTemperature: 72,
            zones: ['office-east', 'office-west']
          }
        },
        createdAt: '2023-01-15T08:00:00Z',
        updatedAt: '2023-01-15T08:00:00Z'
      },
      {
        id: '2',
        name: 'Evening Security Lock',
        description: 'Lock all exterior doors at night',
        enabled: true,
        conditions: {
          time: {
            days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
            startTime: '19:00',
            endTime: '19:15'
          }
        },
        actions: {
          access: {
            lockDoors: ['main-entrance', 'side-entrance', 'loading-dock']
          }
        },
        createdAt: '2023-01-15T08:00:00Z',
        updatedAt: '2023-01-15T08:00:00Z'
      },
      {
        id: '3',
        name: 'Weekend Energy Saver',
        description: 'Reduce climate control on weekends',
        enabled: true,
        conditions: {
          time: {
            days: ['saturday', 'sunday'],
            allDay: true
          }
        },
        actions: {
          climate: {
            mode: 'eco',
            zones: ['all']
          }
        },
        createdAt: '2023-01-15T08:00:00Z',
        updatedAt: '2023-01-15T08:00:00Z'
      }
    ];
  }

  getMockHistoricalData(params = {}) {
    // Generate some mock historical data based on the requested parameters
    const now = new Date();
    const data = {
      timeRange: params.timeRange || '24h',
      systems: params.systems || ['climate', 'access', 'security', 'network'],
      dataPoints: []
    };

    // Generate data points for the last 24 hours (or requested time range)
    const hours = params.timeRange === '7d' ? 168 : params.timeRange === '30d' ? 720 : 24;

    for (let i = 0; i < hours; i++) {
      const timestamp = new Date(now.getTime() - (i * 60 * 60 * 1000));

      const dataPoint = {
        timestamp: timestamp.toISOString(),
        climate: {
          temperature: 70 + Math.sin(i / 12 * Math.PI) * 5, // Simulate temperature fluctuation
          humidity: 45 + Math.sin(i / 12 * Math.PI) * 10,
          energyUsage: 2.5 + Math.random() * 1.5
        },
        access: {
          doorsLocked: i >= 8 && i <= 18 ? 3 : 12, // Fewer doors locked during business hours
          accessEvents: i >= 8 && i <= 18 ? Math.floor(Math.random() * 10) : Math.floor(Math.random() * 2)
        },
        security: {
          motionEvents: i >= 8 && i <= 18 ? Math.floor(Math.random() * 15) : Math.floor(Math.random() * 3),
          alarms: Math.random() > 0.95 ? 1 : 0 // Occasional alarm
        },
        network: {
          bandwidth: i >= 8 && i <= 18 ? 50 + Math.random() * 30 : 10 + Math.random() * 15,
          activeDevices: i >= 8 && i <= 18 ? 30 + Math.floor(Math.random() * 20) : 5 + Math.floor(Math.random() * 10)
        }
      };

      data.dataPoints.push(dataPoint);
    }

    return data;
  }
}

export default new BuildingManagementService();
