import axios from 'axios';

/**
 * Access Control API Service
 * Handles client-side API calls to the unified access control endpoints
 * that combine Unifi Access and Lenel S2 NetBox
 */
const accessControlService = {
  /**
   * Get configuration status for all access control systems
   * @returns {Promise<Object>} Configuration status for all systems
   */
  getConfigStatus: async () => {
    try {
      const response = await axios.get('/api/access-control/config');
      return response.data;
    } catch (error) {
      console.error('Error fetching access control configuration status:', error);
      throw error;
    }
  },

  /**
   * Get all doors/portals from all access control systems
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of doors/portals from all systems
   */
  getAllDoors: async (params = {}) => {
    try {
      const response = await axios.get('/api/access-control/doors', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching all doors/portals:', error);
      throw error;
    }
  },

  /**
   * Control a door/portal in the specified access control system
   * @param {string} doorId Door/portal ID
   * @param {string} system System identifier ('unifi-access' or 'lenel-s2-netbox')
   * @param {string} action Action to perform ('unlock', 'lock', or 'momentary-unlock')
   * @returns {Promise<Object>} Response message
   */
  controlDoor: async (doorId, system, action) => {
    try {
      const response = await axios.post('/api/access-control/doors/control', {
        doorId,
        system,
        action
      });
      return response.data;
    } catch (error) {
      console.error(`Error controlling door ${doorId} in ${system}:`, error);
      throw error;
    }
  },

  /**
   * Get all users from all access control systems
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of users from all systems
   */
  getAllUsers: async (params = {}) => {
    try {
      const response = await axios.get('/api/access-control/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching all users:', error);
      throw error;
    }
  },

  /**
   * Get a user by ID from all access control systems
   * @param {string} userId User ID
   * @returns {Promise<Object>} User data from all systems
   */
  getUserById: async (userId) => {
    try {
      const response = await axios.get(`/api/access-control/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new user in selected access control systems
   * @param {Object} userData User data
   * @param {string} userData.firstName First name
   * @param {string} userData.lastName Last name
   * @param {string} userData.email Email address
   * @param {string} userData.phone Phone number
   * @param {string} userData.department Department
   * @param {string} userData.title Job title
   * @param {Array<string>} userData.systems Systems to create the user in
   * @param {Object} userData.accessLevels Access levels for each system
   * @param {Array} userData.cards Cards to assign to the user
   * @returns {Promise<Object>} Response message
   */
  createUser: async (userData) => {
    try {
      const response = await axios.post('/api/access-control/users', userData);
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  /**
   * Update a user in all provisioned systems
   * @param {string} userId User ID
   * @param {Object} userData User data to update
   * @returns {Promise<Object>} Response message
   */
  updateUser: async (userId, userData) => {
    try {
      const response = await axios.put(`/api/access-control/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error(`Error updating user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Delete a user from all provisioned systems
   * @param {string} userId User ID
   * @returns {Promise<Object>} Response message
   */
  deleteUser: async (userId) => {
    try {
      const response = await axios.delete(`/api/access-control/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Enable or disable a user across all provisioned systems
   * @param {string} userId User ID
   * @param {Object} statusData Status data
   * @param {boolean} statusData.enabled Whether the user should be enabled
   * @returns {Promise<Object>} Response message
   */
  updateUserStatus: async (userId, statusData) => {
    try {
      const response = await axios.put(`/api/access-control/users/${userId}/status`, statusData);
      return response.data;
    } catch (error) {
      console.error(`Error updating status for user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Get all access levels from all access control systems
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of access levels from all systems
   */
  getAllAccessLevels: async (params = {}) => {
    try {
      const response = await axios.get('/api/access-control/access-levels', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching all access levels:', error);
      throw error;
    }
  },

  /**
   * Get all schedules from all access control systems
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of schedules from all systems
   */
  getAllSchedules: async (params = {}) => {
    try {
      const response = await axios.get('/api/access-control/schedules', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching all schedules:', error);
      throw error;
    }
  },

  /**
   * Create a new schedule in selected access control systems
   * @param {Object} scheduleData Schedule data
   * @param {string} scheduleData.name Schedule name
   * @param {string} scheduleData.description Schedule description
   * @param {Array} scheduleData.timeWindows Time windows for the schedule
   * @param {Array} scheduleData.doors Doors to apply the schedule to
   * @param {Array<string>} scheduleData.systems Systems to create the schedule in
   * @returns {Promise<Object>} Response message
   */
  createSchedule: async (scheduleData) => {
    try {
      const response = await axios.post('/api/access-control/schedules', scheduleData);
      return response.data;
    } catch (error) {
      console.error('Error creating schedule:', error);
      throw error;
    }
  },

  /**
   * Get all holidays from all access control systems
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of holidays from all systems
   */
  getAllHolidays: async (params = {}) => {
    try {
      const response = await axios.get('/api/access-control/holidays', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching all holidays:', error);
      throw error;
    }
  },

  /**
   * Create a new holiday in selected access control systems
   * @param {Object} holidayData Holiday data
   * @param {string} holidayData.name Holiday name
   * @param {string} holidayData.date Holiday date
   * @param {string} holidayData.description Holiday description
   * @param {Array<string>} holidayData.systems Systems to create the holiday in
   * @returns {Promise<Object>} Response message
   */
  createHoliday: async (holidayData) => {
    try {
      const response = await axios.post('/api/access-control/holidays', holidayData);
      return response.data;
    } catch (error) {
      console.error('Error creating holiday:', error);
      throw error;
    }
  },

  /**
   * Bulk update access levels for multiple users
   * @param {Array<string>} userIds Array of user IDs to update
   * @param {Object} accessLevels Access levels to update
   * @param {Array<string>} accessLevels.unifiAccess Unifi Access access level IDs
   * @param {Array<string>} accessLevels.lenelS2NetBox Lenel S2 NetBox access level IDs
   * @param {string} updateMode Update mode ('add', 'remove', or 'replace')
   * @returns {Promise<Object>} Response message
   */
  bulkUpdateAccessLevels: async (userIds, accessLevels, updateMode) => {
    try {
      const response = await axios.post('/api/access-control/users/bulk-update-access-levels', {
        userIds,
        accessLevels,
        updateMode
      });
      return response.data;
    } catch (error) {
      console.error('Error bulk updating access levels:', error);
      throw error;
    }
  }
};

export default accessControlService;