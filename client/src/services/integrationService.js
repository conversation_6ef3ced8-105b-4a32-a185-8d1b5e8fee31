import axios from 'axios';

/**
 * Integration Management Service
 * Handles client-side API calls for managing integrations
 */
const integrationService = {
  /**
   * Get all available integrations
   * @returns {Promise<Array>} List of available integrations
   */
  getAvailableIntegrations: async () => {
    try {
      const response = await axios.get('/api/integrations/available');
      // Ensure we always return an array
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error('Error fetching available integrations:', error);
      throw error;
    }
  },

  /**
   * Get all integration configurations (admin only)
   * @returns {Promise<Array>} List of integration configurations
   */
  getAllConfigurations: async () => {
    try {
      const response = await axios.get('/api/integrations/configs');
      // Ensure we always return an array
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error('Error fetching integration configurations:', error);
      throw error;
    }
  },

  /**
   * Get user's integration preferences
   * @returns {Promise<Object>} User's integration preferences
   */
  getUserPreferences: async () => {
    try {
      const response = await axios.get('/api/integrations/preferences');
      return response.data;
    } catch (error) {
      console.error('Error fetching user integration preferences:', error);
      throw error;
    }
  },

  /**
   * Update user's integration preferences
   * @param {Object} preferences Updated preferences
   * @returns {Promise<Object>} Updated user preferences
   */
  updateUserPreferences: async (preferences) => {
    try {
      const response = await axios.put('/api/integrations/preferences', preferences);
      return response.data;
    } catch (error) {
      console.error('Error updating user integration preferences:', error);
      throw error;
    }
  },

  /**
   * Get configuration for a specific integration
   * @param {string} integrationId Integration ID
   * @returns {Promise<Object>} Integration configuration
   */
  getIntegrationConfig: async (integrationId) => {
    try {
      const response = await axios.get(`/api/integrations/${integrationId}/config`);
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error(`Error fetching ${integrationId} configuration:`, error);
      throw error;
    }
  },

  /**
   * Save configuration for a specific integration (admin only)
   * @param {string} integrationId Integration ID
   * @param {Object} config Configuration object
   * @returns {Promise<Object>} Response message
   */
  saveIntegrationConfig: async (integrationId, config) => {
    try {
      const response = await axios.post(`/api/integrations/${integrationId}/config`, config);
      return response.data;
    } catch (error) {
      console.error(`Error saving ${integrationId} configuration:`, error);
      throw error;
    }
  },

  /**
   * Get integration status
   * @returns {Promise<Array>} Integration status list
   */
  getIntegrationStatus: async () => {
    try {
      const response = await axios.get('/api/integration-status');
      // Ensure we always return an array
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error('Error fetching integration status:', error);
      throw error;
    }
  },

  /**
   * Get all integration settings (admin only)
   * @returns {Promise<Array>} List of integration settings
   */
  getAllSettings: async () => {
    try {
      const response = await axios.get('/api/integrations/settings');
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error('Error fetching integration settings:', error);
      throw error;
    }
  },

  /**
   * Get settings for a specific integration
   * @param {string} integrationId Integration ID
   * @returns {Promise<Object>} Integration settings
   */
  getIntegrationSettings: async (integrationId) => {
    try {
      const response = await axios.get(`/api/integrations/settings/${integrationId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching settings for integration ${integrationId}:`, error);
      throw error;
    }
  },

  /**
   * Update settings for a specific integration (admin only)
   * @param {string} integrationId Integration ID
   * @param {Object} settings Settings object with isRequired, isReadOnly, and useGlobalConfig
   * @returns {Promise<Object>} Updated integration settings
   */
  updateIntegrationSettings: async (integrationId, settings) => {
    try {
      const response = await axios.put(`/api/integrations/settings/${integrationId}`, settings);
      return response.data;
    } catch (error) {
      console.error(`Error updating settings for integration ${integrationId}:`, error);
      throw error;
    }
  },

  /**
   * Get user account status across all integrations
   * @param {string} userId User ID
   * @returns {Promise<Object>} Account status for each integration
   */
  getUserAccountStatus: async (userId) => {
    try {
      const response = await axios.get(`/api/user-provisioning/${userId}/status`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user account status:', error);
      throw error;
    }
  },

  /**
   * Provision user account in an integration
   * @param {string} userId User ID
   * @param {string} integrationId Integration ID
   * @param {Object} userData User data for provisioning
   * @returns {Promise<Object>} Provisioning result
   */
  provisionUserAccount: async (userId, integrationId, userData = {}) => {
    try {
      const response = await axios.post(`/api/user-provisioning/${userId}/${integrationId}`, userData);
      return response.data;
    } catch (error) {
      console.error(`Error provisioning user account in ${integrationId}:`, error);
      throw error;
    }
  },

  /**
   * Deprovision user account from an integration
   * @param {string} userId User ID
   * @param {string} integrationId Integration ID
   * @returns {Promise<Object>} Deprovisioning result
   */
  deprovisionUserAccount: async (userId, integrationId) => {
    try {
      const response = await axios.delete(`/api/user-provisioning/${userId}/${integrationId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deprovisioning user account from ${integrationId}:`, error);
      throw error;
    }
  }
};

export default integrationService;
