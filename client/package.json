{"name": "csfportal-client", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/icons-material": "^5.11.0", "@mui/material": "^5.11.4", "@mui/x-date-pickers": "^6.18.6", "@react-buddy/ide-toolbox": "^2.4.0", "@react-buddy/palette-mui": "^5.0.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.2.2", "luxon": "^3.7.1", "moment": "^2.29.4", "react": "^18.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-grid-layout": "^1.3.4", "react-helmet-async": "^1.3.0", "react-quill": "^2.0.0", "react-resizable": "^3.0.5", "react-router-dom": "^6.6.2", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "devDependencies": {"http-proxy-middleware": "^2.0.6", "react-app-rewired": "^2.2.1"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:6000"}