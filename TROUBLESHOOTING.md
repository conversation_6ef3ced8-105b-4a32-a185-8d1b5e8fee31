# Gmail Ticketing Setup Troubleshooting

## Common Setup Errors and Solutions

### Error: "Cannot destructure property 'tokens' of '(intermediate value)' as it is undefined"

**Cause:** This error occurs during OAuth2 token exchange when the authorization code is invalid, expired, or incorrectly formatted.

**Solutions:**

#### Option 1: Use the Simplified Setup (Recommended)
```bash
# Run the simple setup instead
npm run setup-gmail-simple
# OR
node setup-gmail-simple.js
```

The simplified setup avoids OAuth2 complexity by using service account authentication only.

#### Option 2: Fix OAuth2 Issues
If you want to use the advanced OAuth2 setup:

1. **Check Authorization Code:**
   - Make sure you copied the entire authorization code
   - The code should be used immediately (expires in ~10 minutes)
   - Don't include extra spaces or characters

2. **Verify OAuth2 Credentials:**
   - Ensure you downloaded "OAuth 2.0 Client IDs" not "Service Account" credentials
   - The file should contain `"installed"` or `"web"` section
   - Application type should be "Desktop application"

3. **Re-create OAuth2 Credentials:**
   ```bash
   # Delete existing files and start fresh
   rm gmail-setup-credentials.json
   rm gmail-setup-token.json
   
   # Run setup again
   npm run setup-gmail-ticketing
   ```

### Error: "Authentication failed" or "Permission denied"

**Solutions:**

1. **Check Google Cloud Authentication:**
   ```bash
   gcloud auth list
   gcloud auth login  # If not authenticated
   ```

2. **Verify Project Access:**
   ```bash
   gcloud config get-value project
   gcloud config set project YOUR_PROJECT_ID
   ```

3. **Check Required Permissions:**
   - Your Google account needs Project Editor or Owner role
   - Enable required APIs manually if needed

### Error: "API not enabled"

**Solutions:**

1. **Enable APIs Manually:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/apis/library)
   - Enable these APIs:
     - Gmail API
     - Cloud Pub/Sub API
     - Identity and Access Management (IAM) API

2. **Use gcloud to Enable:**
   ```bash
   gcloud services enable gmail.googleapis.com
   gcloud services enable pubsub.googleapis.com
   gcloud services enable iam.googleapis.com
   ```

### Error: "Service account creation failed"

**Solutions:**

1. **Check Permissions:**
   ```bash
   # Verify you have the necessary IAM permissions
   gcloud projects get-iam-policy YOUR_PROJECT_ID
   ```

2. **Manual Service Account Creation:**
   - Go to [IAM & Admin > Service Accounts](https://console.cloud.google.com/iam-admin/serviceaccounts)
   - Create service account manually
   - Grant these roles:
     - Pub/Sub Admin
     - Logs Writer
   - Generate and download JSON key

### Error: "Topic already exists" or "Subscription already exists"

**Solution:** This is usually not an error - the setup script handles existing resources gracefully.

If you need to recreate resources:
```bash
# Delete existing resources
gcloud pubsub subscriptions delete gmail-tickets-sub
gcloud pubsub topics delete gmail-tickets

# Run setup again
npm run setup-gmail-simple
```

## Quick Setup Verification

After setup, verify everything is working:

1. **Check Environment Variables:**
   ```bash
   grep -E "GOOGLE_GMAIL|SMTP|TICKET_EMAIL" .env
   ```

2. **Test Service Account Key:**
   ```bash
   cat gmail-ticketing-service-account-key.json | head -5
   ```

3. **Verify Pub/Sub Resources:**
   ```bash
   gcloud pubsub topics list
   gcloud pubsub subscriptions list
   ```

4. **Test Application Startup:**
   ```bash
   npm run dev
   # Check logs for Gmail webhook initialization
   ```

## Alternative Setup Methods

If you continue having issues with Google Cloud setup:

### Method 1: IMAP Polling (Simplest)
Add to your `.env` file:
```env
# IMAP Configuration (polls every 2 minutes)
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=your-app-password

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
TICKET_EMAIL_FROM=<EMAIL>
```

### Method 2: Email Forwarding
- Set up email forwarding to a service like Mailgun
- Configure webhook from the service to your portal
- Simpler than Google Cloud setup

## Getting Help

1. **Check Setup Logs:** The setup scripts provide detailed error messages
2. **Verify Prerequisites:** Ensure Google Cloud CLI is installed and authenticated
3. **Test Components:** Test each component (authentication, APIs, permissions) individually
4. **Use Simple Setup:** When in doubt, use the simplified service account setup

## Manual Verification Steps

If automated setup fails, you can verify each step manually:

1. **Google Cloud Project:** https://console.cloud.google.com/
2. **APIs Enabled:** https://console.cloud.google.com/apis/dashboard
3. **Service Accounts:** https://console.cloud.google.com/iam-admin/serviceaccounts
4. **Pub/Sub Topics:** https://console.cloud.google.com/cloudpubsub/topic/list
5. **IAM Permissions:** https://console.cloud.google.com/iam-admin/iam

The ticketing system will work with any of these setup methods - choose the one that works best for your environment!