# Gmail OAuth2 Email Implementation

## Overview

This document describes the implementation of Gmail OAuth2 for sending emails in the CSF Portal application. The email system has been updated to use Google OAuth2 authentication instead of traditional SMTP authentication, with a fallback to SMTP if OAuth2 authentication fails.

## Changes Made

1. Modified the email utility (`server/utils/emailTemplates/emailUtils.js`) to:
   - Use Gmail OAuth2 for authentication when sending emails
   - Add detailed logging for debugging OAuth2 issues
   - Implement a fallback mechanism to use SMTP if OAuth2 authentication fails
   - Handle token refresh events

2. Added methods to the EmailUtils class:
   - `initializeTransporter()`: Sets up the email transporter with <PERSON>Auth2 or falls back to SMTP
   - `initializeSMTPTransporter()`: Sets up the email transporter with SMTP (used as fallback)

3. Enhanced the `sendTemplatedEmail()` method to:
   - Attempt to send emails using OAuth2 first
   - Dynamically fall back to SMTP if OAuth2 authentication fails at runtime
   - Provide detailed error logging

## Configuration

The following environment variables are used for Gmail OAuth2 configuration:

```
# Gmail OAuth2 Configuration
GMAIL_OAUTH_CLIENT_ID=your_client_id
GMAIL_OAUTH_CLIENT_SECRET=your_client_secret
GMAIL_OAUTH_REFRESH_TOKEN=your_refresh_token
```

The existing SMTP configuration is used as a fallback:

```
# Email configuration (SMTP fallback)
EMAIL_HOST=smtp.office365.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password_here
EMAIL_FROM=<EMAIL>
```

## Setting Up Gmail OAuth2

To properly set up Gmail OAuth2 for sending emails, follow these steps:

1. **Create OAuth2 credentials in Google Cloud Console**:
   - Go to the [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select an existing one
   - Navigate to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth client ID"
   - Select "Web application" as the application type
   - Add authorized redirect URIs (e.g., `https://developers.google.com/oauthplayground`)
   - Note the Client ID and Client Secret

2. **Generate a Refresh Token**:
   - Go to the [OAuth 2.0 Playground](https://developers.google.com/oauthplayground/)
   - Click the gear icon in the top right and check "Use your own OAuth credentials"
   - Enter your Client ID and Client Secret
   - Select the required scopes: `https://mail.google.com/`
   - Click "Authorize APIs" and follow the authorization flow
   - Click "Exchange authorization code for tokens"
   - Note the Refresh Token

3. **Update Environment Variables**:
   - Add the Client ID, Client Secret, and Refresh Token to your environment variables
   - Ensure the EMAIL_FROM variable matches the Gmail account used for OAuth2

## Troubleshooting

If you encounter issues with Gmail OAuth2 authentication:

1. **Check OAuth2 Credentials**:
   - Verify that the Client ID, Client Secret, and Refresh Token are correct
   - Ensure the Gmail account has not revoked access to the application

2. **Check Console Logs**:
   - Look for "Initializing Gmail OAuth2 with user:" log messages
   - Check if "OAuth2 client ID available:", "OAuth2 client secret available:", and "OAuth2 refresh token available:" all show as true
   - Look for "New access token generated for:" messages, which indicate successful token refresh

3. **SMTP Fallback**:
   - If OAuth2 authentication fails, the system will automatically fall back to SMTP
   - Check the SMTP credentials if both OAuth2 and SMTP authentication fail

## Security Considerations

1. **Token Storage**:
   - Store OAuth2 tokens securely in environment variables or a secure vault
   - Never commit tokens to version control

2. **Permissions**:
   - Use the minimum required scopes for Gmail OAuth2 (`https://mail.google.com/`)
   - Regularly review and audit application access

3. **Token Refresh**:
   - The system automatically handles token refresh
   - If the refresh token expires, you'll need to generate a new one

## References

- [Nodemailer OAuth2 Documentation](https://nodemailer.com/smtp/oauth2/)
- [Google OAuth2 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Gmail API Documentation](https://developers.google.com/gmail/api/guides)