# Deploying CSF Portal to Digital Ocean App Platform

This document outlines the steps to deploy the CSF Portal application to Digital Ocean App Platform.

## Prerequisites

Before deploying to Digital Ocean App Platform, ensure you have:

1. A Digital Ocean account
2. A MongoDB database (either hosted on Digital Ocean or another provider like MongoDB Atlas)
3. Google OAuth credentials for authentication
4. Access to the CSF Portal GitHub repository

## Deployment Steps

### 1. Create a MongoDB Database

If you don't already have a MongoDB database, you can create one using Digital Ocean's Managed Databases or MongoDB Atlas:

#### Using MongoDB Atlas:
1. Sign up or log in to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. Create a new cluster
3. Configure database access (create a user with appropriate permissions)
4. Configure network access (whitelist IP addresses or allow access from anywhere)
5. Get your connection string, which will look like: `mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority`

### 2. Set Up the App on Digital Ocean App Platform

1. Log in to your Digital Ocean account
2. Navigate to the App Platform section
3. Click "Create App"
4. Select your GitHub repository and the main branch
5. Digital Ocean will detect the Dockerfile and use it to build and deploy your app
6. Configure the app settings:
   - Select the appropriate plan (Basic or Professional)
   - Configure the app name and region
   - Set up environment variables (see below)
   - Configure any additional settings as needed

### 3. Configure Environment Variables

In the Digital Ocean App Platform dashboard, add the following environment variables:

- `NODE_ENV`: Set to `production`
- `PORT`: Set to `8080`
- `MONGO_URI`: Your MongoDB connection string
- `SESSION_SECRET`: A secure random string for session encryption
- `GOOGLE_CLIENT_ID`: Your Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Your Google OAuth client secret
- `GOOGLE_CALLBACK_URL`: Set to `https://<your-app-url>.ondigitalocean.app/api/auth/google/callback`
- `GOOGLE_API_KEY`: Your Google API key
- `ALLOWED_DOMAINS`: Comma-separated list of domains allowed for Google login

### 4. Deploy the App

1. Click "Create Resources" to deploy the app
2. Digital Ocean will build and deploy your app using the Dockerfile
3. Once the deployment is complete, you can access your app at the provided URL

### 5. Update Google OAuth Configuration

After deployment, update your Google OAuth configuration to use the new callback URL:

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to your project
3. Go to "APIs & Services" > "Credentials"
4. Edit your OAuth 2.0 Client ID
5. Add the new callback URL: `https://<your-app-url>.ondigitalocean.app/api/auth/google/callback`

## Troubleshooting

### Common Issues

1. **App fails to build**: Check the build logs for errors. Common issues include:
   - Missing dependencies
   - Syntax errors in the code
   - Issues with the Dockerfile

2. **App fails to start**: Check the runtime logs for errors. Common issues include:
   - Missing environment variables
   - Database connection issues
   - Port conflicts

3. **Authentication issues**: Check that your Google OAuth configuration is correct:
   - Verify the callback URL is correct
   - Ensure the allowed domains are configured correctly
   - Check that the client ID and secret are correct

### Viewing Logs

To view logs for your app:

1. Go to the Digital Ocean App Platform dashboard
2. Select your app
3. Click on the "Logs" tab
4. Select the component (web) to view its logs

## Scaling

To scale your app:

1. Go to the Digital Ocean App Platform dashboard
2. Select your app
3. Click on the "Settings" tab
4. Under "Resources", adjust the number of instances or the instance size

## Monitoring

Digital Ocean App Platform provides basic monitoring for your app:

1. Go to the Digital Ocean App Platform dashboard
2. Select your app
3. Click on the "Insights" tab to view metrics like CPU usage, memory usage, and request count

For more advanced monitoring, consider setting up a monitoring solution like New Relic or Datadog.

## Backup and Restore

Ensure your MongoDB database is backed up regularly:

1. If using MongoDB Atlas, configure automated backups
2. If using Digital Ocean Managed Databases, configure automated backups
3. Consider implementing application-level backups for critical data

## Conclusion

Your CSF Portal application is now deployed to Digital Ocean App Platform. The app will automatically redeploy when you push changes to the main branch of your GitHub repository.

For more information on managing your app on Digital Ocean App Platform, refer to the [Digital Ocean App Platform documentation](https://docs.digitalocean.com/products/app-platform/).