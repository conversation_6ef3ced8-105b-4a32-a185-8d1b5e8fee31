# Canva OAuth Implementation Summary

This document summarizes the changes made to implement the Canva OAuth integration for the CSF Portal. The integration allows users to authenticate with Canva using a one-click authorization flow, with their authentication tied directly to their portal login.

## Overview

The implementation follows the OAuth 2.0 authorization code flow, where users are redirected to Canva's authorization page, grant permission to the application, and are then redirected back to the portal with an authorization code. The code is exchanged for access and refresh tokens, which are stored in the user's profile and used for subsequent API requests.

## Changes Made

### 1. User Model Updates

Updated the User model to store Canva OAuth credentials:

```javascript
// Added to models/User.js
canvaAccessToken: {
  type: String
},
canvaRefreshToken: {
  type: String
},
canvaTokenExpiry: {
  type: Date
}
```

These fields store the user-specific OAuth tokens and their expiration time.

### 2. CanvaAPI Implementation Updates

Enhanced the CanvaAPI class to support OAuth authentication:

- Modified the constructor to accept OAuth parameters (clientId, clientSecret, redirectUri, userTokens, userId)
- Added methods for OAuth flow:
  - `getAuthUrl()` - Generates an authorization URL
  - `exchangeCode()` - Exchanges an authorization code for tokens
  - `refreshToken()` - Refreshes an access token using a refresh token
- Updated the `initialize()` method to check and refresh user tokens if needed
- Added token handling to API methods

### 3. Controller Updates

Updated the Canva controller with OAuth functionality:

- Added helper functions for API initialization with user tokens:
  - `getApiWithUser()` - Gets an API instance with user-specific tokens
  - `getApiInstance()` - Gets the best available API instance
- Added methods for OAuth flow:
  - `getAuthUrl()` - Generates an authorization URL
  - `handleCallback()` - Handles the OAuth callback
  - `checkAuth()` - Checks if a user is authenticated
- Updated existing API methods to use user-specific tokens
- Enhanced error handling for authentication errors

### 4. Route Updates

Updated the routes to include OAuth endpoints:

```javascript
// OAuth Routes
router.get('/auth', isAuthenticated, canvaController.getAuthUrl);
router.get('/callback', isAuthenticated, canvaController.handleCallback);
router.get('/check-auth', isAuthenticated, canvaController.checkAuth);
```

### 5. Environment Variables

Added environment variables for Canva OAuth configuration:

```
# Canva API configuration
CANVA_DOMAIN=canva.com
CANVA_API_KEY=your_canva_api_key

# Canva OAuth configuration
CANVA_CLIENT_ID=your_canva_client_id
CANVA_CLIENT_SECRET=your_canva_client_secret
CANVA_REDIRECT_URI=https://your-app-url.com/api/canva/callback
```

### 6. Documentation

Created comprehensive documentation:

- Updated `CANVA_API_SETUP.md` with OAuth setup instructions
- Created `CANVA_OAUTH_FRONTEND_IMPLEMENTATION.md` with frontend implementation guide
- Created `CANVA_OAUTH_IMPLEMENTATION_SUMMARY.md` (this document)

### 7. Testing

Created a test script (`test-canva-oauth.js`) to verify the implementation:

- Tests auth URL generation
- Tests API functionality with mock tokens
- Tests user token updates

## Frontend Implementation

The frontend implementation (detailed in `CANVA_OAUTH_FRONTEND_IMPLEMENTATION.md`) includes:

1. A Canva Authorization Button component that:
   - Checks the user's authentication status
   - Provides a button to initiate the OAuth flow
   - Displays the authentication status

2. A Canva Dashboard page that:
   - Includes the authorization button
   - Displays the user's Canva designs
   - Handles loading states and errors

## User Flow

The user flow for the Canva OAuth integration is as follows:

1. User navigates to the Canva section of the portal
2. If not authenticated, they see a "Connect with Canva" button
3. When they click the button, they are redirected to Canva's authorization page
4. After granting permission, they are redirected back to the portal
5. Their Canva resources are now accessible through the portal

## Backwards Compatibility

The implementation maintains backwards compatibility with the existing API key-based authentication:

- If a user is not authenticated with Canva, the system falls back to the global API key
- The global API key can still be configured through environment variables
- Existing API endpoints continue to work with both authentication methods

## Security Considerations

The implementation includes several security measures:

- OAuth tokens are stored in the user's profile in the database, not in cookies or local storage
- Tokens are refreshed automatically when they expire
- The client secret is never exposed to the client
- The implementation follows OAuth 2.0 best practices

## Next Steps

To complete the implementation, the following steps should be taken:

1. Create a Canva Developer App and obtain OAuth credentials
2. Configure the environment variables with the OAuth credentials
3. Implement the frontend components as described in `CANVA_OAUTH_FRONTEND_IMPLEMENTATION.md`
4. Test the implementation end-to-end
5. Deploy the changes to production