# Planning Center API Include Parameter Implementation

## Overview

This document describes the changes made to support the Planning Center People API's "include" parameter functionality. The implementation allows including additional data not available in the normal attributes array and links that data to the person records.

## Changes Made

### 1. Enhanced the Planning Center API Integration

Modified the `planningCenterAPI.js` file to:

- Process included data in the API response
- Link included data to the person records using the ID from the relationships object
- Add a `linked_data` property to each person object containing the processed relationships

```javascript
// Example of how to use the include parameter
const peopleWithEmails = await planningCenterAPI.getPeople({ include: 'emails' });
```

### 2. Updated the Controller Methods

Modified the controller methods to:

- Pass query parameters to the API, which now handles the include parameter
- Process the linked data in the transformed output for the directory view
- Extract specific data from common included resources (emails, phone numbers)

## How to Test

### Basic Testing

1. Make a request to the `/api/planning-center/people` endpoint with an include parameter:

```
GET /api/planning-center/people?include=emails,phone_numbers
```

2. Verify that the response contains the included data linked to each person in the `linked_data` property.

### Directory View Testing

1. Make a request to the `/api/planning-center/people-directory` endpoint with an include parameter:

```
GET /api/planning-center/people-directory?include=emails,phone_numbers
```

2. Verify that the response contains the transformed people data with the additional data in the `additionalData` property.

## Example Response Structure

```json
{
  "data": [
    {
      "id": "12345",
      "type": "Person",
      "attributes": {
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>"
      },
      "relationships": {
        "emails": {
          "data": [
            { "type": "Email", "id": "67890" }
          ]
        }
      },
      "linked_data": {
        "emails": [
          {
            "id": "67890",
            "type": "Email",
            "attributes": {
              "address": "<EMAIL>",
              "location": "Home",
              "primary": true
            }
          }
        ]
      }
    }
  ],
  "included": [
    {
      "id": "67890",
      "type": "Email",
      "attributes": {
        "address": "<EMAIL>",
        "location": "Home",
        "primary": true
      }
    }
  ]
}
```

## Documentation Reference

For more information about the Planning Center People API and the include parameter, refer to the official documentation:
https://developer.planning.center/docs/#/apps/people/2025-07-02/vertices/person