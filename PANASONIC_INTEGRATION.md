# Panasonic Pro AV Camera Integration

This document provides information about the Panasonic Pro AV Camera integration in the CSF Portal, specifically for the AW-HE40 model.

## Overview

The Panasonic Pro AV Camera integration allows you to control and monitor Panasonic professional cameras through the CSF Portal. This integration supports:

- Getting camera information and status
- Controlling PTZ (Pan, Tilt, Zoom) functions
- Managing camera presets
- Capturing snapshots

## Configuration

### Environment Variables

The following environment variables need to be configured in your `.env` file:

```
# Panasonic Pro AV Camera configuration
PANASONIC_HOST=your_panasonic_host
PANASONIC_PORT=80
PANASONIC_USERNAME=your_panasonic_username
PANASONIC_PASSWORD=your_panasonic_password
PANASONIC_MODEL=AW-HE40
PANASONIC_PROTOCOL=http
```

- `PANASONIC_HOST`: The hostname or IP address of the Panasonic camera
- `PANASONIC_PORT`: The port number (default: 80 for HTTP, 443 for HTTPS)
- `PANASONIC_USERNAME`: The username for authentication
- `PA<PERSON><PERSON>NIC_PASSWORD`: The password for authentication
- `PANASO<PERSON>C_MODEL`: The camera model (default: AW-HE40)
- `PANASONIC_PROTOCOL`: The protocol to use (http or https, default: http)

## API Endpoints

All endpoints require authentication.

### Initialize

```
GET /api/panasonic/initialize
```

Initializes the Panasonic camera API and checks the connection.

### Camera Information

```
GET /api/panasonic/info
```

Returns information about the camera, such as model, firmware version, etc.

### Camera Status

```
GET /api/panasonic/status
```

Returns the current status of the camera, including power state, current position, etc.

### Camera Presets

```
GET /api/panasonic/presets
```

Returns a list of available camera presets.

### Move to Preset

```
GET /api/panasonic/presets/:presetId
```

Moves the camera to the specified preset position.

### Control PTZ

```
POST /api/panasonic/ptz
```

Controls the camera's pan, tilt, and zoom functions.

Request body:
```json
{
  "direction": "up",
  "speed": 50
}
```

Parameters:
- `direction`: The direction to move the camera (values: up, down, left, right, upleft, upright, downleft, downright, stop)
- `speed`: The speed of movement (range: 1-100)

### Control Zoom

```
POST /api/panasonic/zoom
```

Controls the camera's zoom function.

Request body:
```json
{
  "direction": "tele",
  "speed": 50
}
```

Parameters:
- `direction`: The direction to zoom (values: tele, wide, stop)
- `speed`: The speed of zooming (range: 1-100)

### Snapshot

```
GET /api/panasonic/snapshot
```

Returns a JPEG snapshot from the camera.

### Configuration

```
GET /api/panasonic/config
```

Returns the current configuration of the Panasonic camera integration.

## Troubleshooting

If you encounter issues with the Panasonic camera integration, check the following:

1. Ensure the camera is powered on and connected to the network
2. Verify that the environment variables are correctly set
3. Check that the username and password are correct
4. Ensure the camera is accessible from the server running the CSF Portal
5. Check the server logs for any error messages

## References

- [Panasonic AW-HE40 Product Page](https://pro-av.panasonic.net/en/products/aw-he40/)
- [Panasonic AW-HE40 API Documentation](https://pro-av.panasonic.net/en/products/aw-he40/specification.html)