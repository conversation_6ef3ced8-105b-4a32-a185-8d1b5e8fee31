# Google Admin Authentication Guide

## Issue: Google Admin Page Shows "Not Authenticated"

If you're seeing a message that Google Admin is not authenticated, despite believing it's configured through environment variables, this document will help you resolve the issue.

## Root Cause

The issue occurs because the Google Admin integration requires proper authentication with Google's APIs. There are two authentication methods available:

1. **OAuth Authentication** - Requires user interaction to complete the authentication flow
2. **Service Account Authentication** - Can run without user interaction, ideal for automated systems

Currently, your `.env` file contains placeholder values for the Google Admin credentials, not actual values. Even though the environment variables exist, they don't contain valid credentials.

## Authentication Methods

### OAuth Authentication
This method requires a user to manually authenticate through Google's OAuth flow. It's suitable for:
- Development environments
- When you want to authenticate as yourself
- When you don't need automated access

### Service Account Authentication
This method uses a service account with domain-wide delegation. It's suitable for:
- Production environments
- Automated systems that run without user interaction
- When you want to avoid the OAuth flow

## Solution

You can choose either OAuth Authentication or Service Account Authentication based on your needs.

## OAuth Authentication

### Step 1: Create OAuth Credentials

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Library" and enable the Admin SDK API
4. Go to "APIs & Services" > "Credentials" and create an OAuth 2.0 Client ID
5. Add your redirect URI (default: `http://localhost:6000/api/google-admin/callback`) as an authorized redirect URI
6. Copy the Client ID and Client Secret

### Step 2: Update Your Environment Variables for OAuth

Open your `.env` file and replace the placeholder values with your actual credentials:

```
# Google Admin configuration - OAuth method
GOOGLE_ADMIN_CLIENT_ID=your_actual_client_id_here
GOOGLE_ADMIN_CLIENT_SECRET=your_actual_client_secret_here
GOOGLE_ADMIN_REDIRECT_URI=http://localhost:6000/api/google-admin/callback
GOOGLE_ADMIN_TOKEN_PATH=./google-admin-token.json
```

### Step 3: Complete the OAuth Authentication Process

1. Restart your application to load the new environment variables
2. Navigate to the Google Admin Configuration page in your application
3. You should see a message indicating that you're not authenticated
4. Click on the "Authenticate with Google Admin" link
5. Follow the Google authentication process, making sure to use a Google account with admin privileges for your Google Workspace
6. After successful authentication, you'll be redirected back to your application
7. The Google Admin page should now show "Authenticated" status

## Service Account Authentication

### Step 1: Create a Service Account

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Library" and enable the Admin SDK API
4. Go to "IAM & Admin" > "Service Accounts" and click "Create Service Account"
5. Enter a name and description for the service account, then click "Create and Continue"
6. You can skip the "Grant this service account access to project" step by clicking "Continue"
7. Click "Done" to finish creating the service account
8. Find your new service account in the list and click on it
9. Go to the "Keys" tab and click "Add Key" > "Create new key"
10. Select "JSON" as the key type and click "Create"
11. The key file will be downloaded to your computer - keep this file secure!

### Step 2: Set Up Domain-Wide Delegation

1. In the Google Cloud Console, go to your service account
2. Click on the "Details" tab
3. Find the "Show domain-wide delegation" section and click "Edit"
4. Check the "Enable Google Workspace Domain-wide Delegation" box
5. Enter a product name for the consent screen (e.g., "CSF Portal")
6. Save the changes
7. Note the "Client ID" (a long number) - you'll need this for the next step

### Step 3: Authorize the Service Account in Google Workspace

1. Go to your [Google Workspace Admin Console](https://admin.google.com/)
2. Navigate to "Security" > "API controls"
3. In the "Domain-wide Delegation" section, click "Manage Domain Wide Delegation"
4. Click "Add new"
5. Enter the Client ID from the previous step
6. For OAuth scopes, enter the following scopes (separated by commas):
   ```
   https://www.googleapis.com/auth/admin.directory.user,https://www.googleapis.com/auth/admin.directory.group,https://www.googleapis.com/auth/admin.directory.user.security
   ```
7. Click "Authorize"

### Step 4: Update Your Environment Variables for Service Account

Open your `.env` file and add the service account credentials:

```
# Google Admin configuration - Service Account method
GOOGLE_ADMIN_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_ADMIN_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key content here...\n-----END PRIVATE KEY-----\n"
GOOGLE_ADMIN_IMPERSONATION_EMAIL=<EMAIL>
```

Notes:
- Replace `<EMAIL>` with your service account email
- Copy the entire private key from the JSON file, including the `-----BEGIN PRIVATE KEY-----` and `-----END PRIVATE KEY-----` parts
- Replace `<EMAIL>` with the email of a Google Workspace admin user that you want to impersonate

### Step 5: Restart Your Application

1. Restart your application to load the new environment variables
2. Navigate to the Google Admin Configuration page in your application
3. The Google Admin page should now show "Authenticated" status with "Using Service Account Authentication"
4. No manual authentication step is required!

## Troubleshooting

### OAuth Authentication Issues

1. **Check your credentials**: Ensure the Client ID and Client Secret are correct and from a project with the Admin SDK API enabled
2. **Check the redirect URI**: Make sure the redirect URI in your Google Cloud Console exactly matches the one in your `.env` file
3. **Check the token file**: The token is stored in the file specified by `GOOGLE_ADMIN_TOKEN_PATH`. Make sure this directory is writable
4. **Check the logs**: Look for any error messages in your application logs that might provide more information

### Service Account Authentication Issues

1. **Check your service account credentials**: Ensure the service account email and private key are correct
2. **Verify domain-wide delegation**: Make sure domain-wide delegation is properly set up in both the Google Cloud Console and Google Workspace Admin Console
3. **Check impersonation email**: The impersonation email must be a valid admin user in your Google Workspace
4. **Verify scopes**: Ensure the scopes authorized in the Google Workspace Admin Console match the scopes required by the application
5. **Check private key format**: The private key should include newlines represented as `\n` characters
6. **API enablement**: Confirm that the Admin SDK API is enabled in your Google Cloud project
7. **Check logs**: Look for specific error messages related to service account authentication in your application logs

## Additional Information

### OAuth Authentication
- The authentication token is stored in the file specified by `GOOGLE_ADMIN_TOKEN_PATH` (default: `./google-admin-token.json`)
- This token includes refresh capabilities, so once authenticated, the application should be able to maintain authentication
- If you change the required scopes in the code, you may need to re-authenticate

### Service Account Authentication
- No token file is needed for service account authentication
- The service account credentials are read directly from environment variables
- The service account must have domain-wide delegation enabled
- The impersonation email must be a Google Workspace admin user
- Service account authentication is ideal for production environments as it doesn't require user interaction

### General Security Notes
- For production environments, make sure to secure your `.env` file appropriately
- Consider using environment variable management systems that support encryption
- Regularly rotate your service account keys for better security