const jwt = require('jsonwebtoken');
const fs = require('fs');

// Debug script to validate Apple Business Manager JWT token generation
function debugAppleJWT(clientId, keyId, privateKeyPathOrContent, issuerId) {
  console.log('=== Apple Business Manager JWT Debug ===\n');
  
  // Check parameters
  console.log('1. Parameter Check:');
  console.log(`   Client ID: ${clientId ? 'PROVIDED' : 'MISSING'} (length: ${clientId?.length || 0})`);
  console.log(`   Key ID: ${keyId ? 'PROVIDED' : 'MISSING'} (length: ${keyId?.length || 0})`);
  console.log(`   Issuer ID: ${issuerId ? 'PROVIDED' : 'MISSING'} (length: ${issuerId?.length || 0})`);
  console.log(`   Private Key: ${privateKeyPathOrContent ? 'PROVIDED' : 'MISSING'}\n`);
  
  // Check if it's content or file path
  const isPrivateKeyContent = typeof privateKeyPathOrContent === 'string' && 
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  '23066096'
);
