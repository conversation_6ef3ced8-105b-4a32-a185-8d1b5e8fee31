# Planning Center API Version Clarification

## Overview

This document clarifies the relationship between the Planning Center API versioning used in the code and the versioning mentioned in the documentation URLs.

## API Versioning

The Planning Center API uses two different versioning schemes:

1. **Endpoint Versioning**: The actual API endpoints use a simple version number format (e.g., `v2`).
   - Example: `/people/v2/people`
   - Example: `/calendar/v2/resources`

2. **Documentation Versioning**: The documentation URLs use a date-based versioning scheme.
   - People API: `2025-07-02` (https://developer.planning.center/docs/#/apps/people/2025-07-02/vertices/person)
   - Calendar API: `2022-07-07` (https://developer.planning.center/docs/#/apps/calendar/2022-07-07/vertices/resource)

## Implementation Details

Our implementation correctly follows the API specifications by using the appropriate endpoint versioning (`v2`) in the API calls, while referencing the date-based versioning in the documentation comments.

### People API Methods

- `getPeople()`: Uses `/people/v2/people` endpoint
- `getPersonById()`: Uses `/people/v2/people/${personId}` endpoint

### Calendar API Methods

- `getEvents()`: Uses `/calendar/v2/events` endpoint
- `getResources()`: Uses `/calendar/v2/resources` endpoint
- `getResourceById()`: Uses `/calendar/v2/resources/${resourceId}` endpoint

## Changes Made

1. Updated the documentation in `planningCenterAPI.js` to clarify the relationship between the API version used in the code (`v2`) and the version mentioned in the documentation URL (date-based).

2. Verified that the implementation correctly follows the API specifications by using the appropriate endpoint versioning.

## Testing

The implementation was tested using a simple test script that verifies the API endpoints are correct. The test confirmed that the endpoints are valid (receiving a 401 Unauthorized error rather than a 404 Not Found error), indicating that the API versioning is correct but authentication credentials are needed.

## Conclusion

The Planning Center API integration is correctly implemented according to the API specifications. The code uses the appropriate endpoint versioning (`v2`), while the documentation references the date-based versioning scheme used in the official API documentation.