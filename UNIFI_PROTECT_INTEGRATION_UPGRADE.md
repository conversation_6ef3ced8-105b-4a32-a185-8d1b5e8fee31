# UniFi Protect Integration Upgrade

This document describes the upgrade of the UniFi Protect integration to use the `unifi-protect` npm package, which provides a more comprehensive API with additional features.

## Changes Made

1. Added the `unifi-protect` npm package (version 4.25.0) to the project dependencies
2. Refactored the UniFi Protect API implementation to use the new package
3. Maintained support for multiple Protect instances (A and B)
4. Maintained backward compatibility for legacy environment variables
5. Created a test script to verify the implementation

## New Features

The new implementation provides access to all features available in the `unifi-protect` package, including:

1. **Full access to the UniFi Protect NVR JSON** - The bootstrap data contains comprehensive information about the Protect system
2. **Improved camera management** - More detailed camera information and control options
3. **Enhanced event handling** - Better access to motion events, doorbell rings, and other events
4. **Support for additional device types** - Access to lights, sensors, and other Protect devices
5. **Improved error handling and logging** - Better debugging and error reporting

## Configuration

The integration continues to use environment variables for configuration:

### Multiple Instances (Recommended)

```
# Instance A
UNIFI_PROTECT_HOST_A=*************
UNIFI_PROTECT_API_KEY_A=your-api-key-for-instance-a

# Instance B (optional)
UNIFI_PROTECT_HOST_B=*************
UNIFI_PROTECT_API_KEY_B=your-api-key-for-instance-b
```

### Legacy Configuration (Backward Compatibility)

```
UNIFI_PROTECT_HOST=*************
UNIFI_PROTECT_USERNAME=your-username
UNIFI_PROTECT_PASSWORD=your-password
UNIFI_PROTECT_PORT=443  # Optional, defaults to 443
```

## Getting API Keys

To get an API key for your UniFi Protect instance:

1. Log in to your UniFi Protect web interface
2. Go to Settings > Users
3. Create a new local user or edit an existing one
4. Assign appropriate permissions (at least "View" for cameras)
5. Generate an API key for the user
6. Copy the API key and use it in your environment variables

## Testing the Implementation

A test script is provided to verify the implementation:

```bash
# Set up your environment variables in .env file
# Then run:
node test-unifi-protect-integration.js
```

The test script will:
1. Initialize the API
2. Get all cameras
3. Test camera-specific methods (details, snapshot, PTZ)
4. Get events
5. Get system status
6. Get viewers

## API Methods

The following methods are available in the new implementation:

### Camera Methods
- `getCameras(instanceId)` - Get all cameras from a specific instance
- `getAllCameras()` - Get all cameras from all instances
- `getCameraDetails(cameraId, instanceId)` - Get details for a specific camera
- `getCameraSnapshot(cameraId, instanceId)` - Get a snapshot image for a specific camera

### Event Methods
- `getEvents(params, instanceId)` - Get events from a specific instance with optional filtering
- `getAllEvents(params)` - Get events from all instances with optional filtering

### System Methods
- `getSystemStatus(instanceId)` - Get system status from a specific instance
- `getAllSystemStatus()` - Get system status from all instances

### PTZ Control
- `controlPTZ(cameraId, action, params, instanceId)` - Control PTZ cameras
  - Actions: `goto`, `patrol_start`, `patrol_stop`, `zoom`, `move`

### Viewer Methods
- `getViewers(instanceId)` - Get viewers from a specific instance
- `updateViewer(viewerId, settings, instanceId)` - Update viewer settings

## Advanced Usage

For advanced usage and access to additional features provided by the `unifi-protect` package, you can access the underlying API instance:

```javascript
const UnifiProtectAPI = require('./server/integrations/unifiProtect/unifiProtectAPI');
const unifiProtectAPI = new UnifiProtectAPI();

// Initialize the API
await unifiProtectAPI.initialize();

// Get the underlying API instance for a specific instance
const api = await unifiProtectAPI._getApiInstance('A');

// Now you can use all methods provided by the unifi-protect package
const bootstrap = await api.bootstrap();
```

## Troubleshooting

If you encounter issues with the new implementation:

1. Check your environment variables to ensure they are correctly set
2. Verify that your API keys are valid and have the necessary permissions
3. Run the test script to identify specific issues
4. Check the logs for error messages

If necessary, you can revert to the previous implementation by restoring the backup file:

```bash
mv /path/to/server/integrations/unifiProtect/unifiProtectAPI.js.bak /path/to/server/integrations/unifiProtect/unifiProtectAPI.js
```