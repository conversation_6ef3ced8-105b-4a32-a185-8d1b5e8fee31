# Planning Center People Page Fix

## Issue Description
The Planning Center people page was not returning all people from the API and not properly showing their names.

## Root Causes Identified
1. **Pagination Issues**: The Planning Center API integration wasn't properly handling pagination parameters, resulting in incomplete data being returned.
2. **Name Formatting Issues**: People's names weren't being properly formatted and displayed in the UI.

## Changes Made

### 1. Updated `planningCenterAPI.js` - Pagination Handling
- Enhanced the `getPeople` method to properly handle pagination parameters
- Added default pagination values (page=1, per_page=25) if not provided
- Added logging of pagination information for debugging
- Ensured pagination metadata is always included in the response, even when there's no included data

```javascript
async getPeople(params = {}) {
  try {
    // Ensure pagination parameters are properly set
    const queryParams = { ...params };
    
    // Set default pagination if not provided
    if (!queryParams.per_page) {
      queryParams.per_page = 25; // Default page size
    }
    
    if (!queryParams.page) {
      queryParams.page = 1; // Default to first page
    }
    
    // Make the API request with the prepared parameters
    const response = await this.axios.get('/people/v2/people', { params: queryParams });
    
    // Log pagination information for debugging
    console.log('Planning Center API pagination info:', {
      total_count: response.data.meta?.total_count,
      total_pages: response.data.meta?.total_pages,
      current_page: response.data.meta?.current_page,
      per_page: queryParams.per_page,
      page: queryParams.page
    });
    
    // Process included data if it exists
    if (response.data.included && response.data.included.length > 0) {
      return this.processIncludedData(response.data);
    }
    
    // If no included data, still extract pagination information
    const pagination = this.extractPaginationInfo(response.data.links, response.data.meta);
    
    return {
      ...response.data,
      pagination
    };
  } catch (error) {
    console.error('Error fetching Planning Center people:', error);
    throw error;
  }
}
```

### 2. Updated `planningCenterAPI.js` - Name Formatting
- Enhanced the `processIncludedData` method to ensure proper name formatting
- Added validation to ensure attributes object exists
- Added validation for first_name and last_name attributes
- Added a full_name attribute that combines first_name and last_name
- Added logging of sample processed people data for debugging

```javascript
// Process each item to link their relationships with included data
const processedData = dataArray.map(item => {
  // Create a copy of the item object
  const processedItem = { ...item };

  // Ensure attributes exist
  if (!processedItem.attributes) {
    processedItem.attributes = {};
  }

  // Ensure first_name and last_name are properly set
  if (processedItem.attributes.first_name === undefined) {
    processedItem.attributes.first_name = '';
  }

  if (processedItem.attributes.last_name === undefined) {
    processedItem.attributes.last_name = '';
  }

  // Add a full_name attribute for convenience
  processedItem.attributes.full_name = `${processedItem.attributes.first_name} ${processedItem.attributes.last_name}`.trim();
  
  // ... rest of the method ...
});

// Log the first few processed items for debugging
if (processedData.length > 0) {
  console.log('Sample processed people data:');
  processedData.slice(0, 3).forEach((person, index) => {
    console.log(`Person ${index + 1}: ID=${person.id}, Name=${person.attributes.full_name || 'N/A'}`);
  });
}
```

### 3. Updated `planningCenterController.js` - Name Usage
- Enhanced the `getPeopleForDirectory` method to use the formatted names
- Added logic to use the full_name attribute if available, otherwise construct the name from first_name and last_name
- Added logging of person names for debugging

```javascript
// Use the full_name attribute if available, otherwise construct the name from first_name and last_name
const personName = attributes.full_name || `${attributes.first_name || ''} ${attributes.last_name || ''}`.trim();

// Log the person's name for debugging
console.log(`Formatting person: ID=${person.id}, Name=${personName}`);

return {
  _id: `pc_${person.id}`,
  name: personName,
  // ... rest of the person object ...
};
```

## Expected Results
With these changes, the Planning Center people page should now:
1. Properly handle pagination, returning all people from the API in paginated form
2. Correctly display people's names in the UI
3. Provide better logging for debugging any future issues

## Testing
The changes can be tested by:
1. Navigating to the People Directory page
2. Enabling the Planning Center toggle
3. Verifying that people are displayed with their full names
4. Navigating through multiple pages of results using the pagination controls
5. Searching for people and verifying that the search results are paginated correctly

## Additional Notes
- Added extensive logging to help with debugging any future issues
- Improved error handling and validation throughout the code
- Made the code more robust by handling edge cases (missing attributes, undefined values, etc.)