# WiiM Config Infinite Loop Fix

## Issue Description
The WiiM page was experiencing an infinite loop when calling the `getConfig()` endpoint. This was causing excessive API calls to the server and potentially degrading performance.

## Root Cause Analysis
After examining the code in `WiimPage.js`, we identified that the issue was in the `useEffect` hook that had `[configStatus]` as its dependency array:

```javascript
useEffect(() => {
  const fetchData = async () => {
    try {
      // Get configuration status
      const config = await wiimService.getConfig();
      setConfigStatus(config);
      
      // ... additional code ...
    } catch (err) {
      // ... error handling ...
    }
  };

  fetchData();

  // ... interval setup ...

  return () => clearInterval(intervalId);
}, [configStatus]); // This was causing the infinite loop
```

This created an infinite loop because:
1. The effect runs initially
2. It calls `getConfig()` and updates `configStatus` with `setConfigStatus(config)`
3. Since `configStatus` changed, the effect runs again
4. It calls `getConfig()` again, and the cycle continues

## Solution
The fix was to change the dependency array to an empty array:

```javascript
useEffect(() => {
  // ... same effect code ...
}, []); // Empty dependency array to ensure this effect only runs once on mount
```

With this change:
1. The effect runs only once when the component mounts
2. It calls `getConfig()` and updates `configStatus`
3. Even though `configStatus` changes, the effect doesn't run again
4. The polling interval for playback status updates still works correctly because it's set up inside the useEffect and continues to run every 5 seconds

## Implementation Details
The change was made to `client/src/pages/Wiim/WiimPage.js` by modifying the dependency array of the useEffect hook from `[configStatus]` to `[]`.

## Testing
A test script was created to simulate and explain the fix. The script demonstrates:
- How the original code would cause an infinite loop
- How the fixed code prevents the infinite loop
- That the polling functionality for playback status updates is maintained

## Additional Notes
This is a common pattern in React applications to prevent infinite loops when fetching data and updating state in useEffect hooks. When a state variable is both read and updated in the same useEffect hook, it should generally not be included in the dependency array unless you specifically want the effect to run again when that state changes.

In this case, we only want to fetch the configuration once when the component mounts, and then set up a polling interval for playback status updates. The empty dependency array ensures this behavior.

## Related Issues
This fix complements the previous fix for the WiiM one-click setup infinite loop issue. Together, these changes ensure that the WiiM integration doesn't make excessive API calls to the server.