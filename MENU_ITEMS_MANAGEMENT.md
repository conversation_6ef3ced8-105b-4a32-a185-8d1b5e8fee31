# Menu Items Management

This document provides information about the menu items management feature in the CSF Portal.

## Overview

The menu items management feature allows administrators to manage the navigation menu items displayed in the portal. Administrators can:

- View all menu items
- Filter menu items by search term, category, and type
- Add new menu items
- Edit existing menu items
- Delete menu items
- Initialize default menu items

## Accessing Menu Items Management

1. Log in to the CSF Portal with an admin account
2. Navigate to Admin > Menu Management
3. Click on the "Menu Items" tab

## Menu Item Properties

Each menu item has the following properties:

- **Title**: The display name of the menu item in the navigation menu
- **Original Title**: The original title (used for reference)
- **Friendly Name**: An optional alternative display name
- **Description**: A brief description of the menu item
- **Path**: The URL path for the menu item (e.g., `/dashboard`)
- **Icon**: The Material-UI icon name to display next to the menu item
- **Custom Icon**: URL to a custom icon image (optional)
- **Categories**: Array of categories the menu item belongs to
- **Required Roles**: Array of user roles that can see this menu item
- **Required Permission**: Permission required to see this menu item
- **Active**: Whether the menu item is active and visible
- **Order**: The display order of the menu item (lower numbers appear first)
- **Type**: The type of menu item (`regular`, `integration`, or `admin`)

## Menu Item Types

Menu items are categorized into three types:

1. **Regular**: Standard navigation items (Dashboard, Shortcuts, etc.)
2. **Integration**: Items related to integrated services (Canva, GLPI, etc.)
3. **Admin**: Administrative items (Manage Users, Manage Roles, etc.)

## Managing Menu Items

### Viewing Menu Items

The menu items management page displays a table of all menu items with the following columns:

- Title
- Path
- Categories
- Type
- Status
- Order
- Actions

You can filter the menu items by:

- Search term (searches in title, description, and path)
- Category
- Type

### Adding a Menu Item

To add a new menu item:

1. Click the "Add Menu Item" button
2. Fill in the required fields (Title, Path)
3. Configure other properties as needed
4. Click "Save"

### Editing a Menu Item

To edit an existing menu item:

1. Click the edit icon (pencil) in the Actions column
2. Modify the properties as needed
3. Click "Save"

### Deleting a Menu Item

To delete a menu item:

1. Click the delete icon (trash) in the Actions column
2. Confirm the deletion

### Initializing Default Menu Items

If you need to reset the menu items to their default state:

1. Click the "Initialize Default Menu Items" button
2. Confirm the action

This will create the default menu items if they don't exist, but won't modify existing items.

## API Endpoints

The menu items management feature uses the following API endpoints:

- `GET /api/menu-items`: Get all menu items
- `GET /api/menu-items/search?q={searchTerm}`: Search menu items
- `GET /api/menu-items/categories`: Get all unique categories
- `GET /api/menu-items/:id`: Get a specific menu item
- `POST /api/menu-items`: Create a new menu item (admin only)
- `PUT /api/menu-items/:id`: Update a menu item (admin only)
- `DELETE /api/menu-items/:id`: Delete a menu item (admin only)
- `POST /api/menu-items/init`: Initialize default menu items (admin only)

## Technical Implementation

The menu items management feature consists of:

1. **Backend**:
   - MongoDB model: `MenuItem.js`
   - API routes: `menuItems.js`

2. **Frontend**:
   - Service: `menuItemService.js`
   - Component: `MenuItemsManagement.js`
   - Page: `AdminMenuPage.js` (Menu Items tab)

## Permissions

To manage menu items, a user must have the `menu:admin` permission and the `admin` role.

## Troubleshooting

If you encounter issues with menu items management:

1. Ensure you're logged in with an admin account
2. Check the browser console for any error messages
3. Verify that the API endpoints are working correctly
4. Check the server logs for any backend errors

If menu items are not appearing in the navigation menu:

1. Verify that the menu item is marked as active
2. Check that the user has the required roles and permissions
3. Ensure the menu item has valid categories