# API Token Caching Recommendations

## Overview

This document provides recommendations for implementing token caching in various APIs used in the CSF Portal application. Token caching reduces the number of authentication calls to external providers, improving performance and reducing the risk of rate limiting.

## Implemented Token Caching

Token caching has been successfully implemented for the following APIs:

### Apple Business Manager API

- **Implementation**: The `authenticate()` method in `appleBusinessManagerAPI.js` now checks for a cached token before making an authentication request.
- **Cache Key**: Based on client ID and key ID to uniquely identify different API clients.
- **Expiration**: Uses the `expires_in` value from the API response with a 5-minute safety buffer.
- **Benefits**: Reduces authentication calls to Apple's servers, shares tokens across different instances of the API client.

## Recommended APIs for Token Caching

The following APIs could benefit from token caching:

### Dreo API

- **Current Implementation**: The `authenticate()` method in `dreoAPI.js` obtains a token but doesn't implement caching or expiration tracking.
- **Recommendation**: 
  - Import the cacheUtil module
  - Create a cache key based on the email (unique identifier)
  - Check the cache before authenticating
  - Store the token in the cache with a default expiration time (since the API doesn't provide one)
  - Add token expiration tracking

### Google APIs (Admin, Drive, Calendar, Forms)

- **Current Implementation**: These APIs use the Google Auth library which has its own token management.
- **Recommendation**: 
  - Enhance the existing token management with the cacheUtil for cross-instance sharing
  - Cache the entire credentials object, not just the access token
  - Use the expiration time provided by the Google Auth library

### Canva API

- **Current Implementation**: The `authenticate()` method in `canvaAPI.js` obtains a token but doesn't implement caching.
- **Recommendation**: 
  - Import the cacheUtil module
  - Create a cache key based on client identifiers
  - Check the cache before authenticating
  - Store the token in the cache with the expiration time from the response

### WiiM API (Spotify Integration)

- **Current Implementation**: The Spotify authentication in `wiimAPI.js` obtains a token but doesn't implement caching.
- **Recommendation**: 
  - Import the cacheUtil module
  - Create a cache key for Spotify tokens
  - Check the cache before authenticating
  - Store the token in the cache with the expiration time from the response

## Implementation Guidelines

Follow these guidelines when implementing token caching for any API:

1. **Import the cache utility**:
   ```javascript
   const cacheUtil = require('../../utils/cacheUtil');
   ```

2. **Create a unique cache key**:
   ```javascript
   const cacheKey = cacheUtil.createKey('api-name-token', {
     uniqueId1: this.uniqueId1,
     uniqueId2: this.uniqueId2
   });
   ```

3. **Check for a cached token before authenticating**:
   ```javascript
   const cachedToken = cacheUtil.get(cacheKey);
   if (cachedToken) {
     console.log('Using cached token');
     this.token = cachedToken.token;
     this.tokenExpiration = cachedToken.expiration;
     return this.token;
   }
   ```

4. **Store the token in the cache with its expiration**:
   ```javascript
   // Calculate token expiration (subtract safety buffer)
   const expiresIn = response.data.expires_in * 1000;
   this.tokenExpiration = Date.now() + expiresIn - (5 * 60 * 1000);
   
   // Cache the token with its expiration
   cacheUtil.set(cacheKey, {
     token: this.token,
     expiration: this.tokenExpiration
   }, expiresIn - (5 * 60 * 1000));
   ```

5. **Handle APIs without expiration information**:
   ```javascript
   // Use a default expiration time (e.g., 1 hour)
   const defaultExpiresIn = 60 * 60 * 1000; // 1 hour in milliseconds
   this.tokenExpiration = Date.now() + defaultExpiresIn - (5 * 60 * 1000);
   
   // Cache the token with the default expiration
   cacheUtil.set(cacheKey, {
     token: this.token,
     expiration: this.tokenExpiration
   }, defaultExpiresIn - (5 * 60 * 1000));
   ```

## Benefits of Token Caching

1. **Reduced API Calls**: Minimizes authentication requests to external providers.
2. **Improved Performance**: Faster response times by avoiding unnecessary authentication.
3. **Reduced Rate Limiting**: Less chance of hitting API rate limits for authentication.
4. **Token Sharing**: Multiple instances of the same API client can share tokens.
5. **Automatic Refresh**: Tokens are automatically refreshed when they expire.

## Testing Token Caching

A test script is provided at `/test-apple-business-manager-token-cache.js` that demonstrates how to test token caching. This script:

1. Mocks the authentication API call
2. Verifies that the first authentication makes an API call
3. Verifies that subsequent authentications use the cached token
4. Tests token refresh when the cached token expires
5. Verifies that a new instance uses the cached token

You can use this script as a template for testing token caching in other APIs.