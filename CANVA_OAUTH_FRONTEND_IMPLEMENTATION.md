# Canva OAuth Frontend Implementation Guide

This guide explains how to implement the frontend components for the Canva OAuth integration in the CSF Portal. The integration allows users to authenticate with Canva using a one-click authorization flow.

## Prerequisites

- The backend implementation of the Canva OAuth integration is complete
- Access to the CSF Portal frontend codebase
- Basic understanding of React and Material-UI

## Implementation Steps

### 1. Create a Canva Authorization Button Component

Create a new component for the Canva authorization button:

```jsx
// src/components/Canva/CanvaAuthButton.js
import React, { useState, useEffect } from 'react';
import { Button, CircularProgress, Typography, Box } from '@mui/material';
import axios from 'axios';

const CanvaAuthButton = () => {
  const [loading, setLoading] = useState(false);
  const [authStatus, setAuthStatus] = useState({
    authenticated: false,
    message: '',
    authUrl: null
  });

  // Check authentication status on component mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // Function to check if user is authenticated with Canva
  const checkAuthStatus = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/canva/check-auth');
      setAuthStatus({
        authenticated: response.data.authenticated,
        message: response.data.message,
        authUrl: null
      });
    } catch (error) {
      setAuthStatus({
        authenticated: false,
        message: error.response?.data?.message || 'Error checking authentication status',
        authUrl: error.response?.data?.authUrl || null
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to initiate the OAuth flow
  const handleAuth = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/canva/auth');
      // Redirect to Canva authorization page
      window.location.href = response.data.authUrl;
    } catch (error) {
      console.error('Error getting auth URL:', error);
      setAuthStatus({
        ...authStatus,
        message: error.response?.data?.message || 'Error initiating authentication'
      });
      setLoading(false);
    }
  };

  return (
    <Box sx={{ mb: 3 }}>
      {loading ? (
        <CircularProgress size={24} />
      ) : authStatus.authenticated ? (
        <Box>
          <Typography variant="body1" color="success.main" sx={{ mb: 1 }}>
            ✅ {authStatus.message}
          </Typography>
          <Button 
            variant="outlined" 
            color="primary" 
            onClick={checkAuthStatus}
          >
            Refresh Status
          </Button>
        </Box>
      ) : (
        <Box>
          <Typography variant="body1" color="error.main" sx={{ mb: 1 }}>
            {authStatus.message}
          </Typography>
          <Button 
            variant="contained" 
            color="primary" 
            onClick={handleAuth}
            startIcon={<img src="/canva-logo.svg" alt="Canva" height="20" />}
          >
            Connect with Canva
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default CanvaAuthButton;
```

### 2. Create a Canva Dashboard Page

Create a dashboard page for the Canva integration:

```jsx
// src/pages/Canva/CanvaDashboardPage.js
import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Divider,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActionArea
} from '@mui/material';
import axios from 'axios';
import CanvaAuthButton from '../../components/Canva/CanvaAuthButton';

const CanvaDashboardPage = () => {
  const [loading, setLoading] = useState(false);
  const [designs, setDesigns] = useState([]);
  const [error, setError] = useState(null);

  // Fetch designs when component mounts
  useEffect(() => {
    fetchDesigns();
  }, []);

  // Function to fetch designs from the API
  const fetchDesigns = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get('/api/canva/designs');
      setDesigns(response.data);
    } catch (error) {
      console.error('Error fetching designs:', error);
      setError(error.response?.data?.message || 'Error fetching designs');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Canva Integration
        </Typography>
        
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            Authentication Status
          </Typography>
          <CanvaAuthButton />
        </Paper>

        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Your Canva Designs
          </Typography>
          <Divider sx={{ mb: 3 }} />
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Typography color="error">{error}</Typography>
          ) : designs.length === 0 ? (
            <Typography>No designs found. Please authenticate with Canva to view your designs.</Typography>
          ) : (
            <Grid container spacing={3}>
              {designs.map((design) => (
                <Grid item xs={12} sm={6} md={4} key={design.id}>
                  <Card>
                    <CardActionArea href={design.url} target="_blank" rel="noopener noreferrer">
                      {design.thumbnail && (
                        <CardMedia
                          component="img"
                          height="140"
                          image={design.thumbnail}
                          alt={design.title}
                        />
                      )}
                      <CardContent>
                        <Typography variant="h6" noWrap>
                          {design.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Created: {new Date(design.created).toLocaleDateString()}
                        </Typography>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default CanvaDashboardPage;
```

### 3. Add the Canva Dashboard to the Router

Update your router configuration to include the Canva dashboard page:

```jsx
// In your router configuration file
import CanvaDashboardPage from './pages/Canva/CanvaDashboardPage';

// Add this to your routes
<Route path="/dashboard/canva" element={<CanvaDashboardPage />} />
```

### 4. Add Canva to the Navigation Menu

Update your navigation menu to include a link to the Canva dashboard:

```jsx
// In your navigation component
<ListItem button component={Link} to="/dashboard/canva">
  <ListItemIcon>
    <img src="/canva-logo.svg" alt="Canva" height="24" />
  </ListItemIcon>
  <ListItemText primary="Canva" />
</ListItem>
```

### 5. Handle OAuth Callback

The OAuth callback is handled by the backend, which will redirect to the Canva dashboard page after successful authentication. No additional frontend code is needed for this step.

## Testing the Implementation

1. Navigate to the Canva dashboard page
2. Click the "Connect with Canva" button
3. You should be redirected to the Canva authorization page
4. After authorizing, you should be redirected back to the Canva dashboard
5. The authentication status should show as authenticated
6. Your Canva designs should be displayed

## Troubleshooting

- If the authorization button doesn't work, check the browser console for errors
- Ensure that the Canva OAuth credentials are properly configured in the environment variables
- Check the network tab in the browser developer tools to see if the API requests are being made correctly

## Additional Resources

- [Canva Connect API Documentation](https://www.canva.dev/docs/connect/)
- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [Material-UI Documentation](https://mui.com/getting-started/usage/)