# Google Groups and Teams Implementation

This document describes the implementation of fetching teams and groups from Google for user profiles.

## Overview

The implementation adds functionality to sync a user's Google groups with their profile in the application. It also derives teams from Google groups based on a naming convention. This functionality is available both for individual user sync and for syncing all users at once.

## Implementation Details

### Changes Made

1. Updated the `/api/staff-directory/google/sync-profile` endpoint in `staffDirectory.js` to:
   - Add the necessary scopes for accessing Google groups
   - Fetch the user's groups from Google Admin Directory API
   - Process each Google group and create or update corresponding Group records in the database
   - Update the user's groups field with references to these Group records
   - Derive teams from Google groups with the "Team-" prefix

2. Enhanced the `syncAllUsers` function in `googleAdminController.js` to:
   - Sync groups and teams for all users in addition to basic user information
   - Process each user's Google groups and create or update corresponding Group records
   - Derive teams from Google groups with the "Team-" prefix
   - Update each user's groups and teams fields with references to these records

### How It Works

1. When a user syncs their profile with Google, the application:
   - Fetches their basic profile information (name, job title, department, etc.)
   - Fetches their group memberships from Google
   - Updates their profile with this information

2. When an admin syncs all users from Google, the application:
   - Fetches all users from Google Workspace
   - Creates or updates user records in the database
   - For each user, fetches their group memberships from Google
   - Updates each user's profile with this information

3. For Google groups:
   - Each Google group the user belongs to is mapped to a Group record in the database
   - If a Group record doesn't exist for a Google group, one is created
   - The user's groups field is updated with references to these Group records

4. For teams:
   - Google groups with names starting with "Team-" are treated as teams
   - The "Team-" prefix is removed to get the team name
   - A Team record is created or updated in the database
   - The user's teams field is updated with references to these Team records

## Service Account Requirements

The Google service account used for this integration needs to have domain-wide delegation with the following OAuth scopes:

```
https://www.googleapis.com/auth/admin.directory.user
https://www.googleapis.com/auth/admin.directory.user.readonly
https://www.googleapis.com/auth/admin.directory.group
https://www.googleapis.com/auth/admin.directory.group.readonly
```

### Setting Up Service Account Permissions

1. Go to the Google Cloud Console
2. Navigate to the project that contains your service account
3. Go to "IAM & Admin" > "Service Accounts"
4. Find your service account and note its email address
5. Go to the Google Admin Console (admin.google.com)
6. Navigate to "Security" > "API Controls" > "Domain-wide Delegation"
7. Add the service account with the following OAuth scopes:
   ```
   https://www.googleapis.com/auth/admin.directory.user,https://www.googleapis.com/auth/admin.directory.user.readonly,https://www.googleapis.com/auth/admin.directory.group,https://www.googleapis.com/auth/admin.directory.group.readonly
   ```

## Testing

A test script (`test-google-groups-sync.js`) has been created to verify the implementation. The script:

1. Fetches a user's groups directly from Google Admin API
2. Gets the user's current teams and groups from the database
3. Calls the sync-profile endpoint
4. Verifies that the user's groups in the database match what was fetched from Google
5. Checks if teams were derived from Google groups with the "Team-" prefix

Note: Running the test script requires the service account to have the appropriate permissions as described above.

## Troubleshooting

If groups are not being pulled from Google, check the following:

1. Ensure the service account has domain-wide delegation with the required scopes
2. Verify that the user has a valid email address in the database
3. Check the server logs for any authentication or API errors
4. Ensure the Google Admin API is enabled in the Google Cloud Console project