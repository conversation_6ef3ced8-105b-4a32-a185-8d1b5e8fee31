# WiiM Spotify Auto Update Implementation

## Overview

This document describes the changes made to implement auto-update functionality for the playlist tab and currently playing information when a song or playlist is changed in the WiiM page.

## Requirements

1. Use the Spotify Web API to start a user's playback when pressing play for a song or playlist on the WiiM page.
2. Auto-update the playlist tab and currently playing information when a song or playlist is changed.
3. Add auto-update polling to the currently playing information to automatically pick up new songs when they change.

## Implementation Details

### 1. Spotify Web API for Playback

The Spotify Web API was already being used for starting playback through the following chain:
- Client: `handlePlaySpotifyPlaylist` -> `wiimService.playSpotifyPlaylistConnect()`
- Server: API route `/api/wiim/spotify/playlists/:playlistId/play-connect` -> `wiimController.playSpotifyPlaylistConnect` -> `wiimAPI.playSpotifyPlaylistConnect()`

The `playSpotifyPlaylistConnect` method in wiimAPI.js already uses the Spotify Web API endpoint `/me/player/play` as specified in the requirements.

### 2. Auto-Update for Playlist Tab and Currently Playing Information

#### Enhanced Polling Mechanisms

1. **Regular Polling (every 5 seconds)**
   - Modified to fetch both playback status and current playlist in parallel
   - Updates the playlist state if data is available
   - Ensures the currently playing information is always up to date

2. **Spotify Polling (every 10 seconds)**
   - Added functionality to fetch Spotify playlists
   - Updates the Spotify playlists state if data is available
   - Ensures the Spotify playlist tab is always up to date

#### Immediate Updates After Actions

Updated the following functions to immediately update playlist information after actions, rather than waiting for polling:

1. **handlePlaySpotifyPlaylist**
   - Refreshes Spotify playback state, queue, and playlists after playing a playlist
   - Refreshes playback status and current playlist to update Now Playing information

2. **handlePlaySpotifyTrack**
   - Refreshes Spotify playback state, queue, and playlists after playing a track
   - Refreshes playback status and current playlist to update Now Playing information

3. **handlePlayTrack**
   - Refreshes playback status and current playlist after playing a track

4. **handlePlayPlaylist**
   - Refreshes playback status, current playlist, and all playlists after playing a playlist

5. **handleNext** and **handlePrevious**
   - Refreshes playback status and current playlist after skipping to the next or previous track

### 3. Auto-Update Polling for Currently Playing Information

The existing polling mechanisms were enhanced to ensure they update all relevant information:

1. **Regular Polling (every 5 seconds)**
   - Already updated playback status
   - Now also updates playlist information

2. **Spotify Polling (every 10 seconds)**
   - Already updated Spotify playback state and queue
   - Now also updates Spotify playlists

## Testing

The implementation was tested to ensure:
1. Playback works correctly using the Spotify Web API
2. Auto-updates work when songs or playlists change
3. The UI reflects changes immediately after actions

## Conclusion

These changes ensure that both the playlist tab and currently playing information are automatically updated when a song or playlist is changed, providing a better user experience with always up-to-date information.