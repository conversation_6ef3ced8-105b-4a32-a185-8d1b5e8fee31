# WiiM API SSL Certificate Validation Fix

## Issue
The WiiM API calls were failing when connecting to WiiM devices with untrusted SSL certificates (such as self-signed certificates).

## Solution
Modified the WiiM API implementation to ignore untrusted SSL certificates, allowing it to function even when connecting to devices with self-signed or otherwise untrusted certificates.

## Changes Made
1. Added the `https` module import to the WiiM API implementation:
   ```javascript
   const https = require('https');
   ```

2. Updated the Axios instance configuration in the WiiM API constructor to include an HTTPS agent with SSL certificate validation disabled:
   ```javascript
   this.axios = axios.create({
     baseURL: this.baseURL,
     headers: {
       'Content-Type': 'application/json'
     },
     httpsAgent: new https.Agent({
       rejectUnauthorized: false
     })
   });
   ```

## Files Modified
- `/server/integrations/wiim/wiimAPI.js`

## How It Works
The `httpsAgent` configuration with `rejectUnauthorized: false` tells the HTTPS agent to accept connections even if the SSL certificate is not trusted. This allows the WiiM API to connect to devices with self-signed or otherwise untrusted certificates without failing.

## Security Considerations
Disabling SSL certificate validation reduces security by removing the protection against man-in-the-middle attacks. However, this is a common practice for internal or development systems that use self-signed certificates. In a production environment, it's recommended to use properly signed certificates whenever possible.

## Testing
To test this change:
1. Ensure the WiiM device is configured with HTTPS enabled
2. Try accessing the WiiM device through the portal
3. Verify that the WiiM API calls succeed even if the device has an untrusted SSL certificate

## References
- This implementation follows the same pattern used in the UniFi Network API implementation in this codebase
- [Node.js HTTPS documentation](https://nodejs.org/api/https.html#https_https_request_options_callback)
- [Axios documentation](https://axios-http.com/docs/req_config)