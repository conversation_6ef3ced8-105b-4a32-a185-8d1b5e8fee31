# Google Drive Integration Changes

## Issue Description
The Google Drive page was configured through environment variables, but the page was returning a "failed to load files" error. The integration needed to use global environment variables and filter files based on the user's email address to only show files they have access to. Additionally, the Drive needed to support folders and files with a viewer.

## Changes Made

### 1. Updated Google Drive API to Filter Files by User's <PERSON><PERSON>

Modified the `listFiles` and `searchFiles` methods in the Google Drive API to accept a `userEmail` parameter and filter files based on the user's access:

```javascript
// In googleDriveAPI.js
async listFiles(options = {}, userEmail = null) {
  // ...
  // If userEmail is provided, add a query to filter files by user's access
  if (userEmail) {
    // Build the query to filter files the user has access to
    const userQuery = `'${userEmail}' in owners or '${userEmail}' in writers or '${userEmail}' in readers`;
    
    // Combine with existing query if present
    if (options.q) {
      options.q = `(${options.q}) and (${userQuery})`;
    } else {
      options.q = userQuery;
    }
  }
  // ...
}

async searchFiles(query, options = {}, userEmail = null) {
  // ...
  // If userEmail is provided, add a query to filter files by user's access
  if (userEmail) {
    // Build the query to filter files the user has access to
    const userQuery = `'${userEmail}' in owners or '${userEmail}' in writers or '${userEmail}' in readers`;
    
    // Combine with existing query
    defaultOptions.q = `(${defaultOptions.q}) and (${userQuery})`;
  }
  // ...
}
```

### 2. Updated Controller to Pass User's Email to API Methods

Modified the `listFiles` and `searchFiles` methods in the Google Drive controller to extract the user's email from the authenticated user object and pass it to the API methods:

```javascript
// In googleDriveController.js
exports.listFiles = async (req, res) => {
  // ...
  // Get user email from the authenticated user
  const userEmail = req.user ? req.user.email : null;
  
  // If no user email is available, log a warning
  if (!userEmail) {
    console.warn('No user email available for filtering Google Drive files');
  }

  const options = req.query;
  const files = await api.listFiles(options, userEmail);
  // ...
}

exports.searchFiles = async (req, res) => {
  // ...
  // Get user email from the authenticated user
  const userEmail = req.user ? req.user.email : null;
  
  // If no user email is available, log a warning
  if (!userEmail) {
    console.warn('No user email available for filtering Google Drive search results');
  }

  const files = await api.searchFiles(query, options, userEmail);
  // ...
}
```

### 3. Implemented Folder Navigation

Added folder navigation functionality to the Google Drive files page:

1. Added state variables to track the current folder and folder path:
```javascript
const [currentFolderId, setCurrentFolderId] = useState('root');
const [folderPath, setFolderPath] = useState([{ id: 'root', name: 'My Drive' }]);
```

2. Updated the `fetchFiles` function to use the current folder ID:
```javascript
const fetchFiles = async () => {
  // ...
  const options = { 
    q: currentFolderId === 'root' 
      ? "'root' in parents" 
      : `'${currentFolderId}' in parents` 
  };
  const data = await googleDriveService.listFiles(options);
  // ...
}
```

3. Added functions to handle folder navigation:
```javascript
// Navigate to viewer page or enter folder
const handleViewFile = (file) => {
  // If it's a folder, navigate into it
  if (file.mimeType === 'application/vnd.google-apps.folder') {
    navigateToFolder(file);
  } else {
    // Otherwise, open the file viewer
    navigate(`/google-drive/view/${file.id}`);
  }
};

// Navigate into a folder
const navigateToFolder = (folder) => {
  // Update current folder ID
  setCurrentFolderId(folder.id);
  
  // Add folder to path
  setFolderPath([...folderPath, { id: folder.id, name: folder.name }]);
  
  // Reset search term
  setSearchTerm('');
  
  // Fetch files in the folder
  fetchFilesInFolder(folder.id);
};

// Fetch files in a specific folder
const fetchFilesInFolder = async (folderId) => {
  // ...
  const options = { 
    q: `'${folderId}' in parents` 
  };
  const data = await googleDriveService.listFiles(options);
  // ...
};

// Navigate to a specific folder in the path
const navigateToPathFolder = (index) => {
  // Get the folder from the path
  const folder = folderPath[index];
  
  // Update current folder ID
  setCurrentFolderId(folder.id);
  
  // Update path to include only up to this folder
  setFolderPath(folderPath.slice(0, index + 1));
  
  // Reset search term
  setSearchTerm('');
  
  // Fetch files in the folder
  fetchFilesInFolder(folder.id);
};
```

4. Added breadcrumb navigation to the UI:
```jsx
{/* Folder Path Breadcrumbs */}
<Box sx={{ mb: 2 }}>
  <Breadcrumbs aria-label="folder path">
    {folderPath.map((folder, index) => (
      <Link
        key={folder.id}
        color={index === folderPath.length - 1 ? "text.primary" : "inherit"}
        sx={{ 
          cursor: index === folderPath.length - 1 ? 'default' : 'pointer',
          display: 'flex',
          alignItems: 'center'
        }}
        onClick={() => index < folderPath.length - 1 && navigateToPathFolder(index)}
        underline={index === folderPath.length - 1 ? "none" : "hover"}
      >
        {index === 0 ? (
          <>
            <FolderIcon sx={{ mr: 0.5 }} fontSize="small" />
            {folder.name}
          </>
        ) : folder.name}
      </Link>
    ))}
  </Breadcrumbs>
</Box>
```

5. Updated the file list item click handler to pass the entire file object:
```jsx
<ListItemButton onClick={() => handleViewFile(file)}>
  {/* ... */}
</ListItemButton>
```

## Summary

These changes ensure that:

1. The Google Drive integration uses global environment variables for configuration.
2. Files are filtered based on the user's email address to only show files they have access to.
3. Folder navigation is properly supported, allowing users to browse through folders.
4. The file viewer functionality works correctly, allowing users to view and edit files.

The integration now properly shows only the files that the logged-in user has access to, and supports folder navigation and file viewing similar to the dashboard.