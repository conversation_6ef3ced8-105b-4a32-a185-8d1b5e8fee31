# Google Integrations Setup Guide

This guide explains how to properly configure Google service account authentication for all Google integrations in the CSF Portal.

## 🚨 Current Issue

The Google integrations are failing because **domain-wide delegation is not configured** in Google Workspace Admin Console. The service account credentials are valid, but the service account cannot impersonate users without proper delegation setup.

## ✅ Fixes Applied

### 1. Route Registration Issue Fixed
- **Problem**: `/api/google-drive` routes existed but weren't registered in `server.js`
- **Fix**: Added `app.use('/api/google-drive', require('./routes/api/googleDrive'));` to `server.js:109`

### 2. Frontend API Endpoint Mismatch Fixed  
- **Problem**: `RecentFilesWidget` was calling `/api/google/drive/files` instead of `/api/google-drive/files`
- **Fix**: Updated `client/src/components/widgets/RecentFilesWidget.js:31` to use correct endpoint

## 🔧 Required Configuration: Domain-Wide Delegation

### Step 1: Get Service Account Client ID
The service account email is: `<EMAIL>`

You need to get the **Client ID** for this service account:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to IAM & Admin → Service Accounts
3. Find the service account: `<EMAIL>`
4. Copy the **Client ID** (numeric value)

### Step 2: Configure Domain-Wide Delegation
1. Go to [Google Admin Console](https://admin.google.com/)
2. Navigate to **Security** → **Access and data control** → **API controls**
3. Click **Manage Domain Wide Delegation** 
4. Click **Add new** and enter:
   - **Client ID**: (from Step 1)
   - **OAuth Scopes**: (copy the list below exactly)

### Step 3: Required OAuth Scopes
Add these scopes in the domain-wide delegation configuration:

```
https://www.googleapis.com/auth/drive,https://www.googleapis.com/auth/drive.file,https://www.googleapis.com/auth/drive.metadata,https://www.googleapis.com/auth/admin.directory.user,https://www.googleapis.com/auth/admin.directory.group,https://www.googleapis.com/auth/admin.directory.user.security,https://www.googleapis.com/auth/calendar,https://www.googleapis.com/auth/calendar.events,https://www.googleapis.com/auth/calendar.settings.readonly,https://www.googleapis.com/auth/forms.body,https://www.googleapis.com/auth/forms.responses.readonly
```

## 🧪 Testing the Configuration

After configuring domain-wide delegation, test the setup:

```bash
# Test service account authentication
node test-google-service-auth-simple.js

# Test specific Google integration
node test-google-drive-service-account.js  # (if exists)
```

## 📊 Current Integration Status

| Integration | Environment Variables | Route Registration | Frontend Alignment | Domain Delegation |
|-------------|----------------------|-------------------|-------------------|------------------|
| Google Drive | ✅ Configured | ✅ Fixed | ✅ Fixed | ❌ **Missing** |
| Google Admin | ✅ Configured | ✅ Working | ✅ Working | ❌ **Missing** |
| Google Calendar | ✅ Configured | ✅ Working | ✅ Working | ❌ **Missing** |
| Google Forms | ✅ Configured | ✅ Working | ✅ Working | ❌ **Missing** |

## 🔑 Environment Variables (Already Configured)

```bash
# Service Account (Primary Authentication)
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# Domain Configuration
ALLOWED_DOMAINS=ukcsf.org
```

## 📝 Integration Architecture

```
Frontend (React) → API Routes → Controllers → Google API Wrappers → Service Account Auth → Google APIs
                                                                          ↓
                                                                  Impersonate User
                                                                (Requires Domain Delegation)
```

## 🎯 Next Steps

1. **Configure Domain-Wide Delegation** (Steps 1-3 above) - **This is the critical missing piece**
2. Test authentication with `node test-google-service-auth-simple.js`
3. Verify all Google integrations are working in the portal
4. Monitor integration status in the portal admin panel

## ⚠️ Security Notes

- Service account has domain-wide delegation powers - ensure proper access controls
- Private key is stored securely in environment variables
- All API calls are logged and tracked via `integrationTracker`
- Users can only access data they have permissions for (impersonation respects Google Workspace permissions)

## 🔍 Troubleshooting

### "invalid_grant: Invalid email or User ID"
- Domain-wide delegation is not configured
- Service account Client ID is incorrect in delegation settings  
- User email doesn't exist in the domain

### "Access denied" or 403 errors
- Missing required scopes in domain-wide delegation
- User doesn't have permission to access the requested resource

### Route not found (404 errors)
- Check that all routes are properly registered in `server.js`
- Verify frontend is calling correct API endpoints