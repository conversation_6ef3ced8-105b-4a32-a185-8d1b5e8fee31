# One-Click Setup for Integrations

This document describes the implementation of one-click setup functionality for integrations in the CSF Portal. The goal is to simplify the user experience by eliminating custom config screens where possible and adding simple one-click setup options.

## Implemented Integrations

The following integrations now have one-click setup functionality:

- **Planning Center**: Already had one-click setup implemented
- **Dreo**: One-click setup added in this update
- **UniFi Access**: One-click setup added in this update
- **Lenel S2 NetBox**: One-click setup added in this update

## Implementation Pattern

The one-click setup functionality follows a consistent pattern across integrations:

### Client-Side

1. **Service Method**: Add a `oneClickSetup` method to the integration's service file that makes a POST request to the one-click setup endpoint.

```javascript
oneClickSetup: async () => {
  try {
    const response = await axios.post('/api/integration-name/one-click-setup');
    return response.data;
  } catch (error) {
    console.error('Error setting up integration with one click:', error);
    throw error;
  }
}
```

2. **Setup Page**: Add a one-click setup section to the integration's setup page with:
   - A heading and description
   - A button to trigger the one-click setup
   - Loading and success states
   - Error handling

```jsx
<Box sx={{ mb: 4 }}>
  <Typography variant="h6" gutterBottom>
    One-Click Setup
  </Typography>
  <Typography variant="body1" paragraph>
    Use our one-click setup to automatically configure the integration with generated credentials.
    This is the easiest way to get started.
  </Typography>
  <Button
    variant="contained"
    color={oneClickSuccess ? "success" : "primary"}
    onClick={handleOneClickSetup}
    disabled={oneClickLoading}
    startIcon={oneClickLoading ? <CircularProgress size={20} color="inherit" /> : <AutoFixHighIcon />}
    sx={{ mb: 2 }}
  >
    {oneClickLoading ? 'Setting up...' : oneClickSuccess ? 'Setup Successful' : 'One-Click Setup'}
  </Button>
  {oneClickSuccess && (
    <Alert severity="success" sx={{ mb: 2 }}>
      Integration has been configured successfully with one-click setup!
    </Alert>
  )}
</Box>
```

3. **Handler Function**: Add a handler function to the setup page component to call the service method and update the UI.

```javascript
const handleOneClickSetup = async () => {
  setOneClickLoading(true);
  setError(null);
  setOneClickSuccess(false);

  try {
    const response = await integrationService.oneClickSetup();
    setOneClickSuccess(true);

    // Update the config status with the new configuration
    setConfigStatus({
      // Update with the appropriate fields from the response
    });

    setSuccess(true);
  } catch (err) {
    setError(`One-click setup failed: ${err.message}`);
    console.error('Error setting up integration with one click:', err);
  } finally {
    setOneClickLoading(false);
  }
};
```

### Server-Side

1. **Controller Method**: Add a `oneClickSetup` method to the integration's controller file that generates credentials and saves the configuration.

```javascript
exports.oneClickSetup = async (req, res) => {
  try {
    // Generate unique credentials appropriate for the integration
    const credentials = {
      // Integration-specific credentials
    };

    // Create a new configuration
    const config = new IntegrationConfig({
      // Configuration with generated credentials
    });

    await config.save();

    // Update the API instance with new credentials
    integrationAPI = new IntegrationAPI(/* credentials */);

    // Initialize the API to update the integration status
    await integrationAPI.initialize();

    res.json({ 
      message: 'Integration configured successfully with one-click setup',
      // Return non-sensitive configuration details
    });
  } catch (error) {
    console.error('Error setting up integration with one click:', error);
    res.status(500).json({ 
      message: 'Error setting up integration with one click', 
      error: error.message 
    });
  }
};
```

2. **Route**: Add a route for the one-click setup endpoint.

```javascript
// @route   POST api/integration-name/one-click-setup
// @desc    Set up integration with one click
// @access  Private
router.post('/one-click-setup', isAuthenticated, integrationController.oneClickSetup);
```

## Remaining Integrations

The following integrations still need one-click setup functionality:

- Synology
- Canva
- GLPI
- Google Admin
- Google Drive
- Google Forms
- Mosyle Business
- UniFi Network
- UniFi Protect

## Implementation Considerations

When implementing one-click setup for the remaining integrations, consider the following:

1. **Authentication Method**: Different integrations may require different authentication methods (API keys, OAuth, username/password, etc.). The one-click setup should generate appropriate credentials for each integration.

2. **Security**: Ensure that sensitive credentials are not returned to the client. Only return non-sensitive information like usernames or configuration timestamps.

3. **Error Handling**: Provide clear error messages to help users troubleshoot issues with the one-click setup.

4. **Testing**: Test the one-click setup functionality thoroughly to ensure it works correctly and doesn't break existing functionality.

5. **Documentation**: Update the integration-status.md file to reflect which integrations have one-click setup available.

## Benefits

Implementing one-click setup for integrations provides several benefits:

- **Improved User Experience**: Users can set up integrations more quickly and easily.
- **Reduced Support Burden**: Fewer users will need help setting up integrations.
- **Standardized Implementation**: Following a consistent pattern makes the code more maintainable.
- **Easier Onboarding**: New users can get started with integrations more quickly.
