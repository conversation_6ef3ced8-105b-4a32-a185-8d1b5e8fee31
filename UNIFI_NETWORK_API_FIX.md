# UniFi Network API Update

## Issue
The UniFi Network integration was failing with the following error:
```
{
    "message": "Error fetching UniFi Network devices",
    "error": "Request failed with status code 404"
}
```

This indicated that the API endpoints being used in the UniFi Network API wrapper were no longer valid or had changed in a newer version of the UniFi Network Controller.

## Solution
The UniFi Network API wrapper has been completely updated to use the `node-unifi` npm package, which provides a robust and well-maintained interface to the UniFi Network Controller API. This change offers several benefits:

1. Support for all UniFi-Controller API features from v4.x.x up to v8.x.x
2. Compatibility with CloudKey Gen1, CloudKey Gen2, UnifiOS-based UDM-Pro Controller, and self-hosted UniFi controller software
3. Simplified API calls with consistent error handling
4. Support for WebSocket-based push notifications for real-time events
5. Support for local and UniFi cloud accounts, including 2FA authentication

The following methods have been updated to use the node-unifi package:
- `getDevices()`
- `getDeviceDetails(deviceId)`
- `getClients()`
- `getClientDetails(clientId)`
- `blockClient(clientId)`
- `unblockClient(clientId)`
- `getNetworkStats()`
- `getSiteInfo()`
- `getDeviceStatistics(deviceId)`

## Implementation Details

### Authentication Changes
The authentication method has been changed from API key-based to username/password-based authentication:

1. The constructor now accepts a username instead of an API key
2. The `initialize` method now requires a password parameter
3. A `login` method has been added to handle authentication with the UniFi controller

### API Method Changes
All API methods have been simplified to use the corresponding methods from the node-unifi package:

- `getDevices()` → `controller.getDevices()`
- `getClients()` → `controller.getClientDevices()`
- `blockClient(clientId)` → `controller.blockClient(clientId)`
- `unblockClient(clientId)` → `controller.unblockClient(clientId)`
- `getNetworkStats()` → `controller.getHealthStats()`
- `getSiteInfo()` → `controller.getSiteSysinfo()`

For methods that don't have a direct equivalent in the node-unifi package, we retrieve the data and filter it as needed:
- `getDeviceDetails(deviceId)` → Gets all devices and finds the specific one by ID
- `getClientDetails(clientId)` → Gets all clients and finds the specific one by ID
- `getDeviceStatistics(deviceId)` → Gets device details and extracts relevant statistics

## Testing
A test script (`test-unifi-network-fix.js`) has been updated to verify the changes. To run the test:

1. Ensure the following environment variables are set:
   - `UNIFI_NETWORK_HOST`: The hostname or IP address of your UniFi Network Controller
   - `UNIFI_NETWORK_USERNAME`: Your username for the UniFi Network Controller
   - `UNIFI_NETWORK_PASSWORD`: Your password for the UniFi Network Controller
   - `UNIFI_NETWORK_PORT` (optional, defaults to 443): The port your UniFi Network Controller is running on
   - `UNIFI_NETWORK_SITE` (optional, defaults to 'default'): The site name in your UniFi Network Controller

2. Run the test script:
   ```
   node test-unifi-network-fix.js
   ```

## Notes
- The UniFi Network API integration now uses username/password authentication instead of API keys
- This update ensures compatibility with a wide range of UniFi Network Controller versions (v4.x.x up to v8.x.x)
- The node-unifi package handles SSL verification, which can be disabled for self-signed certificates
- The integration now has the potential to support WebSocket-based push notifications for real-time events in the future

## References
- UniFi Network API Documentation: https://ubntwiki.com/products/software/unifi-controller/api
- node-unifi package: https://github.com/jens-maus/node-unifi
- Last updated: 2025-07-22