# Google Forms Webhook Integration

This document describes how to use the Google Forms webhook integration to automatically create tasks from form responses.

## Overview

The Google Forms webhook integration allows you to:

1. Register webhooks for specific Google Forms
2. Map form fields to task properties
3. Define rules for auto-assigning tasks based on form responses
4. Automatically create tasks when new form responses are submitted

The system periodically checks for new form responses and creates tasks according to your configuration.

## Prerequisites

- Google Forms integration must be properly configured and authenticated
- Service account authentication is required for webhook functionality
- You must have access to the forms you want to create webhooks for

## Setting Up a Webhook

### 1. Identify the Form

First, you need to identify the Google Form you want to create a webhook for:

1. Navigate to the Google Forms page in the portal
2. Find the form you want to use
3. Note the form ID (visible in the URL or form details)

### 2. Identify Form Fields

Before creating a webhook, you need to understand the structure of your form:

1. Open the form in Google Forms
2. Note the questions/fields you want to map to task properties
3. For each field, note the field ID (you can find this in the form responses)

### 3. Create a Webhook

Use the API to create a webhook configuration:

```
POST /api/google-forms-webhooks
```

Request body:

```json
{
  "formId": "your-form-id",
  "formName": "Your Form Name",
  "fieldMappings": {
    "titleField": "question-id-for-title",
    "descriptionField": "question-id-for-description",
    "priorityField": "question-id-for-priority",
    "dueDateField": "question-id-for-due-date",
    "tagsField": "question-id-for-tags",
    "customFields": [
      {
        "formField": "question-id-for-custom-field",
        "taskField": "custom-task-field-name"
      }
    ]
  },
  "assignmentRules": [
    {
      "field": "question-id-to-check",
      "value": "value-to-match",
      "assignTo": "user-id-to-assign",
      "priority": 0
    }
  ],
  "defaultAssignee": "default-user-id",
  "taskType": "general"
}
```

### Field Mappings

- `titleField` (required): The form field to use as the task title
- `descriptionField` (optional): The form field to use as the task description
- `priorityField` (optional): The form field to determine task priority
- `dueDateField` (optional): The form field for the task due date
- `tagsField` (optional): The form field for task tags (comma-separated)
- `customFields` (optional): Additional field mappings for custom task properties

### Assignment Rules

Assignment rules determine which user a task should be assigned to based on form responses:

- `field`: The form field to check
- `value`: The value to match in the field
- `assignTo`: The user ID to assign the task to if the value matches
- `priority`: The priority of this rule (lower number = higher priority)

Multiple rules can be defined, and they are evaluated in priority order. The first matching rule is used.

If no rules match, the task is assigned to the `defaultAssignee` if provided, or left unassigned.

### Task Type

The `taskType` field determines what type of task to create:

- `general`: A standard task
- `maintenance`: A maintenance task (includes additional fields)

## Managing Webhooks

### List All Webhooks

```
GET /api/google-forms-webhooks
```

### Get Webhook Details

```
GET /api/google-forms-webhooks/:id
```

### Update a Webhook

```
PUT /api/google-forms-webhooks/:id
```

Request body: Same format as the create webhook request.

### Delete a Webhook

```
DELETE /api/google-forms-webhooks/:id
```

### Manually Process a Webhook

```
POST /api/google-forms-webhooks/:id/process
```

### Process All Webhooks (Admin Only)

```
POST /api/google-forms-webhooks/process-all
```

## Automatic Processing

The system automatically checks for new form responses every 5 minutes and creates tasks according to your webhook configuration.

When new tasks are created, a WebSocket event is broadcast with the details:

```
Event: google-forms-webhook-tasks
Data: {
  webhookId: "webhook-id",
  formName: "Form Name",
  newTasks: [
    {
      responseId: "response-id",
      taskId: "task-id",
      taskTitle: "Task Title"
    }
  ]
}
```

## Troubleshooting

### Common Issues

1. **Webhook not processing responses**: Ensure the webhook is active and the form ID is correct.

2. **Tasks not being created**: Check the field mappings to ensure they match the actual form fields.

3. **Tasks not being assigned**: Verify the assignment rules and ensure the user IDs are correct.

4. **Service account errors**: Ensure the Google Forms integration is properly configured with service account authentication.

### Logs

Check the server logs for detailed information about webhook processing:

- Webhook initialization: "Google Forms webhook monitoring started"
- Webhook processing: "Checking Google Forms webhooks for new responses..."
- Processing results: "Processed X new responses for form Y"

## Best Practices

1. **Use clear field mappings**: Choose form fields that clearly map to task properties.

2. **Set up appropriate assignment rules**: Create rules that distribute tasks efficiently.

3. **Include all necessary information**: Ensure your form collects all the information needed for task creation.

4. **Test your webhook**: After setting up a webhook, submit a test form response and verify that a task is created correctly.

5. **Monitor webhook activity**: Regularly check the webhook status and task creation to ensure everything is working as expected.