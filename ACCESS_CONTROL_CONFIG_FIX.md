# Access Control Configuration Fix

## Issue 1: Missing Environment Variables
The endpoint `https://portal.ukcsf.org/api/access-control/config` was incorrectly returning an empty response because the required environment variables for UniFi Access and Lenel S2 NetBox were not set in the `.env` file.

## Solution 1: Add Environment Variables
Added the following environment variables to the `.env` file:

### UniFi Access Configuration
```
UNIFI_ACCESS_HOST=************
UNIFI_ACCESS_API_KEY=your-unifi-access-api-key
UNIFI_ACCESS_PORT=443
```

### Lenel S2 NetBox Configuration
```
LENEL_S2_NETBOX_HOST=************
LENEL_S2_NETBOX_USERNAME=admin
LENEL_S2_NETBOX_PASSWORD=your-lenel-s2-netbox-password
LENEL_S2_NETBOX_PORT=443
LENEL_S2_NETBOX_LOCAL_NETWORK=true
```

## Important Notes
1. The values provided above are placeholders and need to be replaced with the actual values for your environment.
2. The IP addresses (`************` and `************`) should be updated to match the actual IP addresses of your UniFi Access and Lenel S2 NetBox systems.
3. The API key (`your-unifi-access-api-key`) and password (`your-lenel-s2-netbox-password`) must be replaced with the actual credentials.

## Deployment Instructions
1. Update the `.env` file on the production server with the correct environment variables.
2. Restart the application to apply the changes:
   ```
   pm2 restart all
   ```
   or
   ```
   docker-compose down && docker-compose up -d
   ```
   depending on your deployment method.

## Issue 2: Variable Shadowing in Lenel S2 NetBox API Initialization
Even with the environment variables properly set, the `/building-management/access-control` page was still showing "No access control systems are currently configured". This was because the `initializeLenelS2NetBoxAPI` method in `realtimeService.js` was not properly accessing the environment variables.

## Solution 2: Fix Variable Shadowing
The issue was caused by variable shadowing in the `initializeLenelS2NetBoxAPI` method. The method was using undefined local variables instead of the module-level variables that were properly initialized from environment variables.

1. Modified the `initializeLenelS2NetBoxAPI` method in `server/services/realtimeService.js` to use the module-level variables directly:

```javascript
async initializeLenelS2NetBoxAPI() {
  try {
    console.log('Initializing Lenel S2 NetBox API...');
    
    // Check if the necessary environment variables are set
    // Using module-level variables defined at the top of the file
    if (lenelHost && lenelUsername && lenelPassword) {
      console.log('Lenel S2 NetBox environment variables found, initializing API...');
      
      // Create and initialize the API if it hasn't been initialized yet
      if (!lenelS2NetBoxAPI) {
        console.log('Creating new Lenel S2 NetBox API instance');
        lenelS2NetBoxAPI = new LenelS2NetBoxAPI(lenelHost, lenelUsername, lenelPassword, lenelPort);
        await lenelS2NetBoxAPI.initialize();
      }
      
      console.log('Lenel S2 NetBox API initialized successfully');
    } else {
      console.log('Lenel S2 NetBox environment variables not set, skipping initialization');
    }
  } catch (error) {
    console.error('Error initializing Lenel S2 NetBox API:', error);
  }
}
```
```

2. Created a test script (`test-access-control-fix.js`) to verify that the fix works correctly.

## Verification
After deploying the changes, verify that the endpoint is now returning the expected configuration data by making a request to:
```
https://portal.ukcsf.org/api/access-control/config
```

The response should include configuration information for both UniFi Access and Lenel S2 NetBox systems.

Additionally, verify that the `/building-management/access-control` page now shows the configured access control systems instead of the "No access control systems are currently configured" message.