# Google Authentication Fix

## Issue
The Google sign-in functionality at `https://portal.ukcsf.org/api/auth/google` was returning a 500 error, preventing users from logging in with Google.

## Root Cause
The issue was caused by missing Google OAuth environment variables in the `.env` file. The Passport Google authentication strategy requires the following environment variables to be properly configured:

1. `GOOGLE_CLIENT_ID` - The Google OAuth client ID
2. `GOOGLE_CLIENT_SECRET` - The Google OAuth client secret
3. `GOOGLE_CALLBACK_URL` - The callback URL for Google OAuth

These variables were missing from the `.env` file, causing the Google authentication strategy to not be initialized. When users attempted to sign in with Google, the route existed but the strategy wasn't available, resulting in a 500 server error.

## Changes Made
1. Added the missing Google OAuth environment variables to the `.env` file:
   ```
   # Google OAuth credentials for user authentication
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   GOOGLE_CALLBACK_URL=https://portal.ukcsf.org/api/auth/google/callback
   ```

2. Created a test script (`test-google-auth.js`) to verify the Google authentication configuration.

## Steps to Complete the Fix
To fully resolve the issue, you need to:

1. Replace the placeholder values in the `.env` file with actual Google OAuth credentials:
   - Replace `your_google_client_id` with the actual Google OAuth client ID
   - Replace `your_google_client_secret` with the actual Google OAuth client secret

2. Obtain Google OAuth credentials:
   - Go to the [Google Cloud Console](https://console.cloud.google.com/)
   - Select your project or create a new one
   - Navigate to "APIs & Services" > "Credentials"
   - Create an OAuth client ID for a Web application
   - Add `https://portal.ukcsf.org/api/auth/google/callback` as an authorized redirect URI
   - Copy the client ID and client secret

3. Restart the application after updating the `.env` file.

## Verification
You can verify that the Google authentication is properly configured by running the test script:
```
node test-google-auth.js
```

The script will check if:
- The required Google OAuth environment variables are set with valid values
- The allowed domains are configured
- The Google strategy would be initialized based on the current configuration

## Additional Notes
- The `ALLOWED_DOMAINS` environment variable is already correctly set to `ukcsf.org`, which restricts Google sign-in to users with email addresses from that domain.
- The Google authentication route is configured to request various scopes including profile, email, and several Google API permissions, which are needed for the application's functionality.
- If you need to modify the requested scopes, you can edit the `routes/api/auth.js` file.