# WiiM Spotify Integration Changes

## Overview

This document describes the changes made to the WiiM integration to improve the Spotify functionality, allowing music to be selected to play and playlists to be browsed. The authentication for Spotify is now provided through environment variables, ensuring that every user in the portal uses the same Spotify account.

## Changes Made

1. **Added Spotify Environment Variables**
   - Added `SPOTIFY_CLIENT_ID`, `SPOTIFY_CLIENT_SECRET`, and `SPOTIFY_REFRESH_TOKEN` to the `.env` file
   - These credentials are used to authenticate with Spotify on behalf of all users

2. **Enhanced WiiM API Integration**
   - Added Spotify authentication using environment variables
   - Added methods for refreshing Spotify access tokens
   - Added methods for searching Spotify and playing tracks, albums, and artists

3. **Updated Server-Side Controller**
   - Added endpoints for searching Spotify
   - Added endpoints for playing Spotify tracks, albums, and artists

4. **Updated Client-Side Service**
   - Added methods for calling the new Spotify-related endpoints

5. **Enhanced User Interface**
   - Added search functionality to the Spotify tab
   - Added UI for browsing and playing tracks, albums, and artists
   - Improved the display of Spotify playlists

## Setup Instructions

1. **Obtain Spotify API Credentials**
   - Create a Spotify Developer account at [developer.spotify.com](https://developer.spotify.com)
   - Create a new application to get a Client ID and Client Secret
   - When creating the application, set the Redirect URI to `http://localhost:6000/callback` (this is required by Spotify but not actually used by the application)
   - To obtain a refresh token for your account, follow these steps:
     1. Go to the Spotify Developer Dashboard and select your application
     2. Note your Client ID and Client Secret
     3. Use the following URL in your browser, replacing `YOUR_CLIENT_ID` with your actual Client ID:
        ```
        https://accounts.spotify.com/authorize?client_id=YOUR_CLIENT_ID&response_type=code&redirect_uri=http://localhost:6000/callback&scope=user-read-private%20user-read-email%20playlist-read-private%20playlist-read-collaborative%20user-library-read%20streaming
        ```
     4. Log in with your Spotify account and authorize the application
     5. After authorization, you'll be redirected to a URL that looks like:
        ```
        http://localhost:6000/callback?code=AUTHORIZATION_CODE
        ```
     6. Copy the `AUTHORIZATION_CODE` from the URL (everything after `code=`)
     7. Use a terminal or API tool like Postman to make the following request:
        ```
        curl -X POST -H "Content-Type: application/x-www-form-urlencoded" -H "Authorization: Basic BASE64_ENCODED_CREDENTIALS" -d "grant_type=authorization_code&code=AUTHORIZATION_CODE&redirect_uri=http://localhost:6000/callback" https://accounts.spotify.com/api/token
        ```
        Where:
        - `BASE64_ENCODED_CREDENTIALS` is the base64 encoding of your Client ID and Client Secret joined by a colon (`client_id:client_secret`)
          - To generate this in a terminal: `echo -n "your_client_id:your_client_secret" | base64`
        - `AUTHORIZATION_CODE` is the code you copied in step 6
        
        Note: The authorization code expires after a few minutes, so you need to use it quickly after obtaining it.
     8. The response will contain a `refresh_token` field - this is your Spotify refresh token
     9. Save this refresh token securely as it doesn't expire unless you explicitly revoke access, and you'll need it for the next step

2. **Update Environment Variables**
   - Add the following variables to your `.env` file:
     ```
     SPOTIFY_CLIENT_ID=your_spotify_client_id
     SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
     SPOTIFY_REFRESH_TOKEN=your_spotify_refresh_token
     ```

3. **Restart the Application**
   - Restart the application to apply the changes

## Usage

1. **Browsing Spotify Playlists**
   - Navigate to the WiiM page in the portal
   - Select the Spotify tab
   - View and play your Spotify playlists

2. **Searching Spotify**
   - Enter a search query in the search box at the top of the Spotify tab
   - Click the search button or press Enter
   - Browse the search results by category (tracks, albums, artists, playlists)
   - Click on an item to play it

3. **Playing Spotify Content**
   - Click the play button next to any item to play it
   - Use the playback controls at the top of the page to control playback

## Technical Details

### Authentication Flow

The WiiM integration now uses the following flow for Spotify authentication:

1. When a Spotify-related action is requested, the system checks if it has a valid access token
2. If no token exists or the token has expired, it uses the refresh token from the environment variables to obtain a new access token
3. The access token is used to authenticate requests to the Spotify API
4. The WiiM device is instructed to play the selected content using the authenticated session

This ensures that all users of the portal use the same Spotify account, as specified in the environment variables.

### New API Endpoints

The following new API endpoints have been added:

- `GET /api/wiim/spotify/search` - Searches Spotify for tracks, albums, artists, or playlists
- `POST /api/wiim/spotify/tracks/:trackId/play` - Plays a Spotify track
- `POST /api/wiim/spotify/albums/:albumId/play` - Plays a Spotify album
- `POST /api/wiim/spotify/artists/:artistId/play` - Plays a Spotify artist's top tracks

### UI Enhancements

The Spotify tab in the WiiM page has been enhanced with:

- A search input field with a search button
- Section tabs to switch between playlists and search results
- Search results organized by category (tracks, albums, artists, playlists)
- Improved display of playlists with avatars and better styling