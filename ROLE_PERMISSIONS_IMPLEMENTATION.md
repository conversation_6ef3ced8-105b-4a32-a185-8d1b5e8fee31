# Role Permissions Implementation

This document describes the implementation of customizable permissions for roles in the CSF Portal.

## Overview

The role management page has been updated to allow permissions to be customized for each role. Permissions can now be set at different levels (read/write/admin) for each integration and platform feature.

## Changes Made

### 1. Frontend Changes

#### AdminRolesPage.js
- Added permission selection UI with accordions for integrations and platform features
- Added radio buttons for selecting permission levels (none, read, write, admin)
- Added special wildcard permission for admin role
- Updated permissions display in roles table to show the new permission format in a more readable way
- Added helper functions for permission management:
  - `hasPermission`: Checks if a permission exists in the permissions array
  - `getPermissionLevel`: Gets the permission level for an integration
  - `handlePermissionChange`: Handles permission level changes

### 2. Backend Changes

#### auth.js
- Updated hasPermission middleware to handle the new permission format:
  - Checks for global wildcard permission '*'
  - Checks for exact permission match
  - Handles hierarchical permissions (entity:level):
    - Checks for entity wildcard (entity:*)
    - Checks for higher level permissions:
      - If requesting 'read' access, 'write' and 'admin' permissions are sufficient
      - If requesting 'write' access, 'admin' permission is sufficient

## Permission Format

Permissions are stored as strings in the format: `{integration}:{level}`

Examples:
- `googleDrive:read` - Read-only access to Google Drive
- `lenelS2NetBox:write` - Read and write access to Lenel S2 NetBox
- `wiim:admin` - Full control over Wiim integration

Special permissions:
- `*` - Wildcard permission (grants all permissions)
- `{integration}:*` - All permissions for a specific integration

## Testing Instructions

1. Start the server and navigate to the role management page
2. Create a new role with specific permissions:
   - Set different permission levels for various integrations
   - Set different permission levels for platform features
3. Save the role and verify that the permissions are displayed correctly in the roles table
4. Edit the role and verify that the permission levels are correctly loaded
5. Test permission enforcement by:
   - Creating a user with the new role
   - Logging in as that user
   - Verifying that the user has access to the features according to the assigned permissions

## Troubleshooting

If permissions are not working as expected:
1. Check the browser console for any errors
2. Verify that the permissions are correctly saved in the database
3. Check the server logs for any errors related to permission checks
4. Ensure that the hasPermission middleware is being used correctly in the routes