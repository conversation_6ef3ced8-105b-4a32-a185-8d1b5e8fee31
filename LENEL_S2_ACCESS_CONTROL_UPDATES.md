# Lenel S2 NetBox Access Control Updates

## Overview
This document outlines the changes made to expand and update the Lenel S2 NetBox integration based on the NetBox_NBAPI2-3.pdf documentation, focusing specifically on access control features and excluding the intrusion panel functionality.

## Changes Made

### Server-Side Changes

1. **API Wrapper Enhancements**
   - Added methods for access level management:
     - `getAccessLevels()` - Get all access levels
     - `getAccessLevelDetails(accessLevelId)` - Get access level details
   - Added methods for access group management:
     - `getAccessGroups()` - Get all access groups
     - `getAccessGroupDetails(accessGroupId)` - Get access group details
   - Added methods for badge/credential management:
     - `getBadges()` - Get all badges
     - `getBadgeDetails(badgeId)` - Get badge details
   - Added methods for door schedule management:
     - `getDoorSchedules()` - Get all door schedules
     - `getDoorScheduleDetails(scheduleId)` - Get door schedule details
   - Added methods for door status monitoring:
     - `getDoorStatus(doorId)` - Get door status
     - `getDoors()` - Get all doors
   - Added methods for user access management:
     - `assignAccessLevelToCardholder(cardholderId, accessLevelId)` - Assign access level to cardholder
     - `removeAccessLevelFromCardholder(cardholderId, accessLevelId)` - Remove access level from cardholder
   - Added methods for user/cardholder management:
     - `createUser(userData)` - Create a new user/cardholder
     - `updateUser(cardholderId, userData)` - Update an existing user/cardholder
     - `deleteUser(cardholderId)` - Delete a user/cardholder
   - Added methods for user credential management:
     - `createUserCredential(cardholderId, credentialData)` - Create a new credential for a user
     - `updateUserCredential(cardholderId, credentialId, credentialData)` - Update a user credential
     - `deleteUserCredential(cardholderId, credentialId)` - Delete a user credential
     - `getUserCredentials(cardholderId)` - Get all credentials for a user
   - Added methods for user activity logs:
     - `getUserLogs(cardholderId, params)` - Get activity logs for a user

2. **Controller Enhancements**
   - Added controller methods to expose all new API wrapper methods as endpoints
   - Implemented proper error handling for all new endpoints
   - Ensured all endpoints are properly authenticated

3. **Route Enhancements**
   - Added routes for all new controller methods
   - Ensured all routes are protected with authentication middleware
   - Used RESTful naming conventions for all routes

### Client-Side Changes

1. **Service Enhancements**
   - Added client-side service methods to call all new API endpoints
   - Implemented proper error handling for all service methods
   - Added methods for door control:
     - `lockDoor(doorId)` - Lock a door
     - `unlockDoor(doorId)` - Unlock a door
   - Added methods for user management:
     - `createUser(userData)` - Create a new user
     - `updateUser(userId, userData)` - Update an existing user
     - `deleteUser(userId)` - Delete a user
     - `getUsers(params)` - Get all users
     - `getUser(userId)` - Get user details
   - Added methods for user credential management:
     - `createUserCredential(userId, credentialData)` - Create a new credential for a user
     - `updateUserCredential(userId, credentialId, credentialData)` - Update a user credential
     - `deleteUserCredential(userId, credentialId)` - Delete a user credential
     - `getUserCredentials(userId)` - Get all credentials for a user
   - Added methods for user activity logs:
     - `getUserLogs(userId, params)` - Get activity logs for a user

2. **UI Enhancements**
   - Enhanced the portal details view with a tabbed interface for different access control features:
     - Portal Details tab - Shows basic portal information
     - Access Levels tab - Shows a list of access levels
     - Access Groups tab - Shows a list of access groups
     - Badges tab - Shows a list of badges
     - Door Schedules tab - Shows a list of door schedules
     - Doors tab - Shows a list of doors with lock/unlock controls
     - Users tab - Shows a list of users with management options
   - Added lock/unlock functionality to the Portal Details tab
   - Added lock/unlock functionality to each door in the Doors tab
   - Added comprehensive user management functionality:
     - Table view of all users with options to view, edit, and delete
     - Form for creating and editing users
     - Management of user credentials (add, edit, delete)
     - Assignment and removal of access levels
     - Viewing of user activity logs
   - Implemented loading indicators for all data fetching operations
   - Added error handling and user feedback for all operations
   - Added snackbar notifications for user actions

## Implementation Details

### Access Control Features
The implementation focuses on the following access control features:

1. **Access Level Management**
   - Viewing access levels and their details
   - Understanding which doors and schedules are associated with each access level

2. **Access Group Management**
   - Viewing access groups and their details
   - Understanding which access levels are included in each group

3. **Badge/Credential Management**
   - Viewing badges and their details
   - Understanding which cardholders are associated with each badge

4. **Door Schedule Management**
   - Viewing door schedules and their details
   - Understanding when doors are scheduled to be locked/unlocked

5. **Door Status Monitoring**
   - Viewing the status of all doors
   - Understanding whether doors are locked/unlocked, open/closed

6. **User Access Management**
   - Assigning access levels to cardholders
   - Removing access levels from cardholders

7. **Door Control**
   - Locking and unlocking doors remotely
   - Providing immediate feedback on door control operations

8. **User Management**
   - Creating, updating, and deleting users/cardholders
   - Managing user information (name, email, phone, department, title, employee ID)
   - Viewing a list of all users with filtering and sorting options

9. **User Credential Management**
   - Creating, updating, and deleting user credentials
   - Managing credential properties (type, value, expiration date, status)
   - Viewing all credentials associated with a user

10. **User Activity Logs**
    - Viewing user activity logs
    - Filtering logs by date, event type, and location

## Testing

To test the implementation:

1. Navigate to the Lenel S2 NetBox page
2. Click the "View Details" button for a portal
3. Verify that the details dialog opens with the Portal Details tab selected
4. Test the lock/unlock buttons in the Portal Details tab
5. Navigate through the other tabs and verify that the data is displayed correctly
6. Test the lock/unlock buttons for doors in the Doors tab
7. Navigate to the Users tab and verify that the list of users is displayed correctly
8. Test creating a new user by clicking the "Add User" button
9. Test editing a user by clicking the edit button for a user
10. Test deleting a user by clicking the delete button for a user
11. Test viewing user details by clicking the view button for a user
12. Test adding a credential to a user
13. Test editing a credential
14. Test deleting a credential
15. Test assigning an access level to a user
16. Test removing an access level from a user
17. Verify that user activity logs are displayed correctly
18. Verify that error messages are displayed appropriately when operations fail
19. Verify that success messages are displayed appropriately when operations succeed

## Future Improvements

1. Add the ability to create, update, and delete access levels
2. Add the ability to create, update, and delete access groups
3. Add the ability to create, update, and delete badges
4. Add the ability to create, update, and delete door schedules
5. Add more detailed status information for doors
6. Add filtering and searching capabilities for all lists
7. Add pagination for large lists of data
8. Add bulk operations for user management (e.g., assign access level to multiple users at once)
9. Add import/export functionality for users and credentials
10. Add audit trail for user management operations
11. Add more detailed reporting for user activity