# Google Calendar Permissions Implementation

This document describes the changes made to implement proper permission checking for Google Calendar integration, ensuring that only calendars the currently logged-in user has access to are shown.

## Overview

The implementation ensures that:

1. Only calendars the logged-in user has access to are shown
2. Clear error messages are displayed when a user doesn't have the necessary permissions
3. Users are provided with a way to authenticate if needed

## Server-Side Changes

### 1. Added User-Only API Instance Function

Created a new function `getUserApiInstance` in `googleCalendarController.js` that only uses user tokens and doesn't fall back to global tokens:

```javascript
// Helper function to get API instance using only user tokens
// This ensures only calendars the user has access to are shown
const getUserApiInstance = async (req) => {
  // Check if user is logged in
  if (!req.user || !req.user.id) {
    throw new Error('User authentication required to access calendars');
  }

  try {
    // Get API instance with user tokens only
    const api = await getApiWithUserTokens(req.user.id);
    return api;
  } catch (error) {
    console.error('Error getting user API instance:', error);
    throw new Error('Unable to access user calendars: ' + error.message);
  }
};
```

### 2. Updated Calendar API Endpoints

Modified the following endpoints to use the new `getUserApiInstance` function:

- `listCalendars`: Only returns calendars the user has access to
- `getCalendar`: Only allows access to calendars the user has permission for
- `listEvents`: Only allows access to events from calendars the user has permission for

### 3. Improved Error Handling

Added more specific error messages for different permission-related issues:

- 401 Unauthorized: When the user is not logged in
- 403 Forbidden: When the user doesn't have the necessary permissions
- 404 Not Found: When the calendar is not found or the user doesn't have permission to access it

## Client-Side Changes

### 1. Updated Error Handling

Modified the error handling in the GoogleCalendarPage component to handle permission-related errors:

- Updated `loadCalendars`, `fetchCalendars`, and `fetchEvents` functions to handle specific error types
- Added structured error objects with message, authentication URL, and error type

### 2. Enhanced UI for Permission Errors

Updated the UI to display more detailed error messages and show a login/authentication button when needed:

- Different severity levels for different types of errors
- Action button for authentication and permission errors
- Clear error messages explaining the permission issue

## Testing

To verify that the implementation works correctly:

1. Log in as different users with different Google Calendar permissions
2. Verify that only calendars the user has access to are shown
3. Try accessing a calendar the user doesn't have permission for and verify that an appropriate error message is displayed
4. Log out and verify that authentication errors are handled correctly

## Security Considerations

This implementation improves security by:

1. Ensuring that users can only access calendars they have permission for
2. Not falling back to global tokens, which could potentially expose calendars the user shouldn't have access to
3. Providing clear error messages without exposing sensitive information

## Future Improvements

Potential future improvements include:

1. Adding more granular permission checking for specific calendar operations
2. Implementing a permission caching mechanism to improve performance
3. Adding UI indicators to show which calendars the user has read-only vs. read-write access to