# Lenel S2 NetBox Lazy Initialization

## Problem

The Lenel S2 NetBox integration was being initialized on every page load, causing unnecessary API requests to the Lenel system even on pages that don't use Lenel features. This was inefficient and could potentially cause performance issues.

## Solution

Implemented lazy initialization for the Lenel S2 NetBox API to ensure it's only initialized when actually needed. This prevents unnecessary API requests on pages that don't use Lenel features.

### Changes Made

1. **Modified the Lenel S2 NetBox controller** (`server/controllers/lenelS2NetBoxController.js`):
   - Replaced immediate initialization at the module level with lazy initialization
   - Added logic to initialize the API only when an endpoint is actually called
   - Updated the `ensureApiInitialized` function to create and initialize the API if it hasn't been initialized yet

2. **Modified the realtime service** (`server/services/realtimeService.js`):
   - Replaced immediate initialization at the module level with lazy initialization
   - Updated the `initializeLenelS2NetBoxAPI` method to create and initialize the API only if it hasn't been initialized yet
   - Updated all methods that use the Lenel API to ensure it's initialized before use:
     - `checkActivityLog`
     - `checkEvacuationStatus`
     - `checkElevatorStatus`

3. **Created a test script** (`test-lenel-lazy-initialization.js`):
   - Tests that the Lenel API is only initialized when needed
   - Makes a request to a non-Lenel endpoint and verifies the Lenel API is not initialized
   - Makes a request to a Lenel endpoint and verifies the Lenel API is initialized

### Benefits

1. **Improved Performance**: The Lenel API is only initialized when actually needed, reducing unnecessary API requests and improving page load times.

2. **Reduced Resource Usage**: By not initializing the Lenel API on every page load, we reduce the resource usage of the application.

3. **Better User Experience**: Pages that don't use Lenel features will load faster and be more responsive.

### Pages That Use Lenel Features

The following pages actually need Lenel features and will initialize the Lenel API:

1. **LenelS2NetBoxPage** (`client/src/pages/LenelS2NetBox/LenelS2NetBoxPage.js`):
   - Main page for Lenel S2 NetBox features
   - Makes API calls to fetch portals, access levels, users, etc.

2. **BuildingManagementPage** (`client/src/pages/BuildingManagement/BuildingManagementPage.js`):
   - Checks the status of the Lenel integration
   - Displays links to the Lenel S2 NetBox page if it's active

### Testing

To test the changes, run the test script:

```bash
node test-lenel-lazy-initialization.js
```

This script will:
1. Make a request to a non-Lenel endpoint and check if the Lenel API is initialized
2. Make a request to a Lenel endpoint and verify that the Lenel API is initialized

Check the server logs to verify that the Lenel API is only initialized when needed. You should see the message "Initializing Lenel S2 NetBox API on demand" only when a Lenel endpoint is accessed, not when other endpoints are accessed.

## Conclusion

By implementing lazy initialization for the Lenel S2 NetBox API, we've ensured that it's only initialized when actually needed, preventing unnecessary API requests and improving the performance of the application.