# UniFi Network Fallback Endpoints Fix

## Issue Description
The `/api/unifi-network/devices` endpoint was returning a 404 error. This indicated that the API endpoint being used in the UniFi Network API wrapper was not accessible or had changed in the UniFi Network Controller.

## Root Cause
The UniFi Network API wrapper was only trying a single endpoint format:
```javascript
const endpoint = `/proxy/network/integration/v1/sites/${this.site}/devices`;
```

Different versions of the UniFi Network Controller use different API endpoint structures. If the controller doesn't support this specific endpoint format, it would return a 404 error.

## Fix Applied
Added a fallback mechanism to the UniFi Network API wrapper that tries multiple endpoint formats if the primary one fails. This ensures compatibility with different versions of the UniFi Network Controller.

### Changes to `getDevices` Method
Updated the `getDevices` method to try the following endpoints in sequence:
```javascript
const endpoints = [
  `/proxy/network/integration/v1/sites/${this.site}/devices`,
  `/api/s/${this.site}/stat/device`,
  `/api/site/${this.site}/stat/device`,
  `/v2/api/site/${this.site}/device`
];
```

The method now tries each endpoint in sequence until one succeeds. If an endpoint returns a 404 error, it tries the next one. This ensures that the API can work with different versions of the UniFi Network Controller.

### Changes to `getDeviceDetails` Method
Similarly, updated the `getDeviceDetails` method to try the following endpoints in sequence:
```javascript
const endpoints = [
  `/proxy/network/integration/v1/sites/${this.site}/devices/${deviceId}`,
  `/api/s/${this.site}/stat/device/${deviceId}`,
  `/api/site/${this.site}/stat/device/${deviceId}`,
  `/v2/api/site/${this.site}/device/${deviceId}`
];
```

If all direct endpoints fail, it falls back to getting all devices and filtering for the specific device ID, which was the previous fallback mechanism.

## Response Format Handling
Added logic to handle different response formats based on the endpoint:
```javascript
// Handle different response formats based on the endpoint
if (endpoint.includes('/proxy/network/integration/v1/')) {
  return response.data.data;
} else if (endpoint.includes('/api/s/') || endpoint.includes('/api/site/')) {
  return response.data.data || response.data;
} else {
  return Array.isArray(response.data) ? response.data : (response.data.data || response.data);
}
```

This ensures that the API can correctly parse the response data regardless of which endpoint format is used.

## Testing
Created a test script (`test-unifi-network-fallback-endpoints.js`) to verify the fix. The script tests:
1. The `getDevices` method directly to ensure it can fetch devices using fallback endpoints
2. The API endpoint `/api/unifi-network/devices` directly to ensure it returns a successful response

## Expected Outcome
With these changes, the `/api/unifi-network/devices` endpoint should now work correctly, even if the primary endpoint format is not supported by the UniFi Network Controller. The fallback mechanism ensures compatibility with different versions of the controller.

## Additional Notes
- The fix is minimal and focused specifically on resolving the 404 error without introducing any new functionality or changing existing behavior.
- The fallback mechanism is designed to be transparent to the caller - the API methods still return the same data structure regardless of which endpoint format is used.
- This approach is more robust than hardcoding a single endpoint format, as it can adapt to different controller versions and configurations.

## Date Fixed
2025-07-30