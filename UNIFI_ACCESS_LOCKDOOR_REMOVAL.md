# UniFi Access lockDoor Function Removal

## Overview

The `lockDoor` function has been removed from the UniFi Access API implementation because it does not actually exist in the UniFi Access API according to the official API documentation.

## Changes Made

1. **Removed the `lockDoor` function from the UniFi Access API implementation**
   - Removed the function from `server/integrations/unifiAccess/unifiAccessAPI.js`
   - Added a comment explaining that the function doesn't exist in the UniFi API

2. **Updated the UniFi Access controller**
   - Modified the `lockDoor` method in `server/controllers/unifiAccessController.js` to return an error message
   - Updated the JSDoc comments to explain that the function doesn't exist in the UniFi API

3. **Updated the routes file**
   - Added a comment to the lock door route in `routes/api/unifiAccess.js` explaining that the functionality is not supported

4. **Updated the access control controller**
   - Modified the `controlDoor` method in `server/controllers/accessControlController.js` to handle the case where the 'lock' action is requested for a UniFi Access door
   - Added a comment explaining that the function doesn't exist in the UniFi API

5. **Created a test script**
   - Created `test-unifi-access-lock-removal.js` to verify that the API still works correctly without the `lockDoor` function
   - The test script verifies that attempting to lock a door returns the appropriate error message

## Reason for Removal

The `lockDoor` function was removed because it does not actually exist in the UniFi Access API according to the official API documentation. Attempting to use this function would result in API errors, so it's better to explicitly handle this case and return a clear error message.

## Impact

The removal of the `lockDoor` function has the following impact:

1. **API Endpoints**
   - The `/api/unifi-access/doors/:id/lock` endpoint now returns a 400 error with a message explaining that the functionality is not supported
   - All other endpoints continue to work as before

2. **Access Control**
   - Attempting to lock a UniFi Access door through the access control system now returns a 400 error with a message explaining that the functionality is not supported
   - All other access control functionality continues to work as before

## Testing

A test script has been created to verify that the API still works correctly without the `lockDoor` function. The script tests:

1. That the UniFi Access API still initializes correctly
2. That the `unlockDoor` function still works correctly
3. That attempting to lock a door returns the appropriate error message
4. That the access control controller correctly handles the lock action for UniFi Access doors

To run the test script:

```bash
node test-unifi-access-lock-removal.js
```

## Conclusion

The removal of the `lockDoor` function improves the reliability of the UniFi Access integration by ensuring that it only uses functions that actually exist in the UniFi Access API. The changes have been made in a way that maintains backward compatibility and provides clear error messages when the unsupported functionality is requested.