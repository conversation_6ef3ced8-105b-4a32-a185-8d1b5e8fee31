# Colorlit Z4 Pro LED Controller Integration

This document provides an overview of the Colorlit Z4 Pro LED Controller integration implemented in the CSF Portal.

## Overview

The Colorlit Z4 Pro LED Controller integration allows users to control LED lighting systems through the CSF Portal. The integration provides a user-friendly interface for controlling various aspects of the LED controller, including:

- Power control (on/off)
- Color control (RGB+W)
- Brightness control
- Speed control for effects
- Mode selection
- Zone/group selection

## Implementation Details

The integration follows the standard pattern used in the CSF Portal for integrations:

### Server-Side Components

1. **API Implementation** (`server/integrations/colorlit/coloritAPI.js`)
   - Handles communication with the Colorlit device
   - Provides methods for all controller operations
   - Includes error handling and retry logic

2. **Controller** (`server/controllers/coloritController.js`)
   - Initializes the API with configuration from environment variables or database
   - Provides methods for handling API requests
   - Includes validation and error handling

3. **Database Model** (`models/ColoritConfig.js`)
   - Stores configuration in the database when environment variables are not used
   - Includes host, port, authentication credentials, etc.

4. **API Routes** (`routes/api/colorlit.js`)
   - Defines endpoints for all controller methods
   - Protects routes with authentication middleware

### Client-Side Components

1. **Service Module** (`client/src/services/coloritService.js`)
   - Provides methods for making API calls to the backend
   - Handles error reporting and data formatting

2. **Frontend Component** (`client/src/pages/Colorlit/ColoritPage.js`)
   - Provides a user interface for controlling the LED controller
   - Organized with tabs for different functionality:
     - Control tab: Power, color, brightness, and speed controls
     - Modes tab: Selection of lighting modes/effects
     - Zones tab: Selection of zones/groups
     - Settings tab: Configuration of the integration

## Configuration

The integration can be configured using environment variables or through the user interface.

### Environment Variables

The following environment variables can be set in the `.env` file:

```
# Colorlit Z4 Pro LED Controller configuration
COLORLIT_HOST=your_colorlit_host
COLORLIT_PORT=80
COLORLIT_API_KEY=your_colorlit_api_key
COLORLIT_USERNAME=your_colorlit_username
COLORLIT_PASSWORD=your_colorlit_password
```

- `COLORLIT_HOST`: The hostname or IP address of the Colorlit controller (required)
- `COLORLIT_PORT`: The port number (default: 80 for HTTP, 443 for HTTPS)
- `COLORLIT_API_KEY`: The API key for authentication (if required)
- `COLORLIT_USERNAME`: The username for authentication (if required)
- `COLORLIT_PASSWORD`: The password for authentication (if required)

### User Interface Configuration

If environment variables are not set, the integration can be configured through the user interface:

1. Navigate to the Colorlit page in the CSF Portal
2. If not configured, you'll see a configuration form
3. Enter the required information:
   - Host: IP address or hostname of the Colorlit controller
   - Port: Port number (default: 80)
   - API Key: API key for authentication (if required)
   - Username: Username for authentication (if required)
   - Password: Password for authentication (if required)
4. Click "Save Configuration"

## Usage

Once configured, the Colorlit integration provides the following functionality:

### Power Control

- Turn the LED controller on or off with the power button

### Color Control

- Select colors using the color picker
- Adjust the white value using the slider
- Apply the selected color with the "Apply Color" button

### Brightness Control

- Adjust brightness using the slider
- Apply the selected brightness with the "Apply Brightness" button

### Speed Control

- Adjust speed for effects using the slider
- Apply the selected speed with the "Apply Speed" button

### Mode Selection

- Select from available lighting modes/effects
- Click on a mode to activate it

### Zone Selection

- Select from available zones/groups
- Click on a zone to control it

## Testing

A test script is provided to verify the functionality of the integration:

```bash
node test-colorlit-integration.js
```

This script tests all the API endpoints and verifies that the integration is working correctly.

## Troubleshooting

If you encounter issues with the Colorlit integration, check the following:

1. **Connection Issues**
   - Verify that the Colorlit controller is powered on and connected to the network
   - Check that the host and port are correct
   - Ensure that the CSF Portal server can reach the Colorlit controller

2. **Authentication Issues**
   - Verify that the API key, username, and password are correct
   - Check if the Colorlit controller requires authentication

3. **API Errors**
   - Check the server logs for detailed error messages
   - Verify that the Colorlit controller firmware is up to date

## Future Enhancements

Potential future enhancements for the Colorlit integration:

1. Support for scheduled lighting changes
2. Integration with scenes and automations
3. Support for firmware updates
4. Enhanced error reporting and diagnostics
5. Support for multiple controllers

## References

- [Colorlit Z4 Pro LED Controller Documentation](https://www.colorlit.com/z4-pro-documentation)
- [CSF Portal Integration Guide](https://portal.ukcsf.org/docs/integration-guide)