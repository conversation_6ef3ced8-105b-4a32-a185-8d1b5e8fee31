# Gmail Ticketing System Setup Guide

This guide will help you set up the Gmail API integration for your ticketing system with a fully automated setup script.

## Prerequisites

1. **Google Cloud CLI**: Install from https://cloud.google.com/sdk/docs/install
2. **Google Workspace Admin Access**: You need admin access to create OAuth credentials
3. **Node.js**: Version 14 or higher

## Quick Setup (Automated)

### Step 1: Install Dependencies

```bash
npm install @google-cloud/pubsub googleapis imap mailparser
```

### Step 2: Run the Setup Script

```bash
node setup-gmail-ticketing.js
```

The script will guide you through:
- ✅ Google Cloud authentication
- ✅ Project setup and API enablement
- ✅ Service account creation
- ✅ Pub/Sub topic and subscription setup
- ✅ Environment variable configuration
- ✅ Permission grants and testing

### Step 3: Complete OAuth Setup

When prompted, the script will ask you to:

1. **Create OAuth 2.0 Credentials**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
   - Click "Create Credentials" → "OAuth 2.0 Client IDs"
   - Choose "Desktop application"
   - Download the JSON file
   - Save it as `gmail-setup-credentials.json` in your project root

2. **Authorize the Application**:
   - The script will provide an authorization URL
   - Visit the URL and grant permissions
   - Copy the authorization code back to the script

### Step 4: Set Up App Password

After the script completes:

1. Go to [Google Account Security](https://myaccount.google.com/security)
2. Enable 2-Step Verification if not already enabled
3. Go to "App passwords"
4. Generate a password for "Mail"
5. Update your `.env` file with the app password:

```env
SMTP_PASS=your-generated-app-password
```

### Step 5: Update Portal URL

Update your `.env` file with your actual portal URL:

```env
PORTAL_URL=https://your-actual-portal.com
```

## What the Script Does

### 🔧 **Automated Configuration**:
- Creates Google Cloud project resources
- Enables Gmail API, Pub/Sub API, and IAM API
- Creates service account with proper permissions
- Sets up Pub/Sub topic and subscription for Gmail push notifications
- Generates secure webhook secret
- Updates environment variables automatically

### 📡 **Gmail Integration Setup**:
- Configures Gmail push notifications via Pub/Sub
- Sets up message threading and reply detection
- Handles external email processing
- Creates proper IAM bindings for Gmail API access

### 🔐 **Security Configuration**:
- Creates dedicated service account with minimal permissions
- Generates secure API keys and tokens
- Sets up proper OAuth 2.0 flow
- Configures webhook authentication

## Environment Variables Generated

The script will automatically add these to your `.env` file:

```env
# Gmail Ticketing System Configuration
GOOGLE_GMAIL_PROJECT_ID=your-project-id
GOOGLE_GMAIL_TOPIC_NAME=gmail-tickets
GOOGLE_GMAIL_SUBSCRIPTION_NAME=gmail-tickets-sub
GOOGLE_GMAIL_SERVICE_ACCOUNT_KEY=./gmail-ticketing-service-account-key.json
GMAIL_MONITORED_EMAIL=<EMAIL>

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Ticket System Configuration
TICKET_EMAIL_FROM=<EMAIL>
PORTAL_URL=https://yourportal.com
WEBHOOK_SECRET=auto-generated-secure-key
```

## Testing the Setup

### 1. **Start Your Application**:
```bash
npm run dev
```

### 2. **Send Test Email**:
- Send an email to your monitored address
- Check server logs for processing confirmation
- Verify ticket creation in the portal

### 3. **Test Email Threading**:
- Reply to the auto-response email
- Verify the reply is added to the existing ticket

## Troubleshooting

### Common Issues:

1. **"Authentication failed"**:
   - Ensure Google Cloud CLI is authenticated: `gcloud auth login`
   - Check that OAuth credentials are properly created

2. **"Permission denied"**:
   - Verify service account has proper roles
   - Check that Gmail API is enabled for your project

3. **"Topic not accessible"**:
   - Ensure Pub/Sub API is enabled
   - Verify service account has pubsub.admin role

4. **"Gmail watch failed"**:
   - Check that the monitored email has Gmail API access
   - For Google Workspace, verify domain-wide delegation

### Manual Verification:

```bash
# Test Google Cloud authentication
gcloud auth list

# Test project access
gcloud projects describe YOUR_PROJECT_ID

# Test Pub/Sub access
gcloud pubsub topics list

# Test service account key
cat gmail-ticketing-service-account-key.json
```

## Advanced Configuration

### Domain-Wide Delegation (Google Workspace)

If using Google Workspace, you may need to enable domain-wide delegation:

1. Go to [Google Admin Console](https://admin.google.com)
2. Navigate to Security → API Controls → Domain-wide Delegation
3. Add your service account client ID
4. Grant these scopes:
   - `https://www.googleapis.com/auth/gmail.readonly`
   - `https://www.googleapis.com/auth/pubsub`

### Custom Configuration

You can customize the setup by modifying these variables in the script:

```javascript
this.config = {
  topicName: 'gmail-tickets',           // Pub/Sub topic name
  subscriptionName: 'gmail-tickets-sub', // Pub/Sub subscription name
  // ... other configuration options
};
```

## Security Best Practices

1. **Rotate Keys Regularly**: Set up key rotation for service accounts
2. **Limit Permissions**: The script uses minimal required permissions
3. **Monitor Access**: Set up logging and monitoring for API access
4. **Secure Storage**: Keep service account keys secure and never commit to git
5. **HTTPS Only**: Always use HTTPS for webhook endpoints

## Support

If you encounter issues:

1. Check the setup logs for specific error messages
2. Verify all prerequisites are met
3. Ensure proper Google Cloud permissions
4. Test individual components (Gmail API, Pub/Sub, etc.)

The setup script provides detailed logging to help diagnose any issues during configuration.