# Planning Center Resources API Implementation

## Overview

This document describes the implementation of the Planning Center Resources API in the CSF Portal application. The implementation follows the API documentation available at https://developer.planning.center/docs/#/apps/calendar/2022-07-07/vertices/resource.

## Changes Made

The following changes were made to properly implement the Planning Center Resources API:

1. **Enhanced the Resources API in planningCenterAPI.js**:
   - Improved the existing `getResources` method to process included data if it exists
   - Added a new `getResourceById` method to fetch a single resource by its ID
   - Both methods support query parameters and process included data similar to other endpoints

2. **Updated the Controller in planningCenterController.js**:
   - Added a `formatResourcesData` function to transform the raw API response into a more human-readable format
   - Updated the existing `getResources` endpoint to use this formatting function
   - Added a new `getResourceById` endpoint to fetch and format a single resource

3. **Added a New Route in planningCenter.js**:
   - Added a route for the `getResourceById` endpoint at `/resources/:id`
   - The route is protected by the `isAuthenticated` middleware

## API Endpoints

### Get All Resources

```
GET /api/planning-center/resources
```

This endpoint fetches all resources from the Planning Center API and formats the response data for use in the application. It supports query parameters that can be passed to the Planning Center API.

### Get a Single Resource by ID

```
GET /api/planning-center/resources/:id
```

This endpoint fetches a single resource by its ID from the Planning Center API and formats the response data for use in the application. It supports query parameters that can be passed to the Planning Center API.

## Response Format

The response data is formatted to be more human-readable and easier to use in the application. The formatted response includes:

- **id**: The resource ID
- **name**: The resource name (defaults to 'Unnamed Resource' if not provided)
- **description**: The resource description
- **kind**: The resource kind/type
- **color**: The resource color
- **createdAt**: The creation timestamp
- **updatedAt**: The last update timestamp
- **linkedData**: Any linked data that was processed from included resources
- **rawData**: The original resource data for reference

## Included Data Processing

The implementation processes included data in the API response, similar to how it's done for people. This allows the application to access related resources that are included in the response without making additional API calls.

## Error Handling

Both the API and controller implementations include proper error handling to ensure that any issues with the API calls are caught and reported appropriately.

## Conclusion

The Planning Center Resources API is now properly implemented and able to correctly use the response data in the application. The implementation follows the same patterns used for other Planning Center API endpoints in the codebase, ensuring consistency and maintainability.