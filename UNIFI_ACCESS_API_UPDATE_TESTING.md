# UniFi Access API Update Testing Guide

This document provides instructions for testing the updated UniFi Access API implementation to ensure it works correctly with the rest of the system and maintains backward compatibility.

## Changes Made

The UniFi Access API implementation has been updated to align with the official API documentation:

1. Updated the base URL from `/api` to `/v1`
2. Updated the endpoint for getting access points from `/access-points` to `/devices`
3. Improved error handling for all methods
4. Added detailed comments referencing the official API documentation

## Testing the API Implementation

### 1. Run the Test Script

A test script has been created to verify the API connections and responses:

```bash
node test-unifi-access-api-update.js
```

Before running the script, make sure to set your actual UniFi Access credentials in the environment variables:

```bash
export UNIFI_ACCESS_HOST=your-unifi-access-host
export UNIFI_ACCESS_API_KEY=your-unifi-access-api-key
export UNIFI_ACCESS_PORT=443
```

The test script will:
- Initialize the UniFi Access API
- Test authentication
- Test various API endpoints (doors, devices, users, events, system status)
- Report the results of each test

### 2. Test Integration with Access Control Controller

To verify that the updated API works correctly with the access control controller, run the following test:

```bash
node test-access-control-integration.js
```

This will test the integration between the UniFi Access API and the access control controller.

### 3. Test Door Status Monitoring

To verify that door status monitoring works correctly with the updated API, run:

```bash
node test-door-status-monitoring.js
```

This will test the door status monitoring functionality, which relies on the UniFi Access API.

## Ensuring Backward Compatibility

The updated API implementation maintains backward compatibility with existing code by:

1. Keeping the same method names and signatures
2. Returning data in the same format as before
3. Handling errors in a way that's compatible with existing error handling

To verify backward compatibility, check that:

- All existing functionality continues to work as expected
- No new errors are introduced
- The system can still communicate with the UniFi Access controller

## Troubleshooting

If you encounter issues with the updated API implementation:

1. Check the console logs for detailed error messages
2. Verify that your UniFi Access credentials are correct
3. Ensure that the UniFi Access controller is accessible from the server
4. Check that the UniFi Access controller is running the expected version of the API

## Rollback Procedure

If necessary, you can roll back to the previous API implementation by:

1. Reverting the changes to `server/integrations/unifiAccess/unifiAccessAPI.js`
2. Restarting the server

## Additional Notes

- The updated API implementation includes improved error handling, which may provide more detailed error messages than before
- The base URL has been updated to `/v1` to align with the official API documentation
- The endpoint for getting access points has been updated from `/access-points` to `/devices`