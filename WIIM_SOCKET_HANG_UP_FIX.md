# WiiM Socket Hang Up Fix

## Issue Description
Users were experiencing "socket hang up" errors when fetching WiiM playlists. This error occurs when the server closes the connection before a response is received, typically due to a timeout or network issue.

```json
{
    "message": "Error fetching WiiM playlists",
    "error": "socket hang up"
}
```

## Root Cause Analysis
The root cause of the issue was insufficient timeout and error handling in the WiiM API implementation. The axios instances used for communication with the WiiM device and Spotify API were not configured with proper timeout settings or retry logic, which could lead to "socket hang up" errors if the device or API didn't respond in a timely manner.

## Changes Made

### 1. Added Timeout Configuration
Added explicit timeout settings to the axios instances to prevent requests from hanging indefinitely:
- 10 seconds timeout for WiiM device requests
- 15 seconds timeout for Spotify API requests
- Longer timeouts (15-20 seconds) specifically for playlist fetching operations

### 2. Implemented Retry Logic
Added retry logic to automatically retry failed requests:
- Maximum of 3 retries for each request
- 1 second delay between retries
- Detailed logging of retry attempts

### 3. Added Keep-Alive Connections
Configured HTTP and HTTPS agents with keepAlive enabled to maintain connections:
- HTTP agent for WiiM device communication
- HTTPS agent for Spotify API communication

### 4. Enhanced Error Handling
Improved error handling to provide more detailed error information:
- Added detailed error logging with message, code, stack trace, URL, method, and timeout
- Created more descriptive error objects with additional properties for debugging
- Added specific error types (isWiimError, isSpotifyError) for better error identification

### 5. Created Test Script
Developed a test script (`test-wiim-playlists.js`) to verify the fix:
- Tests connection to the WiiM device
- Tests playlist fetching functionality
- Tests Spotify playlist fetching functionality
- Provides detailed error information if tests fail

## Implementation Details

### WiiM API Axios Instance
```javascript
// Create axios instance for HTTP communication with WiiM device
this.axios = axios.create({
  baseURL: this.baseURL,
  headers: {
    'Content-Type': 'application/json'
  },
  // Add timeout configuration to prevent socket hang up errors
  timeout: 10000, // 10 seconds
  // Add HTTP agent with keepAlive to maintain connection
  httpAgent: new require('http').Agent({ keepAlive: true }),
  // Add retry configuration
  maxRetries: 3,
  retryDelay: 1000
});

// Add request interceptor for retry logic
this.axios.interceptors.response.use(null, async (error) => {
  const config = error.config;
  
  // If we don't have a config or we've already retried the maximum number of times, throw the error
  if (!config || !config.maxRetries || config._retryCount >= config.maxRetries) {
    return Promise.reject(error);
  }
  
  // Increment the retry count
  config._retryCount = config._retryCount || 0;
  config._retryCount++;
  
  // Create a new promise to handle the retry
  const retryDelay = config.retryDelay || 1000;
  await new Promise(resolve => setTimeout(resolve, retryDelay));
  
  // Log the retry attempt
  console.log(`Retrying WiiM API request (${config._retryCount}/${config.maxRetries}): ${config.url}`);
  
  // Return the axios instance to retry the request
  return this.axios(config);
});
```

### Spotify API Axios Instance
```javascript
// Create a separate axios instance for Spotify API calls
this.spotifyAxios = axios.create({
  baseURL: 'https://api.spotify.com/v1',
  headers: {
    'Content-Type': 'application/json'
  },
  // Add timeout configuration to prevent socket hang up errors
  timeout: 15000, // 15 seconds for Spotify API (might be slower than local WiiM device)
  // Add HTTPS agent with keepAlive to maintain connection
  httpsAgent: new https.Agent({ keepAlive: true }),
  // Add retry configuration
  maxRetries: 3,
  retryDelay: 1000
});

// Add request interceptor for retry logic
this.spotifyAxios.interceptors.response.use(null, async (error) => {
  const config = error.config;
  
  // If we don't have a config or we've already retried the maximum number of times, throw the error
  if (!config || !config.maxRetries || config._retryCount >= config.maxRetries) {
    return Promise.reject(error);
  }
  
  // Increment the retry count
  config._retryCount = config._retryCount || 0;
  config._retryCount++;
  
  // Create a new promise to handle the retry
  const retryDelay = config.retryDelay || 1000;
  await new Promise(resolve => setTimeout(resolve, retryDelay));
  
  // Log the retry attempt
  console.log(`Retrying Spotify API request (${config._retryCount}/${config.maxRetries}): ${config.url}`);
  
  // Return the axios instance to retry the request
  return this.spotifyAxios(config);
});
```

### Enhanced Error Handling in getPlaylists
```javascript
async getPlaylists() {
  try {
    // Note: getPlaylists command is not documented in official WiiM API
    // This command may not work on all WiiM devices
    console.warn('getPlaylists command is not officially documented and may not work');
    
    // Use a longer timeout for playlist fetching specifically
    const response = await this.axios.get('/httpapi.asp?command=getPlaylists', {
      timeout: 15000 // 15 seconds timeout for playlist fetching
    });
    
    return response.data;
  } catch (error) {
    // Enhanced error logging with more details
    console.error('Error fetching WiiM playlists:', {
      message: error.message,
      code: error.code,
      stack: error.stack,
      url: error.config?.url,
      method: error.config?.method,
      timeout: error.config?.timeout
    });
    
    // Create a more descriptive error
    const enhancedError = new Error(`Error fetching WiiM playlists: ${error.message}`);
    enhancedError.originalError = error;
    enhancedError.isWiimError = true;
    
    throw enhancedError;
  }
}
```

## Testing
A test script (`test-wiim-playlists.js`) was created to verify the fix. The script tests:
1. Connection to the WiiM device using `getDeviceInfo`
2. Playlist fetching using `getPlaylists`
3. Spotify playlist fetching using `getSpotifyPlaylists`

The test script can be run with:
```
node test-wiim-playlists.js [host] [port]
```

Where `host` is the IP address of the WiiM device and `port` is the port number (default: 80).

## Conclusion
The implemented changes should make the WiiM API more resilient to network issues and less likely to experience "socket hang up" errors. The timeout settings ensure that requests don't hang indefinitely, and the retry logic provides resilience against temporary network issues. The enhanced error handling provides more detailed error information for debugging.

These changes should resolve the "Error fetching WiiM playlists" issue with "socket hang up" errors that users were experiencing.