# CSF Portal Monitoring and Logging

This document outlines the monitoring and logging setup for the CSF Portal application.

## Table of Contents

- [Logging Setup](#logging-setup)
- [Application Monitoring](#application-monitoring)
- [Server Monitoring](#server-monitoring)
- [Database Monitoring](#database-monitoring)
- [Alerting](#alerting)
- [Log Retention and Analysis](#log-retention-and-analysis)

## Logging Setup

### Application Logging

We use Winston for application logging. Install the required packages:

```bash
npm install winston winston-daily-rotate-file
```

Create a logger configuration file at `config/logger.js`:

```javascript
const winston = require('winston');
const { createLogger, format, transports } = winston;
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');

// Define log directory and file paths
const logDir = 'logs';
const errorLog = path.join(logDir, 'error.log');
const combinedLog = path.join(logDir, 'combined.log');
const exceptionLog = path.join(logDir, 'exceptions.log');

// Create the logger
const logger = createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: format.combine(
    format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    format.errors({ stack: true }),
    format.splat(),
    format.json()
  ),
  defaultMeta: { service: 'csfportal' },
  transports: [
    // Write all logs error (and below) to error.log
    new DailyRotateFile({
      filename: path.join(logDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d',
      level: 'error',
    }),
    // Write all logs to combined.log
    new DailyRotateFile({
      filename: path.join(logDir, 'combined-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d',
    })
  ],
  exceptionHandlers: [
    new DailyRotateFile({
      filename: path.join(logDir, 'exceptions-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d',
    })
  ]
});

// If we're not in production, log to the console as well
if (process.env.NODE_ENV !== 'production') {
  logger.add(new transports.Console({
    format: format.combine(
      format.colorize(),
      format.simple()
    )
  }));
}

module.exports = logger;
```

Update `server.js` to use the logger:

```javascript
const logger = require('./config/logger');

// Replace console.log/error with logger
logger.info('MongoDB Connected');
logger.error(err.message);
```

### Nginx Logging

Configure Nginx to use JSON format for logs:

```bash
sudo nano /etc/nginx/nginx.conf
```

Add the following to the http section:

```
log_format json_combined escape=json
  '{'
    '"time_local":"$time_local",'
    '"remote_addr":"$remote_addr",'
    '"remote_user":"$remote_user",'
    '"request":"$request",'
    '"status": "$status",'
    '"body_bytes_sent":"$body_bytes_sent",'
    '"request_time":"$request_time",'
    '"http_referrer":"$http_referer",'
    '"http_user_agent":"$http_user_agent"'
  '}';

access_log /var/log/nginx/access.log json_combined;
```

## Application Monitoring

### PM2 for Process Management and Monitoring

Install PM2:

```bash
npm install -g pm2
```

Create a PM2 ecosystem file at `ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'csfportal',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    },
    env_production: {
      NODE_ENV: 'production'
    }
  }]
};
```

Start the application with PM2:

```bash
pm2 start ecosystem.config.js --env production
```

Set up PM2 to start on system boot:

```bash
pm2 startup
pm2 save
```

### Express Status Monitor

Install express-status-monitor:

```bash
npm install express-status-monitor
```

Add it to `server.js`:

```javascript
const statusMonitor = require('express-status-monitor');

// Add status monitor middleware (before other middleware)
app.use(statusMonitor({
  title: 'CSF Portal Status',
  path: '/status',
  spans: [{
    interval: 1,
    retention: 60
  }, {
    interval: 5,
    retention: 60
  }, {
    interval: 15,
    retention: 60
  }]
}));

// Secure the status page
app.use('/status', (req, res, next) => {
  if (req.isAuthenticated() && req.user.roles.includes('admin')) {
    return next();
  }
  res.status(401).send('Unauthorized');
});
```

## Server Monitoring

### Prometheus and Node Exporter

Install Node Exporter:

```bash
wget https://github.com/prometheus/node_exporter/releases/download/v1.3.1/node_exporter-1.3.1.linux-amd64.tar.gz
tar xvfz node_exporter-1.3.1.linux-amd64.tar.gz
sudo cp node_exporter-1.3.1.linux-amd64/node_exporter /usr/local/bin/
sudo useradd -rs /bin/false node_exporter
```

Create a systemd service for Node Exporter:

```bash
sudo nano /etc/systemd/system/node_exporter.service
```

Add the following content:

```
[Unit]
Description=Node Exporter
After=network.target

[Service]
User=node_exporter
Group=node_exporter
Type=simple
ExecStart=/usr/local/bin/node_exporter

[Install]
WantedBy=multi-user.target
```

Start and enable the service:

```bash
sudo systemctl daemon-reload
sudo systemctl start node_exporter
sudo systemctl enable node_exporter
```

### Prometheus Setup

Install Prometheus:

```bash
wget https://github.com/prometheus/prometheus/releases/download/v2.36.0/prometheus-2.36.0.linux-amd64.tar.gz
tar xvfz prometheus-2.36.0.linux-amd64.tar.gz
sudo cp prometheus-2.36.0.linux-amd64/prometheus /usr/local/bin/
sudo cp prometheus-2.36.0.linux-amd64/promtool /usr/local/bin/
sudo mkdir /etc/prometheus
sudo cp -r prometheus-2.36.0.linux-amd64/consoles /etc/prometheus
sudo cp -r prometheus-2.36.0.linux-amd64/console_libraries /etc/prometheus
sudo useradd -rs /bin/false prometheus
sudo mkdir /var/lib/prometheus
```

Create a Prometheus configuration file:

```bash
sudo nano /etc/prometheus/prometheus.yml
```

Add the following content:

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
  
  - job_name: 'csfportal'
    static_configs:
      - targets: ['localhost:5000']
```

Create a systemd service for Prometheus:

```bash
sudo nano /etc/systemd/system/prometheus.service
```

Add the following content:

```
[Unit]
Description=Prometheus
After=network.target

[Service]
User=prometheus
Group=prometheus
Type=simple
ExecStart=/usr/local/bin/prometheus \
    --config.file /etc/prometheus/prometheus.yml \
    --storage.tsdb.path /var/lib/prometheus/ \
    --web.console.templates=/etc/prometheus/consoles \
    --web.console.libraries=/etc/prometheus/console_libraries

[Install]
WantedBy=multi-user.target
```

Start and enable the service:

```bash
sudo systemctl daemon-reload
sudo systemctl start prometheus
sudo systemctl enable prometheus
```

### Grafana Setup

Install Grafana:

```bash
sudo apt-get install -y apt-transport-https software-properties-common
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -
echo "deb https://packages.grafana.com/oss/deb stable main" | sudo tee -a /etc/apt/sources.list.d/grafana.list
sudo apt-get update
sudo apt-get install -y grafana
```

Start and enable the Grafana service:

```bash
sudo systemctl start grafana-server
sudo systemctl enable grafana-server
```

Configure Nginx as a reverse proxy for Grafana:

```bash
sudo nano /etc/nginx/sites-available/grafana
```

Add the following content:

```
server {
    listen 80;
    server_name monitoring.ukcsf.org;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/grafana /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

Secure Grafana with SSL:

```bash
sudo certbot --nginx -d monitoring.ukcsf.org
```

## Database Monitoring

### MongoDB Exporter

Install MongoDB Exporter:

```bash
wget https://github.com/percona/mongodb_exporter/releases/download/v0.20.7/mongodb_exporter-0.20.7.linux-amd64.tar.gz
tar xvfz mongodb_exporter-0.20.7.linux-amd64.tar.gz
sudo cp mongodb_exporter /usr/local/bin/
sudo useradd -rs /bin/false mongodb_exporter
```

Create a MongoDB monitoring user:

```bash
mongosh -u admin -p secure_password --authenticationDatabase admin
```

```javascript
use admin
db.createUser({
  user: "mongodb_exporter",
  pwd: "monitoring_password",
  roles: [
    { role: "clusterMonitor", db: "admin" },
    { role: "read", db: "local" }
  ]
})
exit
```

Create a systemd service for MongoDB Exporter:

```bash
sudo nano /etc/systemd/system/mongodb_exporter.service
```

Add the following content:

```
[Unit]
Description=MongoDB Exporter
After=network.target

[Service]
User=mongodb_exporter
Group=mongodb_exporter
Type=simple
Environment="MONGODB_URI=********************************************************************"
ExecStart=/usr/local/bin/mongodb_exporter

[Install]
WantedBy=multi-user.target
```

Start and enable the service:

```bash
sudo systemctl daemon-reload
sudo systemctl start mongodb_exporter
sudo systemctl enable mongodb_exporter
```

Update Prometheus configuration to scrape MongoDB metrics:

```bash
sudo nano /etc/prometheus/prometheus.yml
```

Add the following to the scrape_configs section:

```yaml
  - job_name: 'mongodb'
    static_configs:
      - targets: ['localhost:9216']
```

Restart Prometheus:

```bash
sudo systemctl restart prometheus
```

## Alerting

### Prometheus Alertmanager

Install Alertmanager:

```bash
wget https://github.com/prometheus/alertmanager/releases/download/v0.24.0/alertmanager-0.24.0.linux-amd64.tar.gz
tar xvfz alertmanager-0.24.0.linux-amd64.tar.gz
sudo cp alertmanager-0.24.0.linux-amd64/alertmanager /usr/local/bin/
sudo mkdir /etc/alertmanager
sudo useradd -rs /bin/false alertmanager
```

Create an Alertmanager configuration file:

```bash
sudo nano /etc/alertmanager/alertmanager.yml
```

Add the following content (customize with your email details):

```yaml
global:
  smtp_smarthost: 'smtp.example.org:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname', 'job']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 3h
  receiver: 'email-notifications'

receivers:
- name: 'email-notifications'
  email_configs:
  - to: '<EMAIL>'
    send_resolved: true
```

Create a systemd service for Alertmanager:

```bash
sudo nano /etc/systemd/system/alertmanager.service
```

Add the following content:

```
[Unit]
Description=Alertmanager
After=network.target

[Service]
User=alertmanager
Group=alertmanager
Type=simple
ExecStart=/usr/local/bin/alertmanager \
    --config.file=/etc/alertmanager/alertmanager.yml \
    --storage.path=/var/lib/alertmanager

[Install]
WantedBy=multi-user.target
```

Start and enable the service:

```bash
sudo systemctl daemon-reload
sudo systemctl start alertmanager
sudo systemctl enable alertmanager
```

Update Prometheus configuration to use Alertmanager:

```bash
sudo nano /etc/prometheus/prometheus.yml
```

Add the following to the global section:

```yaml
alerting:
  alertmanagers:
  - static_configs:
    - targets:
      - localhost:9093
```

Create alert rules:

```bash
sudo nano /etc/prometheus/alert.rules.yml
```

Add the following content:

```yaml
groups:
- name: node
  rules:
  - alert: HighCPULoad
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU load (instance {{ $labels.instance }})"
      description: "CPU load is > 80%\n  VALUE = {{ $value }}\n  LABELS: {{ $labels }}"

  - alert: HighMemoryLoad
    expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory load (instance {{ $labels.instance }})"
      description: "Memory load is > 80%\n  VALUE = {{ $value }}\n  LABELS: {{ $labels }}"

  - alert: HighDiskUsage
    expr: (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) / node_filesystem_size_bytes{fstype!="tmpfs"} * 100 > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High disk usage (instance {{ $labels.instance }})"
      description: "Disk usage is > 80%\n  VALUE = {{ $value }}\n  LABELS: {{ $labels }}"
```

Update Prometheus configuration to include alert rules:

```bash
sudo nano /etc/prometheus/prometheus.yml
```

Add the following to the global section:

```yaml
rule_files:
  - "alert.rules.yml"
```

Restart Prometheus:

```bash
sudo systemctl restart prometheus
```

## Log Retention and Analysis

### Logrotate Configuration

Configure logrotate for application logs:

```bash
sudo nano /etc/logrotate.d/csfportal
```

Add the following content:

```
/var/www/csfportal/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data www-data
    sharedscripts
    postrotate
        systemctl reload csfportal
    endscript
}
```

### ELK Stack for Log Analysis (Optional)

For more advanced log analysis, consider setting up the ELK Stack (Elasticsearch, Logstash, Kibana):

1. Install Elasticsearch:
   ```bash
   wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo apt-key add -
   echo "deb https://artifacts.elastic.co/packages/7.x/apt stable main" | sudo tee -a /etc/apt/sources.list.d/elastic-7.x.list
   sudo apt update
   sudo apt install elasticsearch
   ```

2. Install Logstash:
   ```bash
   sudo apt install logstash
   ```

3. Install Kibana:
   ```bash
   sudo apt install kibana
   ```

4. Configure Filebeat to ship logs:
   ```bash
   sudo apt install filebeat
   ```

   Edit the Filebeat configuration:
   ```bash
   sudo nano /etc/filebeat/filebeat.yml
   ```

   Configure it to collect application and Nginx logs and send them to Elasticsearch.

5. Start and enable all services:
   ```bash
   sudo systemctl enable elasticsearch
   sudo systemctl start elasticsearch
   sudo systemctl enable logstash
   sudo systemctl start logstash
   sudo systemctl enable kibana
   sudo systemctl start kibana
   sudo systemctl enable filebeat
   sudo systemctl start filebeat
   ```

6. Configure Nginx as a reverse proxy for Kibana:
   ```bash
   sudo nano /etc/nginx/sites-available/kibana
   ```

   Add appropriate configuration and secure with SSL.