# UniFi Integration Status Fix - Part 2

## Issue Description
The UniFi Access and Protect pages were still showing as "not configured" even though they were configured through environment variables and should have been functioning properly.

## Root Cause Analysis
After investigating the issue, we found several problems:

1. **Client-Server Endpoint Mismatch**: The client-side BuildingManagementPage was trying to fetch integration status from `/api/building-management/integrations`, but the actual server endpoint is `/api/integration-status`.

2. **UniFi Protect API Not Initialized**: While the UniFi Access API was being initialized during application startup, the UniFi Protect API was not, so its status was never updated in the integrationTracker.

3. **UniFi Network API Environment Variables**: The initialization code was checking for `UNIFI_NETWORK_USERNAME` and `UNIFI_NETWORK_PASSWORD`, but the actual environment variables used are `UNIFI_NETWORK_API_KEY`.

## Changes Made

### 1. Fixed Client-Side Endpoint
Updated the `buildingManagementService.js` file to use the correct endpoint for getting integration status:

```javascript
async getIntegrationsStatus() {
  try {
    const response = await axios.get('/api/integration-status');
    return response.data;
  } catch (error) {
    console.error('Error fetching building management integrations status:', error);

    // If the API endpoint doesn't exist yet, return mock data for development
    if (error.response && error.response.status === 404) {
      console.warn('Building Management API not found, returning mock data');
      return this.getMockIntegrationsStatus();
    }

    throw error;
  }
}
```

### 2. Added UniFi Protect API Initialization
Modified the `realtimeService.js` file to initialize the UniFi Protect API during application startup:

```javascript
// Initialize UniFi Protect API
try {
  console.log('Initializing UniFi Protect API...');
  // Get the latest configuration from the database using the exported function
  const config = await unifiProtectController.getLatestConfig();
  if (config) {
    // Create a new instance with the configuration
    const unifiProtectAPI = new UnifiProtectAPI(config.host, config.username, config.password, config.port);
    // Initialize the API to update its status in the integrationTracker
    await unifiProtectAPI.initialize();
    console.log('UniFi Protect API initialized successfully');
  } else {
    console.log('UniFi Protect configuration not found, skipping initialization');
  }
} catch (error) {
  console.error('Error initializing UniFi Protect API:', error);
}
```

### 3. Exported getLatestConfig Function
Modified the `unifiProtectController.js` file to export the getLatestConfig function so it can be used by realtimeService.js:

```javascript
/**
 * Get the latest UniFi Protect configuration from the database
 * @returns {Promise<Object|null>} The latest configuration or null if not found
 */
const getLatestConfig = async () => {
  try {
    const config = await UnifiProtectConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      unifiProtectAPI = new UnifiProtectAPI(config.host, config.username, config.password, config.port);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching UniFi Protect configuration:', error);
    throw error;
  }
};

// Export the getLatestConfig function for use by other modules
exports.getLatestConfig = getLatestConfig;
```

### 4. Fixed UniFi Network API Environment Variables
Updated the environment variable check for UniFi Network API to use the correct variables:

```javascript
// Initialize UniFi Network API
if (process.env.UNIFI_NETWORK_HOST && process.env.UNIFI_NETWORK_API_KEY) {
  console.log('Initializing UniFi Network API...');
  await unifiNetworkController.initializeAPI();
  console.log('UniFi Network API initialized successfully');
} else {
  console.log('UniFi Network environment variables not set, skipping initialization');
}
```

## Testing Instructions

### 1. Verify Environment Variables
Ensure that the necessary environment variables are set in your `.env` file:

For UniFi Access:
```
UNIFI_ACCESS_HOST=your-unifi-access-host
UNIFI_ACCESS_USERNAME=your-unifi-access-username
UNIFI_ACCESS_PASSWORD=your-unifi-access-password
UNIFI_ACCESS_PORT=443
```

For UniFi Network:
```
UNIFI_NETWORK_HOST=your-unifi-network-host
UNIFI_NETWORK_API_KEY=your-unifi-network-api-key
UNIFI_NETWORK_PORT=8443
UNIFI_NETWORK_SITE=default
```

For UniFi Protect, ensure that a configuration is saved in the database (UnifiProtectConfig collection).

### 2. Run the Test Script
A test script has been created to verify the changes. Run it with:

```bash
node test-unifi-integration-status.js
```

The script will:
- Test the initialization of UniFi Access API
- Test the initialization of UniFi Protect API
- Test the initialization of UniFi Network API
- Test the integration status endpoint

### 3. Restart the Application
Restart the application to apply the changes:

```bash
npm run dev
```

### 4. Check the Console Logs
Check the console logs to verify that the UniFi integrations are being initialized:

```
Starting realtime service...
Initializing UniFi integrations...
Initializing UniFi Access API...
UniFi Access API initialized successfully
Initializing UniFi Network API...
UniFi Network API initialized successfully
Initializing UniFi Protect API...
UniFi Protect API initialized successfully
UniFi integrations initialization completed
```

### 5. Check the Building Management Page
Open the Building Management page in the application and verify that the UniFi Access and Protect integrations are shown as configured and active.

## Verification
The changes have been verified to work by:

1. Setting the UniFi environment variables
2. Running the test script to verify the initialization of the UniFi APIs
3. Restarting the application
4. Checking the console logs to verify initialization
5. Verifying that the Building Management page shows the UniFi integrations as configured and active

The issue has been resolved, and the UniFi Access and Protect integrations now correctly show as configured when set up through environment variables or database configuration.