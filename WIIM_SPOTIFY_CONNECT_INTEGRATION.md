# WiiM Spotify Connect Integration

## Overview

This document describes the updates made to the WiiM integration to allow playing Spotify content directly using the Spotify Connect API. This enhancement allows users to play Spotify content on any Spotify-connected device, not just the WiiM device.

## Features

1. **Spotify Connect Support**
   - Play Spotify content (tracks, albums, artists, playlists) directly on any Spotify-connected device
   - View and select from available Spotify devices
   - View current Spotify playback state
   - Toggle between WiiM playback and Spotify Connect playback

2. **Enhanced User Interface**
   - Toggle switch to enable/disable Spotify Connect
   - Device selection dropdown for Spotify-connected devices
   - Display of current playback information when using Spotify Connect
   - Refresh button to update the list of available devices

## Requirements

To use the Spotify Connect integration, you need:

1. A Spotify Premium account (Spotify Connect API requires a premium subscription)
2. Spotify API credentials configured in the environment variables:
   - `SPOTIFY_CLIENT_ID`
   - `SPOTIFY_CLIENT_SECRET`
   - `SPOTIFY_REFRESH_TOKEN`
3. The following Spotify API scopes must be included when generating the refresh token:
   - `user-read-playback-state`
   - `user-modify-playback-state`
   - `user-read-currently-playing`
   - `streaming`
   - `playlist-read-private`
   - `playlist-read-collaborative`
   - `user-library-read`

## Setup Instructions

### 1. Obtain Spotify API Credentials

Follow the instructions in the [WIIM_SPOTIFY_INTEGRATION_CHANGES.md](./WIIM_SPOTIFY_INTEGRATION_CHANGES.md) document to obtain your Spotify API credentials.

When generating the authorization URL, make sure to include the additional scopes required for Spotify Connect:

```
https://accounts.spotify.com/authorize?client_id=YOUR_CLIENT_ID&response_type=code&redirect_uri=http://localhost:6000/callback&scope=user-read-private%20user-read-email%20playlist-read-private%20playlist-read-collaborative%20user-library-read%20streaming%20user-read-playback-state%20user-modify-playback-state%20user-read-currently-playing
```

### 2. Update Environment Variables

Add or update the following variables in your `.env` file:

```
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
SPOTIFY_REFRESH_TOKEN=your_spotify_refresh_token
```

### 3. Restart the Application

Restart the application to apply the changes.

## Usage

### Enabling Spotify Connect

1. Navigate to the WiiM page in the portal
2. Select the Spotify tab
3. Toggle the "Use Spotify Connect" switch to enable Spotify Connect

### Selecting a Spotify Device

1. With Spotify Connect enabled, a device selection dropdown will appear
2. Select the device you want to play music on from the dropdown
3. If no devices are available, make sure you have at least one Spotify-connected device active (e.g., Spotify app open on your phone or computer)
4. Click the refresh button next to the dropdown to update the list of available devices

### Playing Spotify Content

1. Browse your Spotify playlists or search for content
2. Click on an item to play it
3. The content will play on the selected Spotify device
4. The current playback information will be displayed below the device selection

### Switching Back to WiiM Playback

1. Toggle the "Use Spotify Connect" switch to disable Spotify Connect
2. Spotify content will now play through the WiiM device

## Technical Details

### Authentication Flow

The Spotify Connect integration uses the same authentication flow as the existing Spotify integration:

1. When a Spotify-related action is requested, the system checks if it has a valid access token
2. If no token exists or the token has expired, it uses the refresh token from the environment variables to obtain a new access token
3. The access token is used to authenticate requests to the Spotify API

### New API Endpoints

The following new API endpoints have been added:

- `GET /api/wiim/spotify/devices` - Gets available Spotify devices for playback
- `GET /api/wiim/spotify/playback` - Gets current Spotify playback state
- `POST /api/wiim/spotify/tracks/:trackId/play-connect` - Plays a track using Spotify Connect API
- `POST /api/wiim/spotify/albums/:albumId/play-connect` - Plays an album using Spotify Connect API
- `POST /api/wiim/spotify/artists/:artistId/play-connect` - Plays an artist using Spotify Connect API
- `POST /api/wiim/spotify/playlists/:playlistId/play-connect` - Plays a playlist using Spotify Connect API

### UI Enhancements

The Spotify tab in the WiiM page has been enhanced with:

- A toggle switch for enabling/disabling Spotify Connect
- A device selection dropdown for Spotify-connected devices
- A display of current playback information when using Spotify Connect
- A refresh button to update the list of available devices

## Troubleshooting

### No Devices Available

If no devices are available in the dropdown:

1. Make sure you have at least one Spotify-connected device active (e.g., Spotify app open on your phone or computer)
2. Click the refresh button to update the list of devices
3. Check that your Spotify account is a Premium account (Spotify Connect API requires a premium subscription)
4. Verify that your Spotify API credentials are correct and have the necessary scopes

### Playback Not Starting

If playback doesn't start when you click on an item:

1. Check that the selected device is active and connected to Spotify
2. Try selecting a different device
3. Check the browser console for any error messages
4. Verify that your Spotify API credentials are correct and have the necessary scopes

### Playback State Not Updating

If the playback state doesn't update:

1. Click the refresh button to manually update the device list and playback state
2. Check that the selected device is active and connected to Spotify
3. Check the browser console for any error messages