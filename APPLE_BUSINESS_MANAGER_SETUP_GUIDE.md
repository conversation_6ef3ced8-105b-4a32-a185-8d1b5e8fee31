# Apple Business Manager Setup Guide

This guide provides step-by-step instructions for setting up Apple Business Manager and integrating it with the CSF Portal application.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Setting Up Apple Business Manager](#setting-up-apple-business-manager)
3. [Creating an API Account](#creating-an-api-account)
4. [Generating API Credentials](#generating-api-credentials)
5. [Configuring Environment Variables](#configuring-environment-variables)
6. [Verifying the Integration](#verifying-the-integration)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

Before you begin, ensure you have:

- An Apple ID with administrative access to your organization
- A D-U-N-S number for your organization (can be obtained from [Dun & Bradstreet](https://www.dnb.com/))
- Authority to sign legal agreements on behalf of your organization
- Access to the CSF Portal server for configuring environment variables

## Setting Up Apple Business Manager

1. **Create an Apple Business Manager account**

   If your organization doesn't already have an Apple Business Manager account:

   a. Go to [business.apple.com](https://business.apple.com)
   
   b. Click "Sign up now"
   
   c. Follow the enrollment process, which includes:
      - Providing your organization's details
      - Verifying your organization with your D-U-N-S number
      - Accepting the Apple Business Manager terms and conditions
      - Verifying your email address
   
   d. Wait for Apple to verify your organization (this may take several days)

2. **Set up your organization structure**

   Once your account is approved:
   
   a. Sign in to [business.apple.com](https://business.apple.com)
   
   b. Set up locations for your organization
   
   c. Create administrator accounts for your team members
   
   d. Configure roles and permissions as needed

## Creating an API Account

To integrate Apple Business Manager with the CSF Portal, you need to create an API account:

1. Sign in to [business.apple.com](https://business.apple.com) with an administrator account

2. Go to Settings > Access and Roles

3. Select the "Apps and Books" tab

4. Click "API Access" in the sidebar

5. Click the "+" button to create a new API account

6. Enter a name for the API account (e.g., "CSF Portal Integration")

7. Select the appropriate role for the API account (typically "Administrator" for full access)

8. Click "Save"

## Generating API Credentials

After creating the API account, you need to generate the necessary credentials:

1. **Create a private key**

   a. In the API Access section, select your newly created API account
   
   b. Click "Create Key"
   
   c. Enter a name for the key (e.g., "CSF Portal Key")
   
   d. Click "Create"
   
   e. Download the private key file (.p8 format) - **IMPORTANT: This file can only be downloaded once**
   
   f. Note the Key ID displayed on the screen

2. **Get your Organization ID**

   a. Go to Settings > Organization Info
   
   b. Your Organization ID is displayed at the top of the page

3. **Get your Issuer ID**

   a. Go back to Settings > Access and Roles > Apps and Books > API Access
   
   b. Your Issuer ID is displayed in the API Access section

4. **Get your Client ID and Client Secret**

   a. In the API Access section, select your API account
   
   b. Click "View Details"
   
   c. Your Client ID and Client Secret are displayed on this page

## Securely Storing the Private Key

The private key file (.p8 format) downloaded from Apple Business Manager is a sensitive credential that needs to be stored securely. Follow these steps to properly store and configure the private key:

1. **Create a secure storage location**

   The CSF Portal project includes a dedicated directory for storing sensitive keys:
   
   ```bash
   # Ensure the keys directory exists
   mkdir -p ./keys
   ```

2. **Store the private key file**

   a. Place the downloaded .p8 file in the keys directory
   
   b. Rename it to `apple_business_manager_private_key.p8`
   
   c. Set proper file permissions (Unix/Linux systems):
   
   ```bash
   # Set owner-only read/write permissions (0600)
   chmod 600 ./keys/apple_business_manager_private_key.p8
   
   # Verify permissions
   ls -la ./keys/
   ```

3. **For production environments**

   In production, consider storing the key in a more secure location:
   
   ```bash
   # Create a secure directory outside the web root
   sudo mkdir -p /etc/csfportal/keys
   
   # Set proper ownership (replace 'appuser' with the user running your application)
   sudo chown appuser:appuser /etc/csfportal/keys
   
   # Set proper directory permissions
   sudo chmod 700 /etc/csfportal/keys
   
   # Move the key file
   sudo mv ./keys/apple_business_manager_private_key.p8 /etc/csfportal/keys/
   
   # Set proper file permissions
   sudo chmod 600 /etc/csfportal/keys/apple_business_manager_private_key.p8
   ```

## Configuring Environment Variables

To integrate Apple Business Manager with the CSF Portal, you need to configure the following environment variables:

1. Access the server where the CSF Portal is deployed

2. Edit the `.env` file in the root directory of the CSF Portal application

3. Add or update the following environment variables:

   ```
   # Apple Business Manager configuration
   APPLE_BUSINESS_MANAGER_CLIENT_ID=your_client_id
   APPLE_BUSINESS_MANAGER_CLIENT_SECRET=your_client_secret
   APPLE_BUSINESS_MANAGER_ORGANIZATION_ID=your_organization_id
   APPLE_BUSINESS_MANAGER_KEY_ID=your_key_id
   APPLE_BUSINESS_MANAGER_ISSUER_ID=your_issuer_id
   APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY=1200
   
   # Choose ONE of the following methods to provide the private key:
   
   # Method 1: Specify the path to the private key file
   APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH=./keys/apple_business_manager_private_key.p8
   
   # Method 2: Provide the private key content directly
   # APPLE_BUSINESS_MANAGER_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----\nMIGHAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBG0wawIBAQQg...\n-----END PRIVATE KEY-----
   ```

4. Replace the placeholder values with your actual credentials:
   - `your_client_id`: The Client ID from your API account
   - `your_client_secret`: The Client Secret from your API account
   - `your_organization_id`: Your Apple Business Manager Organization ID
   - `your_key_id`: The Key ID associated with your private key
   - `your_issuer_id`: Your Apple Business Manager Issuer ID
   - `1200`: The token expiry time in seconds (default is 1200, or 20 minutes)

5. **Choose a method for providing the private key**:

   ### Method 1: Using a file path (traditional approach)
   
   If you prefer to store the private key in a file:
   
   ```
   APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH=./keys/apple_business_manager_private_key.p8
   ```
   
   For production environments, consider using a more secure location:
   
   ```
   APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH=/etc/csfportal/keys/apple_business_manager_private_key.p8
   ```
   
   ### Method 2: Providing the key content directly (recommended for containerized environments)
   
   If you prefer to store the private key content directly in the environment variable:
   
   ```
   APPLE_BUSINESS_MANAGER_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----\nMIGHAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBG0wawIBAQQg...\n-----END PRIVATE KEY-----
   ```
   
   **Important notes for Method 2**:
   - Replace line breaks in the key with `\n`
   - Ensure the entire key is on a single line
   - This method is particularly useful for containerized environments where mounting files can be challenging
   - When using container orchestration platforms like Kubernetes, store this value in a Secret

   **Security considerations for Method 2**:
   - Ensure your `.env` file has restricted permissions (e.g., `chmod 600 .env`)
   - Never commit the `.env` file to version control
   - Consider using a secrets management solution for production environments

6. Save the `.env` file

7. Restart the CSF Portal application to apply the changes

## Testing the Configuration

To verify that the Apple Business Manager integration is properly configured with the secure key storage:

1. Run the test script:

   ```bash
   node test-apple-business-manager.js
   ```

2. If the configuration is correct, you should see successful authentication with the Apple Business Manager API.

3. If you encounter errors related to the private key, check:
   - That the private key file exists at the specified path
   - That the file permissions allow the application to read the file
   - That the key ID matches the private key file

## Verifying the Integration

To verify that the Apple Business Manager integration is working correctly:

1. Sign in to the CSF Portal with an administrator account

2. Navigate to the Apple Business Manager section

3. Check if you can view devices and users from your Apple Business Manager account

4. If you encounter any errors, check the troubleshooting section below

## Troubleshooting

### Common Issues

1. **"Apple Business Manager is not properly configured" error**
   - Ensure all environment variables are correctly set in the `.env` file
   - If using Method 1 (file path), check that the private key file exists at the specified path and is readable by the application
   - If using Method 2 (key content), verify that the private key content is properly formatted with `\n` for line breaks

2. **"Private key path or content is undefined, empty, or not a string" error**
   - Ensure you've set either `APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH` or `APPLE_BUSINESS_MANAGER_PRIVATE_KEY` in your `.env` file
   - Check that the value is not empty and is properly formatted

3. **"Private key file not found" error**
   - This occurs when using Method 1 (file path)
   - Verify that the private key file exists at the path specified in `APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH`
   - Ensure the file permissions allow the application to read the file
   - Consider switching to Method 2 (key content) if file access is problematic

4. **Authentication errors**
   - Verify that all credentials (Client ID, Client Secret, Key ID, Issuer ID) are correct
   - Check that your API account has the necessary permissions in Apple Business Manager
   - Ensure your private key is valid and has not expired
   - If using Method 2 (key content), ensure the key is properly formatted with `\n` for line breaks
   - Check that the key begins with `-----BEGIN PRIVATE KEY-----` and ends with `-----END PRIVATE KEY-----`

5. **No devices or users displayed**
   - Verify that your Apple Business Manager account has devices and users enrolled
   - Check that your API account has permission to access devices and users

### Diagnosing Private Key Issues

If you're experiencing issues with the private key:

1. **For Method 1 (file path):**
   - Check file existence: `ls -la /path/to/your/private_key.p8`
   - Verify file permissions: `chmod 600 /path/to/your/private_key.p8`
   - Test file readability: `cat /path/to/your/private_key.p8`

2. **For Method 2 (key content):**
   - Ensure the key content starts with `-----BEGIN PRIVATE KEY-----` and ends with `-----END PRIVATE KEY-----`
   - Verify all line breaks in the original key are replaced with `\n`
   - Check that the entire key is on a single line in the `.env` file
   - Avoid special characters or quotes that might interfere with the key parsing

### Checking Integration Status

You can check the status of the Apple Business Manager integration by:

1. Accessing the CSF Portal API endpoint: `/api/apple-business-manager/config`
2. Checking the server logs for any error messages related to Apple Business Manager
3. Running the test script: `node test-apple-business-manager.js` from the project root directory

### Getting Help

If you continue to experience issues with the Apple Business Manager integration:

1. Consult the [Apple Business Manager User Guide](https://support.apple.com/guide/apple-business-manager/)
2. Contact Apple Support for issues related to your Apple Business Manager account
3. Contact your system administrator for issues related to the CSF Portal integration

## Additional Resources

- [Apple Business Manager User Guide](https://support.apple.com/guide/apple-business-manager/)
- [Apple Business Manager API Documentation](https://developer.apple.com/documentation/applebusinessmanagerapi/)
- [Creating an API Account in Apple Business Manager](https://support.apple.com/guide/apple-business-manager/create-an-api-account-axm33189f66a/web)