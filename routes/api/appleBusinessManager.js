const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const appleBusinessManagerController = require('../../server/controllers/appleBusinessManagerController');

// @route GET api/apple-business-manager/devices
// @desc Get all Apple Business Manager devices
// @access Private
router.get('/devices', isAuthenticated, appleBusinessManagerController.getDevices);

// @route   GET api/apple-business-manager/devices/:id
// @desc    Get Apple Business Manager device details
// @access  Private
router.get('/devices/:id', isAuthenticated, appleBusinessManagerController.getDeviceDetails);

// @route   GET api/apple-business-manager/config
// @desc    Get Apple Business Manager configuration status
// @access  Private
router.get('/config', isAuthenticated, appleBusinessManagerController.getConfig);

module.exports = router;