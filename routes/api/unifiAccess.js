const express = require('express');
const router = express.Router();
const { isAuthenticated, hasActivatedIntegration } = require('../../middleware/auth');
const unifiAccessController = require('../../server/controllers/unifiAccessController');

// @route   GET api/unifi-access/doors
// @desc    Get all UniFi Access doors
// @access  Private (requires unifi-access integration)
router.get('/doors', isAuthenticated, hasActivatedIntegration('unifi-access'), unifiAccessController.getDoors);

// @route   GET api/unifi-access/doors/:id
// @desc    Get UniFi Access door details
// @access  Private (requires unifi-access integration)
router.get('/doors/:id', isAuthenticated, hasActivatedIntegration('unifi-access'), unifiAccessController.getDoorDetails);

// @route   POST api/unifi-access/doors/:id/unlock
// @desc    Unlock a door
// @access  Private (requires unifi-access integration)
router.post('/doors/:id/unlock', isAuthenticated, hasActivatedIntegration('unifi-access'), unifiAccessController.unlockDoor);

// @route   POST api/unifi-access/doors/:id/lock
// @desc    Lock a door (Note: This functionality is not supported by the UniFi Access API and will return an error)
// @access  Private (requires unifi-access integration)
router.post('/doors/:id/lock', isAuthenticated, hasActivatedIntegration('unifi-access'), unifiAccessController.lockDoor);

// @route   GET api/unifi-access/access-points
// @desc    Get all UniFi Access access points
// @access  Private (requires unifi-access integration)
router.get('/access-points', isAuthenticated, hasActivatedIntegration('unifi-access'), unifiAccessController.getAccessPoints);

// @route   GET api/unifi-access/users
// @desc    Get all UniFi Access users
// @access  Private (requires unifi-access integration)
router.get('/users', isAuthenticated, hasActivatedIntegration('unifi-access'), unifiAccessController.getUsers);

// @route   GET api/unifi-access/events
// @desc    Get UniFi Access events
// @access  Private (requires unifi-access integration)
router.get('/events', isAuthenticated, hasActivatedIntegration('unifi-access'), unifiAccessController.getEvents);

// @route   GET api/unifi-access/system
// @desc    Get UniFi Access system status
// @access  Private (requires unifi-access integration)
router.get('/system', isAuthenticated, hasActivatedIntegration('unifi-access'), unifiAccessController.getSystemStatus);

// @route   GET api/unifi-access/config
// @desc    Get UniFi Access configuration status
// @access  Private
router.get('/config', isAuthenticated, unifiAccessController.getConfig);

// @route   GET api/unifi-access/configuration
// @desc    Alternative route for getting UniFi Access configuration status
// @access  Private
router.get('/configuration', isAuthenticated, unifiAccessController.getConfig);

module.exports = router;
