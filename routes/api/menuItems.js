const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const MenuItem = require('../../models/MenuItem');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/menu-items
 * @desc    Get all menu items
 * @access  public
 */
router.get('/', async (req, res) => {
  try {
    // Filter by category if provided
    const filter = {};
    if (req.query.category) {
      filter.categories = req.query.category;
    }

    // Filter by type if provided
    if (req.query.type) {
      filter.type = req.query.type;
    }

    // Filter by active status
    filter.isActive = true;

    // Get menu items
    const menuItems = await MenuItem.find(filter).sort({ order: 1, title: 1 });

    // Filter menu items based on user roles and permissions
    let filteredMenuItems = menuItems;

    // If user is authenticated, filter by required roles and permissions
    if (req.isAuthenticated()) {
      // Admin can see all menu items
      if (!req.user.roles.includes('admin')) {
        filteredMenuItems = menuItems.filter(item => 
          item.requiredRoles.some(role => req.user.roles.includes(role)) &&
          (!item.requiredPermission || req.user.permissions.includes(item.requiredPermission))
        );
      }
    } else {
      // If not authenticated, return empty array
      filteredMenuItems = [];
    }

    res.json(filteredMenuItems);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/menu-items/search
 * @desc    Search menu items
 * @access  public
 */
router.get('/search', async (req, res) => {
  try {
    const searchTerm = req.query.q;

    if (!searchTerm) {
      return res.status(400).json({ msg: 'Search term is required' });
    }

    // Search menu items using text index
    const menuItems = await MenuItem.find(
      { 
        $text: { $search: searchTerm },
        isActive: true
      },
      { 
        score: { $meta: 'textScore' } 
      }
    ).sort({ score: { $meta: 'textScore' } });

    // Filter menu items based on user roles and permissions (same as above)
    let filteredMenuItems = menuItems;

    if (req.isAuthenticated()) {
      if (!req.user.roles.includes('admin')) {
        filteredMenuItems = menuItems.filter(item => 
          item.requiredRoles.some(role => req.user.roles.includes(role)) &&
          (!item.requiredPermission || req.user.permissions.includes(item.requiredPermission))
        );
      }
    } else {
      filteredMenuItems = [];
    }

    res.json(filteredMenuItems);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/menu-items/categories
 * @desc    Get all unique categories used by menu items
 * @access  public
 */
router.get('/categories', async (req, res) => {
  try {
    const menuItems = await MenuItem.find({ isActive: true });

    // Extract all categories and remove duplicates
    const categories = [...new Set(menuItems.flatMap(item => item.categories))];

    res.json(categories.sort());
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/menu-items/:id
 * @desc    Get menu item by ID
 * @access  public
 */
router.get('/:id', async (req, res) => {
  try {
    const menuItem = await MenuItem.findById(req.params.id);

    if (!menuItem) {
      return res.status(404).json({ msg: 'Menu item not found' });
    }

    // Check if user has access to this menu item
    if (!req.isAuthenticated() || 
        (!req.user.roles.includes('admin') && 
         !menuItem.requiredRoles.some(role => req.user.roles.includes(role)) ||
         (menuItem.requiredPermission && !req.user.permissions.includes(menuItem.requiredPermission)))) {
      return res.status(403).json({ msg: 'Access denied' });
    }

    res.json(menuItem);
  } catch (err) {
    console.error(err.message);

    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Menu item not found' });
    }

    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/menu-items
 * @desc    Create a menu item
 * @access  private
 */
router.post(
  '/',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('title', 'Title is required').not().isEmpty(),
    check('path', 'Path is required').not().isEmpty(),
    check('categories', 'Categories must be an array').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { 
      title, 
      originalTitle,
      friendlyName,
      description, 
      path, 
      icon,
      customIcon,
      categories, 
      requiredRoles,
      requiredPermission,
      isActive,
      order,
      type
    } = req.body;

    try {
      // Create new menu item
      const menuItem = new MenuItem({
        title,
        originalTitle: originalTitle || title,
        friendlyName,
        description,
        path,
        icon: icon || 'link',
        customIcon,
        categories: categories.length > 0 ? categories : ['Core Features'],
        requiredRoles: requiredRoles || ['user'],
        requiredPermission,
        isActive: isActive !== undefined ? isActive : true,
        order: order || 0,
        type: type || 'regular',
        createdBy: req.user.id
      });

      await menuItem.save();

      res.json(menuItem);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/menu-items/:id
 * @desc    Update a menu item
 * @access  private
 */
router.put(
  '/:id',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('title', 'Title is required').not().isEmpty(),
    check('path', 'Path is required').not().isEmpty(),
    check('categories', 'Categories must be an array').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const menuItem = await MenuItem.findById(req.params.id);

      if (!menuItem) {
        return res.status(404).json({ msg: 'Menu item not found' });
      }

      // Update menu item fields
      const { 
        title, 
        originalTitle,
        friendlyName,
        description, 
        path, 
        icon,
        customIcon,
        categories, 
        requiredRoles,
        requiredPermission,
        isActive,
        order,
        type
      } = req.body;

      menuItem.title = title;
      // Only update originalTitle if explicitly provided
      if (originalTitle) {
        menuItem.originalTitle = originalTitle;
      }
      menuItem.friendlyName = friendlyName;
      menuItem.description = description;
      menuItem.path = path;
      menuItem.icon = icon || 'link';
      menuItem.customIcon = customIcon;
      menuItem.categories = categories.length > 0 ? categories : ['Core Features'];
      menuItem.requiredRoles = requiredRoles || ['user'];
      menuItem.requiredPermission = requiredPermission;
      
      if (isActive !== undefined) {
        menuItem.isActive = isActive;
      }
      
      if (order !== undefined) {
        menuItem.order = order;
      }
      
      if (type) {
        menuItem.type = type;
      }

      await menuItem.save();

      res.json(menuItem);
    } catch (err) {
      console.error(err.message);

      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Menu item not found' });
      }

      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   DELETE /api/menu-items/:id
 * @desc    Delete a menu item
 * @access  private
 */
router.delete('/:id', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const menuItem = await MenuItem.findById(req.params.id);

    if (!menuItem) {
      return res.status(404).json({ msg: 'Menu item not found' });
    }

    await menuItem.remove();

    res.json({ msg: 'Menu item removed' });
  } catch (err) {
    console.error(err.message);

    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Menu item not found' });
    }

    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/menu-items/init
 * @desc    Initialize default menu items
 * @access  private
 */
router.post('/init', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    await MenuItem.createDefaultMenuItems();
    const menuItems = await MenuItem.find();
    res.json(menuItems);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

module.exports = router;