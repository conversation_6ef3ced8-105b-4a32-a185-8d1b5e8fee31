/**
 * Access Control API Routes
 * 
 * This file defines the API routes for the unified access control management system
 * that combines Unifi Access and Lenel S2 NetBox.
 */

const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const accessControlController = require('../../server/controllers/accessControlController');

// @route   GET api/access-control/config
// @desc    Get configuration status for all access control systems
// @access  Private
router.get('/config', isAuthenticated, accessControlController.getConfigStatus);

// @route   GET api/access-control/doors
// @desc    Get all doors/portals from all access control systems
// @access  Private
router.get('/doors', isAuthenticated, accessControlController.getAllDoors);

// @route   POST api/access-control/doors/control
// @desc    Control a door/portal in the specified access control system
// @access  Private
router.post('/doors/control', isAuthenticated, accessControlController.controlDoor);

// @route   GET api/access-control/users
// @desc    Get all users from all access control systems
// @access  Private
router.get('/users', isAuthenticated, accessControlController.getAllUsers);

// @route   GET api/access-control/users/:id
// @desc    Get a user by ID from all access control systems
// @access  Private
router.get('/users/:id', isAuthenticated, accessControlController.getUserById);

// @route   POST api/access-control/users
// @desc    Create a new user in selected access control systems
// @access  Private
router.post('/users', isAuthenticated, accessControlController.createUser);

// @route   PUT api/access-control/users/:id
// @desc    Update a user in all provisioned systems
// @access  Private
router.put('/users/:id', isAuthenticated, accessControlController.updateUser);

// @route   DELETE api/access-control/users/:id
// @desc    Delete a user from all provisioned systems
// @access  Private
router.delete('/users/:id', isAuthenticated, accessControlController.deleteUser);

// @route   PUT api/access-control/users/:id/status
// @desc    Enable or disable a user across all provisioned systems
// @access  Private
router.put('/users/:id/status', isAuthenticated, accessControlController.updateUserStatus);

// @route   POST api/access-control/users/bulk-update-access-levels
// @desc    Update access levels for multiple users at once
// @access  Private
router.post('/users/bulk-update-access-levels', isAuthenticated, accessControlController.bulkUpdateAccessLevels);

// @route   GET api/access-control/access-levels
// @desc    Get all access levels from all access control systems
// @access  Private
router.get('/access-levels', isAuthenticated, accessControlController.getAllAccessLevels);

// @route   GET api/access-control/schedules
// @desc    Get all schedules from all access control systems
// @access  Private
router.get('/schedules', isAuthenticated, accessControlController.getAllSchedules);

// @route   POST api/access-control/schedules
// @desc    Create a new schedule in selected access control systems
// @access  Private
router.post('/schedules', isAuthenticated, accessControlController.createSchedule);

// @route   GET api/access-control/holidays
// @desc    Get all holidays from all access control systems
// @access  Private
router.get('/holidays', isAuthenticated, accessControlController.getAllHolidays);

// @route   POST api/access-control/holidays
// @desc    Create a new holiday in selected access control systems
// @access  Private
router.post('/holidays', isAuthenticated, accessControlController.createHoliday);

// @route   GET api/access-control/elevators
// @desc    Get all elevators from all access control systems
// @access  Private
router.get('/elevators', isAuthenticated, accessControlController.getAllElevators);

module.exports = router;