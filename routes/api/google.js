const express = require('express');
const router = express.Router();
const { google } = require('googleapis');
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const User = require('../../models/User');
const GoogleDriveFavorite = require('../../models/GoogleDriveFavorite');
const { getAuthenticatedClient } = require('../../server/utils/googleServiceAuth');

/**
 * Helper function to create Google API client with service account authentication
 * @param {string} userId - User ID to get email for impersonation
 * @param {string} serviceName - Name of the Google service (e.g., 'Drive', 'Admin')
 * @param {string[]} scopes - OAuth scopes required for the API
 * @returns {Promise<Object>} - Authenticated Google API client
 */
const createGoogleClient = async (userId, serviceName, scopes) => {
  try {
    // Get user email for impersonation
    const user = await User.findById(userId);

    if (!user || !user.email) {
      throw new Error('User not found or missing email');
    }

    // Use service account authentication
    const auth = await getAuthenticatedClient(serviceName, scopes, user.email);
    return auth;
  } catch (err) {
    console.error(`Error creating Google ${serviceName} client:`, err);
    throw err;
  }
};

/**
 * Execute a Google API request
 * @param {Function} apiCall - Function that makes the API call
 * @returns {Promise<any>} API response
 */
const executeGoogleApiCall = async (apiCall) => {
  try {
    // Execute the API call
    return await apiCall();
  } catch (error) {
    console.error('Error executing Google API call:', error);
    throw error;
  }
};

/**
 * @route   GET /api/google/drive/files
 * @desc    Get user's Google Drive files
 * @access  private
 */
router.get('/drive/files', isAuthenticated, async (req, res) => {
  try {
    const driveScopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.file',
      'https://www.googleapis.com/auth/drive.readonly'
    ];
    
    const auth = await createGoogleClient(req.user.id, 'Drive', driveScopes);
    const drive = google.drive({ version: 'v3', auth });

    // Get user email from the authenticated user
    const userEmail = req.user ? req.user.email : null;
    
    // If no user email is available, log a warning
    if (!userEmail) {
      console.warn('No user email available for filtering Google Drive files');
    }

    // Get files from Google Drive
    const files = await executeGoogleApiCall(async () => {
      // Build query to filter files by user's access if userEmail is available
      let query = null;
      if (userEmail) {
        query = `'${userEmail}' in owners or '${userEmail}' in writers or '${userEmail}' in readers`;
      }

      const response = await drive.files.list({
        pageSize: 30,
        fields: 'files(id, name, mimeType, webViewLink, iconLink, thumbnailLink, createdTime, modifiedTime)',
        orderBy: 'modifiedTime desc',
        q: query
      });
      return response.data.files;
    });

    res.json(files);
  } catch (err) {
    console.error('Error fetching Drive files:', err);

    if (err.message === 'User not found or missing email') {
      return res.status(401).json({ msg: 'Authentication required for Google Drive' });
    }

    res.status(500).json({ msg: 'Error fetching Google Drive files' });
  }
});

/**
 * @route   GET /api/google/drive/search
 * @desc    Search Google Drive files
 * @access  private
 */
router.get('/drive/search', isAuthenticated, async (req, res) => {
  try {
    const searchTerm = req.query.q;

    if (!searchTerm) {
      return res.status(400).json({ msg: 'Search term is required' });
    }

    const driveScopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.file',
      'https://www.googleapis.com/auth/drive.readonly'
    ];
    
    const auth = await createGoogleClient(req.user.id, 'Drive', driveScopes);
    const drive = google.drive({ version: 'v3', auth });

    // Get user email from the authenticated user
    const userEmail = req.user ? req.user.email : null;
    
    // If no user email is available, log a warning
    if (!userEmail) {
      console.warn('No user email available for filtering Google Drive search results');
    }

    // Search files in Google Drive
    const files = await executeGoogleApiCall(async () => {
      // Build base query for the search term
      let query = `name contains '${searchTerm}'`;
      
      // Add user access filter if userEmail is available
      if (userEmail) {
        query = `(${query}) and ('${userEmail}' in owners or '${userEmail}' in writers or '${userEmail}' in readers)`;
      }

      const response = await drive.files.list({
        q: query,
        pageSize: 30,
        fields: 'files(id, name, mimeType, webViewLink, iconLink, thumbnailLink, createdTime, modifiedTime)',
        orderBy: 'modifiedTime desc'
      });
      return response.data.files;
    });

    res.json(files);
  } catch (err) {
    console.error('Error searching Drive files:', err);

    if (err.message === 'User not found or missing email') {
      return res.status(401).json({ msg: 'Authentication required for Google Drive' });
    }

    res.status(500).json({ msg: 'Error searching Google Drive files' });
  }
});

/**
 * @route   GET /api/google/admin/users
 * @desc    Get users from Google Admin
 * @access  private
 */
router.get('/admin/users', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const adminScopes = [
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.user.readonly',
      'https://www.googleapis.com/auth/admin.directory.group',
      'https://www.googleapis.com/auth/admin.directory.group.readonly'
    ];
    
    const auth = await createGoogleClient(req.user.id, 'Admin', adminScopes);
    const admin = google.admin({ version: 'directory_v1', auth });

    // Get users from Google Admin
    const users = await executeGoogleApiCall(async () => {
      const response = await admin.users.list({
        domain: process.env.ALLOWED_DOMAINS.split(',')[0],
        maxResults: 100,
        orderBy: 'email'
      });
      return response.data.users;
    });

    res.json(users);
  } catch (err) {
    console.error('Error fetching Admin users:', err);

    if (err.message === 'User not found or missing email') {
      return res.status(401).json({ msg: 'Authentication required for Google Admin' });
    }

    res.status(500).json({ msg: 'Error fetching Google Admin users' });
  }
});

/**
 * @route   GET /api/google/admin/groups
 * @desc    Get groups from Google Admin
 * @access  private
 */
router.get('/admin/groups', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const adminScopes = [
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.user.readonly',
      'https://www.googleapis.com/auth/admin.directory.group',
      'https://www.googleapis.com/auth/admin.directory.group.readonly'
    ];
    
    const auth = await createGoogleClient(req.user.id, 'Admin', adminScopes);
    const admin = google.admin({ version: 'directory_v1', auth });

    // Get groups from Google Admin
    const groups = await executeGoogleApiCall(async () => {
      const response = await admin.groups.list({
        domain: process.env.ALLOWED_DOMAINS.split(',')[0],
        maxResults: 100
      });
      return response.data.groups;
    });

    res.json(groups);
  } catch (err) {
    console.error('Error fetching Admin groups:', err);

    if (err.message === 'User not found or missing email') {
      return res.status(401).json({ msg: 'Authentication required for Google Admin' });
    }

    res.status(500).json({ msg: 'Error fetching Google Admin groups' });
  }
});

/**
 * @route   GET /api/google/admin/groups/:groupKey/members
 * @desc    Get members of a group from Google Admin
 * @access  private
 */
router.get('/admin/groups/:groupKey/members', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const adminScopes = [
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.user.readonly',
      'https://www.googleapis.com/auth/admin.directory.group',
      'https://www.googleapis.com/auth/admin.directory.group.readonly'
    ];
    
    const auth = await createGoogleClient(req.user.id, 'Admin', adminScopes);
    const admin = google.admin({ version: 'directory_v1', auth });

    // Get group members from Google Admin
    const members = await executeGoogleApiCall(async () => {
      const response = await admin.members.list({
        groupKey: req.params.groupKey
      });
      return response.data.members;
    });

    res.json(members);
  } catch (err) {
    console.error('Error fetching group members:', err);

    if (err.message === 'User not found or missing email') {
      return res.status(401).json({ msg: 'Authentication required for Google Admin' });
    }

    res.status(500).json({ msg: 'Error fetching group members' });
  }
});

/**
 * @route   GET /api/google/drive/file/:fileId
 * @desc    Get a specific file from Google Drive
 * @access  private
 */
router.get('/drive/file/:fileId', isAuthenticated, async (req, res) => {
  try {
    const driveScopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.file',
      'https://www.googleapis.com/auth/drive.readonly'
    ];
    
    const auth = await createGoogleClient(req.user.id, 'Drive', driveScopes);
    const drive = google.drive({ version: 'v3', auth });

    // Get user email from the authenticated user
    const userEmail = req.user ? req.user.email : null;
    
    // If no user email is available, log a warning
    if (!userEmail) {
      console.warn('No user email available for verifying Google Drive file access');
    }

    // First verify the user has access to this file
    if (userEmail) {
      try {
        const fileList = await executeGoogleApiCall(async () => {
          const response = await drive.files.list({
            q: `id = '${req.params.fileId}' and ('${userEmail}' in owners or '${userEmail}' in writers or '${userEmail}' in readers)`,
            fields: 'files(id)'
          });
          return response.data.files;
        });

        // If no files found, user doesn't have access
        if (!fileList || fileList.length === 0) {
          return res.status(403).json({ msg: 'You do not have access to this file' });
        }
      } catch (verifyError) {
        console.error('Error verifying file access:', verifyError);
        // Continue with the request even if verification fails
      }
    }

    // Get file from Google Drive
    const file = await executeGoogleApiCall(async () => {
      const response = await drive.files.get({
        fileId: req.params.fileId,
        fields: 'id, name, mimeType, webViewLink, iconLink, thumbnailLink, size, createdTime, modifiedTime'
      });
      return response.data;
    });

    res.json(file);
  } catch (err) {
    console.error('Error fetching Drive file:', err);

    if (err.message === 'User not found or missing email') {
      return res.status(401).json({ msg: 'Authentication required for Google Drive' });
    }

    res.status(500).json({ msg: 'Error fetching Google Drive file' });
  }
});

/**
 * @route   GET /api/google/drive/viewer/:fileId
 * @desc    Get embedded viewer URL for a file
 * @access  private
 */
router.get('/drive/viewer/:fileId', isAuthenticated, async (req, res) => {
  try {
    const driveScopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.file',
      'https://www.googleapis.com/auth/drive.readonly'
    ];
    
    const auth = await createGoogleClient(req.user.id, 'Drive', driveScopes);
    const drive = google.drive({ version: 'v3', auth });

    // Get user email from the authenticated user
    const userEmail = req.user ? req.user.email : null;
    
    // If no user email is available, log a warning
    if (!userEmail) {
      console.warn('No user email available for verifying Google Drive file access');
    }

    // First verify the user has access to this file
    if (userEmail) {
      try {
        const fileList = await executeGoogleApiCall(async () => {
          const response = await drive.files.list({
            q: `id = '${req.params.fileId}' and ('${userEmail}' in owners or '${userEmail}' in writers or '${userEmail}' in readers)`,
            fields: 'files(id)'
          });
          return response.data.files;
        });

        // If no files found, user doesn't have access
        if (!fileList || fileList.length === 0) {
          return res.status(403).json({ msg: 'You do not have access to this file' });
        }
      } catch (verifyError) {
        console.error('Error verifying file access:', verifyError);
        // Continue with the request even if verification fails
      }
    }

    // Generate embedded viewer URL
    const viewerUrl = `https://drive.google.com/file/d/${req.params.fileId}/preview`;

    res.json({ viewerUrl });
  } catch (err) {
    console.error('Error generating viewer URL:', err);

    if (err.message === 'User not found or missing email') {
      return res.status(401).json({ msg: 'Authentication required for Google Drive' });
    }

    res.status(500).json({ msg: 'Error generating viewer URL' });
  }
});

/**
 * @route   GET /api/google/drive/editor/:fileId
 * @desc    Get embedded editor URL for a file
 * @access  private
 */
router.get('/drive/editor/:fileId', isAuthenticated, async (req, res) => {
  try {
    const driveScopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.file',
      'https://www.googleapis.com/auth/drive.readonly'
    ];
    
    const auth = await createGoogleClient(req.user.id, 'Drive', driveScopes);
    const drive = google.drive({ version: 'v3', auth });

    // Get user email from the authenticated user
    const userEmail = req.user ? req.user.email : null;
    
    // If no user email is available, log a warning
    if (!userEmail) {
      console.warn('No user email available for verifying Google Drive file access');
    }

    // First verify the user has access to this file
    if (userEmail) {
      try {
        const fileList = await executeGoogleApiCall(async () => {
          const response = await drive.files.list({
            q: `id = '${req.params.fileId}' and ('${userEmail}' in owners or '${userEmail}' in writers or '${userEmail}' in readers)`,
            fields: 'files(id)'
          });
          return response.data.files;
        });

        // If no files found, user doesn't have access
        if (!fileList || fileList.length === 0) {
          return res.status(403).json({ msg: 'You do not have access to this file' });
        }
      } catch (verifyError) {
        console.error('Error verifying file access:', verifyError);
        // Continue with the request even if verification fails
      }
    }

    // Get file from Google Drive to check its type
    const fileData = await executeGoogleApiCall(async () => {
      const file = await drive.files.get({
        fileId: req.params.fileId,
        fields: 'id, name, mimeType'
      });
      return file.data;
    });

    // Generate embedded editor URL based on file type
    let editorUrl;
    const mimeType = fileData.mimeType;

    if (mimeType === 'application/vnd.google-apps.document') {
      editorUrl = `https://docs.google.com/document/d/${req.params.fileId}/edit?usp=drivesdk`;
    } else if (mimeType === 'application/vnd.google-apps.spreadsheet') {
      editorUrl = `https://docs.google.com/spreadsheets/d/${req.params.fileId}/edit?usp=drivesdk`;
    } else if (mimeType === 'application/vnd.google-apps.presentation') {
      editorUrl = `https://docs.google.com/presentation/d/${req.params.fileId}/edit?usp=drivesdk`;
    } else if (mimeType === 'application/vnd.google-apps.drawing') {
      editorUrl = `https://docs.google.com/drawings/d/${req.params.fileId}/edit?usp=drivesdk`;
    } else if (mimeType === 'application/vnd.google-apps.form') {
      editorUrl = `https://docs.google.com/forms/d/${req.params.fileId}/edit?usp=drivesdk`;
    } else {
      return res.status(400).json({ msg: 'File type does not support embedded editing' });
    }

    res.json({ editorUrl, file: fileData });
  } catch (err) {
    console.error('Error generating editor URL:', err);

    if (err.message === 'User not found or missing email') {
      return res.status(401).json({ msg: 'Authentication required for Google Drive' });
    }

    res.status(500).json({ msg: 'Error generating editor URL' });
  }
});

/**
 * @route   GET /api/google/drive/favorites
 * @desc    Get all favorites for the current user
 * @access  private
 */
router.get('/drive/favorites', isAuthenticated, async (req, res) => {
  try {
    const favorites = await GoogleDriveFavorite.find({ user: req.user.id })
      .sort({ createdAt: -1 });

    res.json(favorites);
  } catch (err) {
    console.error('Error fetching favorites:', err);
    res.status(500).json({ msg: 'Error fetching favorites' });
  }
});

/**
 * @route   GET /api/google/drive/favorites/:fileId
 * @desc    Check if a file is favorited by the current user
 * @access  private
 */
router.get('/drive/favorites/:fileId', isAuthenticated, async (req, res) => {
  try {
    const favorite = await GoogleDriveFavorite.findOne({
      user: req.user.id,
      fileId: req.params.fileId
    });

    res.json({ isFavorite: !!favorite });
  } catch (err) {
    console.error('Error checking favorite status:', err);
    res.status(500).json({ msg: 'Error checking favorite status' });
  }
});

/**
 * @route   POST /api/google/drive/favorites
 * @desc    Add a file to favorites
 * @access  private
 */
router.post('/drive/favorites', isAuthenticated, async (req, res) => {
  try {
    const { fileId } = req.body;

    if (!fileId) {
      return res.status(400).json({ msg: 'File ID is required' });
    }

    // Check if the file is already favorited by this user
    const existingFavorite = await GoogleDriveFavorite.findOne({
      user: req.user.id,
      fileId
    });

    if (existingFavorite) {
      return res.status(400).json({ msg: 'File is already in favorites' });
    }

    // Get file details from Google Drive
    const driveScopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.file',
      'https://www.googleapis.com/auth/drive.readonly'
    ];
    
    const auth = await createGoogleClient(req.user.id, 'Drive', driveScopes);
    const drive = google.drive({ version: 'v3', auth });

    // Get user email from the authenticated user
    const userEmail = req.user ? req.user.email : null;
    
    // If no user email is available, log a warning
    if (!userEmail) {
      console.warn('No user email available for verifying Google Drive file access');
    }

    // First verify the user has access to this file
    if (userEmail) {
      try {
        const fileList = await executeGoogleApiCall(async () => {
          const response = await drive.files.list({
            q: `id = '${fileId}' and ('${userEmail}' in owners or '${userEmail}' in writers or '${userEmail}' in readers)`,
            fields: 'files(id)'
          });
          return response.data.files;
        });

        // If no files found, user doesn't have access
        if (!fileList || fileList.length === 0) {
          return res.status(403).json({ msg: 'You do not have access to this file' });
        }
      } catch (verifyError) {
        console.error('Error verifying file access:', verifyError);
        // Continue with the request even if verification fails
      }
    }

    // Get file details
    const fileDetails = await executeGoogleApiCall(async () => {
      const response = await drive.files.get({
        fileId,
        fields: 'id, name, mimeType, webViewLink, iconLink, size, modifiedTime'
      });
      return response.data;
    });

    // Create a new favorite
    const favorite = new GoogleDriveFavorite({
      user: req.user.id,
      fileId: fileDetails.id,
      name: fileDetails.name,
      mimeType: fileDetails.mimeType,
      iconLink: fileDetails.iconLink,
      webViewLink: fileDetails.webViewLink,
      size: fileDetails.size,
      modifiedTime: fileDetails.modifiedTime
    });

    await favorite.save();

    res.json({ msg: 'File added to favorites', favorite });
  } catch (err) {
    console.error('Error adding file to favorites:', err);

    if (err.message === 'User not found or missing email') {
      return res.status(401).json({ msg: 'Authentication required for Google Drive' });
    }

    res.status(500).json({ msg: 'Error adding file to favorites' });
  }
});

/**
 * @route   DELETE /api/google/drive/favorites/:fileId
 * @desc    Remove a file from favorites
 * @access  private
 */
router.delete('/drive/favorites/:fileId', isAuthenticated, async (req, res) => {
  try {
    // Find and remove the favorite
    const favorite = await GoogleDriveFavorite.findOneAndDelete({
      user: req.user.id,
      fileId: req.params.fileId
    });

    if (!favorite) {
      return res.status(404).json({ msg: 'Favorite not found' });
    }

    res.json({ msg: 'File removed from favorites' });
  } catch (err) {
    console.error('Error removing file from favorites:', err);
    res.status(500).json({ msg: 'Error removing file from favorites' });
  }
});

module.exports = router;
