#!/usr/bin/env node

/**
 * Advanced troubleshooting for domain-wide delegation issues
 */

require('dotenv').config();
const { google } = require('googleapis');

async function debugDomainWideDelegation() {
  console.log('🔍 Advanced Domain-Wide Delegation Troubleshooting\n');

  const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
  const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;
  const domain = process.env.ALLOWED_DOMAINS;

  console.log('📋 Configuration Check:');
  console.log(`   Service Account: ${serviceAccountEmail}`);
  console.log(`   Domain: ${domain}`);
  console.log();

  // Test different user email formats
  const testUsers = [
    `blakep@${domain}`,
    `admin@${domain}`,
    `google@${domain}`,
    // Add your actual admin email if different
  ];

  console.log('👥 Testing Different User Emails:');
  console.log('-'.repeat(40));

  for (const userEmail of testUsers) {
    console.log(`\nTesting: ${userEmail}`);
    
    try {
      const auth = new google.auth.JWT({
        email: serviceAccountEmail,
        key: serviceAccountPrivateKey,
        scopes: ['https://www.googleapis.com/auth/drive.readonly'],
        subject: userEmail
      });

      const token = await auth.getAccessToken();
      console.log(`   ✅ SUCCESS: ${userEmail} works!`);
      console.log(`   📝 Token: ${token.token.substring(0, 30)}...`);
      
      // If this user works, test a Drive API call
      try {
        const drive = google.drive({ version: 'v3', auth });
        const files = await drive.files.list({ pageSize: 1 });
        console.log(`   📂 Drive API call successful - found ${files.data.files.length} files`);
      } catch (driveError) {
        console.log(`   ⚠️  Drive API failed: ${driveError.message}`);
      }
      
      break; // Stop testing once we find a working user
      
    } catch (error) {
      console.log(`   ❌ FAILED: ${error.message}`);
      
      if (error.message.includes('invalid_grant')) {
        console.log(`   🔍 Possible causes:`);
        console.log(`      - User ${userEmail} doesn't exist in domain`);
        console.log(`      - Domain-wide delegation not configured for this user`);
        console.log(`      - Scopes don't match configuration`);
      }
    }
  }

  console.log('\n🔧 Troubleshooting Steps:');
  console.log('-'.repeat(40));
  
  console.log('\n1. VERIFY SERVICE ACCOUNT CLIENT ID');
  console.log('   • Go to https://console.cloud.google.com/');
  console.log('   • Navigate to IAM & Admin → Service Accounts');
  console.log(`   • Find: ${serviceAccountEmail}`);
  console.log('   • Copy the "Unique ID" (numeric, like 123456789012345678901)');
  console.log('   • NOT the email address, but the numeric ID');

  console.log('\n2. VERIFY DOMAIN-WIDE DELEGATION SETUP');
  console.log('   • Go to https://admin.google.com/');
  console.log('   • Security → Access and data control → API controls');
  console.log('   • Manage Domain Wide Delegation');
  console.log('   • Look for entry with your Client ID');
  console.log('   • Verify scopes match exactly (case-sensitive)');

  console.log('\n3. VERIFY USER EXISTS');
  console.log(`   • Go to https://admin.google.com/`);
  console.log(`   • Directory → Users`);
  console.log(`   • Search for: bpritchett@${domain}`);
  console.log(`   • Verify user exists and is active`);

  console.log('\n4. CHECK SCOPE CONFIGURATION');
  console.log('   Current scopes being tested:');
  console.log('   • https://www.googleapis.com/auth/drive.readonly');
  console.log('   \n   Full scope list for delegation:');
  
  const fullScopes = [
    'https://www.googleapis.com/auth/drive',
    'https://www.googleapis.com/auth/drive.file',
    'https://www.googleapis.com/auth/drive.metadata',
    'https://www.googleapis.com/auth/admin.directory.user',
    'https://www.googleapis.com/auth/admin.directory.group',
    'https://www.googleapis.com/auth/admin.directory.user.security',
    'https://www.googleapis.com/auth/calendar',
    'https://www.googleapis.com/auth/calendar.events',
    'https://www.googleapis.com/auth/calendar.settings.readonly',
    'https://www.googleapis.com/auth/forms.body',
    'https://www.googleapis.com/auth/forms.responses.readonly'
  ];

  fullScopes.forEach((scope, i) => {
    console.log(`   ${(i + 1).toString().padStart(2, ' ')}. ${scope}`);
  });

  console.log('\n5. WAIT FOR PROPAGATION');
  console.log('   • Domain-wide delegation changes can take up to 24 hours');
  console.log('   • Try again in 15-30 minutes');
  console.log('   • Clear any cached tokens');
  
  console.log('\n6. TEST WITH DIFFERENT SCOPES');
  console.log('   Let me test with minimal scopes...\n');

  // Test with just one scope
  try {
    console.log('Testing with minimal scope (https://www.googleapis.com/auth/userinfo.email)...');
    const minimalAuth = new google.auth.JWT({
      email: serviceAccountEmail,
      key: serviceAccountPrivateKey,
      scopes: ['https://www.googleapis.com/auth/userinfo.email'],
      subject: `blakep@${domain}`
    });

    const minimalToken = await minimalAuth.getAccessToken();
    console.log('✅ SUCCESS with minimal scope! Domain-wide delegation IS working.');
    console.log('🔍 Issue might be with specific API scopes or API enablement.');
    
  } catch (minimalError) {
    console.log(`❌ Failed even with minimal scope: ${minimalError.message}`);
    console.log('🔍 This confirms domain-wide delegation is not working.');
  }

  console.log('\n📞 NEXT STEPS:');
  console.log('1. Double-check the Client ID used in delegation setup');
  console.log('2. Verify the user email exists in Google Workspace');
  console.log('3. Wait 15-30 minutes for propagation');
  console.log('4. Check Google Cloud Console for any project restrictions');
}

debugDomainWideDelegation().catch(console.error);