/**
 * Test script for UniFi Protect integration with environment variables
 * 
 * This script tests the UniFi Protect integration by:
 * 1. Setting environment variables for multiple UniFi Protect instances
 * 2. Initializing the UniFi Protect API
 * 3. Verifying that the API is using the environment variables correctly
 * 
 * Usage:
 * node test-unifi-protect-env.js
 */

// Set environment variables for testing multiple instances
process.env.UNIFI_PROTECT_HOST_A = 'test-host-a';
process.env.UNIFI_PROTECT_API_KEY_A = 'test-api-key-a';
process.env.UNIFI_PROTECT_HOST_B = 'test-host-b';
process.env.UNIFI_PROTECT_API_KEY_B = 'test-api-key-b';
process.env.UNIFI_PROTECT_PORT = '443';

// For backward compatibility testing
process.env.UNIFI_PROTECT_HOST = 'test-host-legacy';
process.env.UNIFI_PROTECT_USERNAME = 'test-username-legacy';
process.env.UNIFI_PROTECT_PASSWORD = 'test-password-legacy';

// Import the UniFi Protect API
const UnifiProtectAPI = require('./server/integrations/unifiProtect/unifiProtectAPI');

// Create a test function
async function testUnifiProtectEnv() {
  console.log('Testing UniFi Protect integration with environment variables...');
  console.log('Environment variables:');
  console.log(`  UNIFI_PROTECT_HOST_A: ${process.env.UNIFI_PROTECT_HOST_A}`);
  console.log(`  UNIFI_PROTECT_API_KEY_A: [hidden]`);
  console.log(`  UNIFI_PROTECT_HOST_B: ${process.env.UNIFI_PROTECT_HOST_B}`);
  console.log(`  UNIFI_PROTECT_API_KEY_B: [hidden]`);
  console.log(`  UNIFI_PROTECT_PORT: ${process.env.UNIFI_PROTECT_PORT}`);
  console.log(`  UNIFI_PROTECT_HOST: ${process.env.UNIFI_PROTECT_HOST} (legacy)`);
  console.log(`  UNIFI_PROTECT_USERNAME: ${process.env.UNIFI_PROTECT_USERNAME} (legacy)`);
  console.log(`  UNIFI_PROTECT_PASSWORD: [hidden] (legacy)`);
  
  try {
    // Initialize the API
    const unifiProtectAPI = new UnifiProtectAPI();
    
    // Check if the API was initialized with the correct instances
    console.log('\nAPI initialized with instances:');
    
    // Check instance A
    if (unifiProtectAPI.instances.A) {
      console.log('Instance A:');
      console.log(`  Host: ${unifiProtectAPI.instances.A.host}`);
      console.log(`  API Key: [hidden]`);
      console.log(`  Port: ${unifiProtectAPI.instances.A.port}`);
      
      // Verify that instance A is using the correct environment variables
      if (
        unifiProtectAPI.instances.A.host === process.env.UNIFI_PROTECT_HOST_A &&
        unifiProtectAPI.instances.A.apiKey === process.env.UNIFI_PROTECT_API_KEY_A &&
        unifiProtectAPI.instances.A.port.toString() === process.env.UNIFI_PROTECT_PORT
      ) {
        console.log('  ✅ Instance A is correctly configured');
      } else {
        console.log('  ❌ Instance A is NOT correctly configured');
        console.log('  Expected:');
        console.log(`    Host: ${process.env.UNIFI_PROTECT_HOST_A}`);
        console.log(`    API Key: [hidden]`);
        console.log(`    Port: ${process.env.UNIFI_PROTECT_PORT}`);
        console.log('  Got:');
        console.log(`    Host: ${unifiProtectAPI.instances.A.host}`);
        console.log(`    API Key: [hidden]`);
        console.log(`    Port: ${unifiProtectAPI.instances.A.port}`);
      }
    } else {
      console.log('❌ Instance A not found');
    }
    
    // Check instance B
    if (unifiProtectAPI.instances.B) {
      console.log('\nInstance B:');
      console.log(`  Host: ${unifiProtectAPI.instances.B.host}`);
      console.log(`  API Key: [hidden]`);
      console.log(`  Port: ${unifiProtectAPI.instances.B.port}`);
      
      // Verify that instance B is using the correct environment variables
      if (
        unifiProtectAPI.instances.B.host === process.env.UNIFI_PROTECT_HOST_B &&
        unifiProtectAPI.instances.B.apiKey === process.env.UNIFI_PROTECT_API_KEY_B &&
        unifiProtectAPI.instances.B.port.toString() === process.env.UNIFI_PROTECT_PORT
      ) {
        console.log('  ✅ Instance B is correctly configured');
      } else {
        console.log('  ❌ Instance B is NOT correctly configured');
        console.log('  Expected:');
        console.log(`    Host: ${process.env.UNIFI_PROTECT_HOST_B}`);
        console.log(`    API Key: [hidden]`);
        console.log(`    Port: ${process.env.UNIFI_PROTECT_PORT}`);
        console.log('  Got:');
        console.log(`    Host: ${unifiProtectAPI.instances.B.host}`);
        console.log(`    API Key: [hidden]`);
        console.log(`    Port: ${unifiProtectAPI.instances.B.port}`);
      }
    } else {
      console.log('❌ Instance B not found');
    }
    
    // Check legacy configuration
    if (unifiProtectAPI.host) {
      console.log('\nLegacy configuration:');
      console.log(`  Host: ${unifiProtectAPI.host}`);
      console.log(`  Username: ${unifiProtectAPI.username}`);
      console.log(`  Port: ${unifiProtectAPI.port}`);
      console.log(`  Password: [hidden]`);
      
      // Verify that the legacy configuration is using the correct environment variables
      if (
        unifiProtectAPI.host === process.env.UNIFI_PROTECT_HOST &&
        unifiProtectAPI.username === process.env.UNIFI_PROTECT_USERNAME &&
        unifiProtectAPI.port.toString() === process.env.UNIFI_PROTECT_PORT
      ) {
        console.log('  ✅ Legacy configuration is correctly set up');
      } else {
        console.log('  ❌ Legacy configuration is NOT correctly set up');
        console.log('  Expected:');
        console.log(`    Host: ${process.env.UNIFI_PROTECT_HOST}`);
        console.log(`    Username: ${process.env.UNIFI_PROTECT_USERNAME}`);
        console.log(`    Port: ${process.env.UNIFI_PROTECT_PORT}`);
        console.log('  Got:');
        console.log(`    Host: ${unifiProtectAPI.host}`);
        console.log(`    Username: ${unifiProtectAPI.username}`);
        console.log(`    Port: ${unifiProtectAPI.port}`);
      }
    } else {
      console.log('\n⚠️ Legacy configuration not found (this is expected when using the new API key method)');
    }
    
    // Test the _getAxiosInstance method
    console.log('\nTesting _getAxiosInstance method:');
    
    try {
      const axiosInstanceA = unifiProtectAPI._getAxiosInstance('A');
      console.log('  ✅ Successfully got axios instance for Instance A');
    } catch (error) {
      console.log(`  ❌ Failed to get axios instance for Instance A: ${error.message}`);
    }
    
    try {
      const axiosInstanceB = unifiProtectAPI._getAxiosInstance('B');
      console.log('  ✅ Successfully got axios instance for Instance B');
    } catch (error) {
      console.log(`  ❌ Failed to get axios instance for Instance B: ${error.message}`);
    }
    
    try {
      const axiosInstanceDefault = unifiProtectAPI._getAxiosInstance(null);
      console.log('  ✅ Successfully got default axios instance');
    } catch (error) {
      console.log(`  ❌ Failed to get default axios instance: ${error.message}`);
    }
    
    try {
      const axiosInstanceInvalid = unifiProtectAPI._getAxiosInstance('C');
      console.log('  ❌ Got axios instance for invalid Instance C (should have failed)');
    } catch (error) {
      console.log(`  ✅ Correctly failed to get axios instance for invalid Instance C: ${error.message}`);
    }
    
    console.log('\nTest completed successfully.');
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
  }
}

// Run the test
testUnifiProtectEnv();