# Door Status Monitoring Fix

## Issue
The system was encountering an error when attempting to broadcast door status updates:

```
Broadcasting door status update for 62 doors
2025-08-01 13:32:16 API response was not successful: NOT FOUND
```

## Root Causes

Two issues were identified:

1. **Missing Method**: The `getDoorStatus` method was missing from the `unifiAccessController.js` file. This method is required to retrieve the status of a specific door from the UniFi Access API.

2. **Environment Variable Mismatch**: The `realtimeService.js` file was checking for the wrong environment variables for UniFi Access authentication. It was looking for `UNIFI_ACCESS_USERNAME` and `UNIFI_ACCESS_PASSWORD`, but the UniFi Access API is now using `UNIFI_ACCESS_API_KEY` for authentication.

## Changes Made

### 1. Added getDoorStatus method to unifiAccessController.js

Added a new method to retrieve door status information:

```javascript
/**
 * Get door status
 * @param {string} doorId - Door ID
 * @returns {Promise<Object>} Door status
 */
exports.getDoorStatus = async (doorId) => {
  try {
    // Get door details which includes status information
    const doorDetails = await unifiAccessAPI.getDoorDetails(doorId);
    
    // Extract and return relevant status information
    return {
      id: doorDetails.id,
      name: doorDetails.name,
      status: doorDetails.status,
      locked: doorDetails.locked,
      open: doorDetails.open,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Controller error fetching UniFi Access door status for ${doorId}:`, error);
    throw error;
  }
};
```

### 2. Updated environment variable checks in realtimeService.js

Changed the environment variable checks in two locations to use `UNIFI_ACCESS_API_KEY` instead of `UNIFI_ACCESS_USERNAME` and `UNIFI_ACCESS_PASSWORD`:

#### In startDoorStatusMonitoring method:
```javascript
// Check if UniFi Access environment variables are set
const unifiAccessConfigured = process.env.UNIFI_ACCESS_HOST && 
                             process.env.UNIFI_ACCESS_API_KEY;
```

#### In initializeUnifiIntegrations method:
```javascript
// Initialize UniFi Access API
if (process.env.UNIFI_ACCESS_HOST && process.env.UNIFI_ACCESS_API_KEY) {
  console.log('Initializing UniFi Access API...');
  const unifiAccessAPI = new UnifiAccessAPI();
  await unifiAccessAPI.initialize();
  console.log('UniFi Access API initialized successfully');
} else {
  console.log('UniFi Access environment variables not set, skipping initialization');
}
```

### 3. Created a test script

Created a test script (`test-door-status-monitoring.js`) to verify the changes to the door status monitoring functionality. This script:

- Sets environment variables for testing
- Initializes the realtime service
- Triggers a door status check
- Logs the results

## Testing

To test the fix:

1. Ensure the environment variables are set correctly:
   - `UNIFI_ACCESS_HOST`: The hostname or IP address of the UniFi Access controller
   - `UNIFI_ACCESS_API_KEY`: The API key for authenticating with the UniFi Access controller
   - `UNIFI_ACCESS_PORT`: (Optional) The port for the UniFi Access controller (default: 443)

2. Run the test script:
   ```
   node test-door-status-monitoring.js
   ```

3. Check the logs for any errors and verify that door status updates are being broadcasted correctly.

## Expected Outcome

The system should now be able to successfully retrieve and broadcast door status updates without encountering the "NOT FOUND" error.