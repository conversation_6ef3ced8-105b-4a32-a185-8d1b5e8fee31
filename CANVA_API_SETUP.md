# Canva API Integration Setup Guide

This guide explains how to set up the Canva API integration for the CSF Portal. The integration allows users to access Canva designs, templates, and other resources directly within the portal.

## Integration Methods

The Canva integration supports two authentication methods:

1. **Global API Key** - A single API key for all users (legacy method)
2. **OAuth Authentication** - User-specific authentication with Canva (recommended)

## Prerequisites

- A Canva account with administrator access
- Access to the CSF Portal server environment variables
- For OAuth: A Canva Developer App with OAuth credentials

## Setting Up OAuth Authentication (Recommended)

OAuth authentication allows each user to authenticate with their own Canva account, providing a more secure and personalized experience.

### Creating a Canva Developer App

1. Log in to your Canva account
2. Go to [Canva Developer Portal](https://www.canva.dev/)
3. Click on "Create App"
4. Enter a name for your app (e.g., "CSF Portal Integration")
5. Select the necessary scopes:
   - `designs:read` - Access to read designs
   - `templates:read` - Access to read templates
   - `users:read` - Access to read user information
   - `files:read` - Access to read files
6. Add your redirect URI (e.g., `https://your-app-url.com/api/canva/callback`)
7. Complete the app creation process
8. Copy your Client ID and Client Secret

### Configuring OAuth Environment Variables

Add the following variables to your `.env` file:

```
# Canva API configuration (optional for global access)
CANVA_DOMAIN=canva.com
CANVA_API_KEY=your_canva_api_key

# Canva OAuth configuration (required for user-specific authentication)
CANVA_CLIENT_ID=your_canva_client_id
CANVA_CLIENT_SECRET=your_canva_client_secret
CANVA_REDIRECT_URI=https://your-app-url.com/api/canva/callback
```

Notes:
- For `CANVA_DOMAIN`, enter only the domain name without "https://" or trailing slashes (e.g., `canva.com`)
- Replace `your_canva_client_id` and `your_canva_client_secret` with the values from your Canva Developer App
- The `CANVA_REDIRECT_URI` must match exactly what you configured in the Canva Developer Portal
- The global `CANVA_API_KEY` is optional if you're using OAuth authentication

## Setting Up Global API Key (Legacy Method)

If you prefer to use a single API key for all users, follow these steps:

### Obtaining a Canva API Key

1. Log in to your Canva account
2. Go to Account Settings → Integrations
3. Navigate to the Developer section
4. Click on "Create new API key"
5. Enter a name for your API key (e.g., "CSF Portal Integration")
6. Select the necessary permissions:
   - Designs (Read)
   - Templates (Read)
   - Users (Read)
   - Files (Read)
7. Copy the generated API key

### Configuring API Key Environment Variables

Add the following variables to your `.env` file:

```
CANVA_DOMAIN=canva.com
CANVA_API_KEY=your_canva_api_key
```

## Updating Environment Variables in Production

If you're updating the environment variables in a production environment:

1. Add the variables to your server's environment configuration
2. Restart the application to apply the changes

For Docker deployments:
```bash
# Update the environment variables in your docker-compose.yml file or add them to your Docker run command
docker-compose down
docker-compose up -d
```

For DigitalOcean App Platform:
1. Go to the App settings
2. Navigate to the Environment Variables section
3. Add or update the Canva environment variables
4. Save changes and trigger a deployment

## User Experience

With OAuth authentication enabled, users will see a "Connect with Canva" button in the Canva section of the portal. When they click this button:

1. They will be redirected to Canva's authorization page
2. They will be asked to grant permission to the app
3. After authorizing, they will be redirected back to the portal
4. Their Canva resources will be accessible through the portal

## Verifying the Configuration

To verify that the Canva integration is properly configured:

1. Log in to the CSF Portal
2. Navigate to the Canva section
3. If using OAuth, you should see a "Connect with Canva" button
4. After connecting, you should see a list of your Canva designs and templates
5. If you see an error message, check that your environment variables are correctly set

## Troubleshooting

If you encounter issues with the Canva integration:

1. **Configuration Not Found**: Ensure that the environment variables are correctly set
2. **Authentication Failed**: 
   - For OAuth: Verify that your Client ID and Client Secret are valid
   - For API Key: Verify that your API key is valid and has the necessary permissions
3. **Redirect URI Mismatch**: Ensure that the redirect URI in your environment variables exactly matches the one configured in the Canva Developer Portal
4. **API Rate Limits**: Canva may impose rate limits on API requests. If you're experiencing throttling, consider implementing caching strategies

## Security Considerations

- OAuth tokens and API keys provide access to Canva resources, so keep them secure
- OAuth is more secure than using a global API key as each user authenticates with their own account
- Regularly rotate your API keys and review OAuth app permissions
- Use environment variables instead of storing credentials in the database
- Never expose credentials in client-side code or public repositories

## Additional Resources

- [Canva Connect API Documentation](https://www.canva.dev/docs/connect/)
- [Canva SCIM API Documentation](https://www.canva.dev/docs/scim/)
- [Canva Audit Logs Documentation](https://www.canva.dev/docs/audit-logs/)
- [CSF Portal Development Guide](./DEVELOPMENT.md)
- [Canva OAuth Frontend Implementation Guide](./CANVA_OAUTH_FRONTEND_IMPLEMENTATION.md)