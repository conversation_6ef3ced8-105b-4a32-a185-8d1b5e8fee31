# 🎫 Complete Ticketing System Setup Guide

Your comprehensive ticketing system is ready! Choose your preferred email integration method below.

## 🚀 Quick Start Options

### Option 1: Automated Gmail API Setup (Recommended)

**One-command setup:**
```bash
# Install dependencies and run automated setup
./install-ticketing-system.sh
```

**Or run step by step:**
```bash
# Install dependencies
npm install @google-cloud/pubsub googleapis imap mailparser

# Run setup wizard
npm run setup-gmail-ticketing
```

This will automatically:
- ✅ Set up Google Cloud project and APIs
- ✅ Create service accounts and permissions
- ✅ Configure Pub/Sub for real-time email processing
- ✅ Generate all environment variables
- ✅ Test the configuration

### Option 2: Simple IMAP Polling

**Add to your .env file:**
```env
# IMAP Configuration
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=your-app-password

# Basic SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
TICKET_EMAIL_FROM=<EMAIL>
```

### Option 3: Email Forwarding with Mailgun

**Set up Mailgun forwarding:**
```env
# Mailgun Configuration
MAILGUN_API_KEY=your-mailgun-api-key
MAILGUN_DOMAIN=yourdomain.com
MAILGUN_WEBHOOK_SIGNING_KEY=your-webhook-signing-key
```

## 🎯 What You Get

### ✅ **Complete Ticketing System**
- **Email-to-Ticket Creation** - Automatically converts emails to tickets
- **Smart Reply Threading** - Links email replies to existing tickets
- **Auto-Assignment Rules** - Routes tickets based on content and rules
- **SLA Tracking** - Response time and resolution monitoring
- **Tag & Category Management** - Flexible organization system
- **Role-Based Access Control** - Different permissions for different users

### ✅ **Google Forms Integration**
- **Advanced Field Mapping** with token system (`$currentUser`, `$dueDate+7`)
- **Smart Assignment Rules** based on form responses
- **Automatic User Creation** for external submissions
- **Custom Field Transformations** (email lookups, tag parsing, etc.)

### ✅ **Professional Email System**
- **HTML Email Templates** with modern styling
- **Auto-Response Messages** for new tickets
- **Notification System** for all ticket events
- **External User Support** for non-portal users

### ✅ **Modern Web Interface**
- **Clean Material-UI Components** with responsive design
- **Advanced Filtering & Search** capabilities
- **Real-time Updates** via WebSocket
- **Dashboard Widgets** for ticket overview
- **Mobile-Friendly** interface

## 📋 Required Environment Variables

After running the setup, ensure these are configured:

```env
# Core Configuration
PORTAL_URL=https://your-actual-portal.com
WEBHOOK_SECRET=auto-generated-secure-key

# Email Configuration (choose one method)
# Gmail API Method:
GOOGLE_GMAIL_PROJECT_ID=your-project-id
GOOGLE_GMAIL_SERVICE_ACCOUNT_KEY=./gmail-ticketing-service-account-key.json
GMAIL_MONITORED_EMAIL=<EMAIL>

# OR IMAP Method:
IMAP_HOST=imap.gmail.com
IMAP_USER=<EMAIL>
IMAP_PASS=your-app-password

# SMTP for outgoing emails
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
TICKET_EMAIL_FROM=<EMAIL>
```

## 🔧 Post-Setup Configuration

### 1. **Create App Password (Google Workspace)**
1. Go to [Google Account Security](https://myaccount.google.com/security)
2. Enable 2-Step Verification 
3. Generate App Password for "Mail"
4. Update `SMTP_PASS` in your .env file

### 2. **Configure Email Routing**
- Set up `<EMAIL>` to receive tickets
- Configure auto-forwarding if using external services
- Test email delivery to your ticketing system

### 3. **Set Up Categories and Tags**
```bash
# Start your application
npm run dev

# Access the admin panel to configure:
# - Ticket categories with auto-assignment rules  
# - Tags with auto-tagging rules
# - User roles and permissions
# - Google Forms webhook mappings
```

## 🧪 Testing Your Setup

### 1. **Test Email-to-Ticket**
```bash
# Send email to: <EMAIL>
# Subject: Test Ticket Creation
# Body: This is a test ticket from email

# Check server logs for processing
# Verify ticket appears in portal
```

### 2. **Test Email Threading**
```bash
# Reply to the auto-response email
# Verify reply is added to existing ticket
# Check notification emails are sent
```

### 3. **Test Google Forms Integration**
```bash
# Create a Google Form
# Set up webhook mapping in portal
# Submit test form response
# Verify ticket is created with correct mapping
```

## 🔐 Security Best Practices

- ✅ **App Passwords**: Never use main account passwords
- ✅ **HTTPS Only**: Always use HTTPS for webhooks
- ✅ **Key Rotation**: Regularly rotate service account keys
- ✅ **Permission Limits**: Use minimal required permissions
- ✅ **Webhook Secrets**: Always validate webhook signatures

## 📞 Support & Troubleshooting

### Common Issues:

**"Authentication failed":**
```bash
# Check Google Cloud CLI
gcloud auth list
gcloud auth login

# Verify project access
gcloud config get-value project
```

**"Permission denied":**
```bash
# Check service account roles
gcloud projects get-iam-policy YOUR_PROJECT_ID

# Re-run setup if needed
npm run setup-gmail-ticketing
```

**"Emails not processing":**
```bash
# Check server logs
npm run dev

# Verify webhook endpoint
curl -X POST https://yourportal.com/api/ticket-email/webhook

# Test SMTP configuration
```

### Debug Commands:
```bash
# Test Google APIs
node -e "console.log(require('googleapis').version)"

# Test Pub/Sub
gcloud pubsub topics list

# Test email parsing
node -e "
const ticketEmailController = require('./server/controllers/ticketEmailController');
console.log('Email controller loaded successfully');
"
```

## 🚀 Go Live Checklist

- [ ] Dependencies installed
- [ ] Gmail API setup completed (if using)
- [ ] App passwords generated
- [ ] Environment variables configured
- [ ] Portal URL updated
- [ ] Email routing configured
- [ ] Test emails successful
- [ ] Categories and tags configured
- [ ] User permissions set up
- [ ] Google Forms webhooks tested
- [ ] Monitoring and logging enabled

---

**🎉 Congratulations!** Your enterprise-grade ticketing system is now ready for production use. Send a test email to get started!