# Use Node.js 18.14 as the base image
FROM node:18.14-alpine

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy client package.json and package-lock.json
COPY client/package*.json ./client/

# Copy the rest of the application code
COPY . .

# Install client dependencies and build (ensuring dev dependencies are included)
# Using a more Windows-compatible approach
WORKDIR /app/client
RUN NPM_CONFIG_PRODUCTION=false npm install
# Fix for Windows path resolution issue with react-app-rewired
RUN npx react-app-rewired build
WORKDIR /app

# Expose the ports the app runs on
EXPOSE 8080
# Expose RADIUS server ports (UDP)
EXPOSE 1812/udp 1813/udp

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080
# RADIUS server environment variables
# Note: For security, RADIUS_SECRET should be provided at runtime
# TLS can be enabled by setting RADIUS_TLS_ENABLED=true and providing key/cert paths
ENV RADIUS_PORT=1812
ENV RADIUS_AUTHORIZATION_PORT=1813
ENV RADIUS_ADDRESS=0.0.0.0
ENV RADIUS_AUTHENTICATION=StaticAuth

# Start the application
CMD ["node", "server.js"]
