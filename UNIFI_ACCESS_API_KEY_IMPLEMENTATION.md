# UniFi Access API Key Implementation

## Overview

This document describes the changes made to update the UniFi Access integration to use API key authentication instead of username/password authentication. This change improves security and reliability of the integration.

## Changes Made

### 1. UniFi Access API Class

Modified the `UnifiAccessAPI` class to use API key authentication:

- Updated the constructor to read the API key from environment variables:
  ```javascript
  this.apiKey = process.env.UNIFI_ACCESS_API_KEY || '';
  ```
  
- Removed username and password properties:
  ```javascript
  // Removed:
  this.username = process.env.UNIFI_ACCESS_USERNAME || '';
  this.password = process.env.UNIFI_ACCESS_PASSWORD || '';
  ```

- Updated the `authenticate` method to use the API key:
  ```javascript
  async authenticate() {
    try {
      if (!this.apiKey) {
        throw new Error('API key is not configured. Please set the UNIFI_ACCESS_API_KEY environment variable.');
      }

      // Set the API key in the Authorization header
      this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.apiKey}`;
      
      // Make a simple request to verify the API key works
      const response = await this.axios.get('/system');
      
      // Update integration status
      integrationTracker.updateStatus(
        this.integrationName, 
        'active', 
        new Date(), 
        'Successfully authenticated with UniFi Access using API key.'
      );

      return true;
    } catch (error) {
      // Error handling...
    }
  }
  ```

- Updated the `isAuthenticated` method to check for the API key:
  ```javascript
  async isAuthenticated() {
    try {
      if (!this.apiKey) {
        console.error('API key is not configured');
        return false;
      }

      // If we haven't set the Authorization header yet, set it now
      if (!this.axios.defaults.headers.common['Authorization']) {
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.apiKey}`;
      }

      // Make a simple request to verify the API key works
      try {
        await this.axios.get('/system');
        return true;
      } catch (apiError) {
        console.error('API key validation failed:', apiError);
        return false;
      }
    } catch (error) {
      // Error handling...
    }
  }
  ```

- Added a helper method to ensure the API key is set in headers:
  ```javascript
  ensureApiKeySet() {
    if (!this.axios.defaults.headers.common['Authorization']) {
      if (!this.apiKey) {
        throw new Error('API key is not configured. Please set the UNIFI_ACCESS_API_KEY environment variable.');
      }
      this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.apiKey}`;
    }
  }
  ```

- Updated all API methods to use the `ensureApiKeySet` helper instead of checking for token or cookies:
  ```javascript
  async getDoors() {
    try {
      this.ensureApiKeySet();
      const response = await this.axios.get('/doors');
      return response.data;
    } catch (error) {
      // Error handling...
    }
  }
  ```

### 2. Test Script

Updated the test script (`test-unifi-access-env.js`) to test the API key authentication:

- Updated environment variables for testing:
  ```javascript
  process.env.UNIFI_ACCESS_HOST = 'test-host';
  process.env.UNIFI_ACCESS_API_KEY = 'test-api-key';
  process.env.UNIFI_ACCESS_PORT = '443';
  ```

- Updated the verification logic to check for the API key:
  ```javascript
  if (
    unifiAccessAPI.host === process.env.UNIFI_ACCESS_HOST &&
    unifiAccessAPI.apiKey === process.env.UNIFI_ACCESS_API_KEY &&
    unifiAccessAPI.port.toString() === process.env.UNIFI_ACCESS_PORT
  ) {
    console.log('\n✅ API is correctly using environment variables');
  }
  ```

- Added tests for the `ensureApiKeySet` method:
  ```javascript
  console.log('\nTesting ensureApiKeySet method...');
  try {
    unifiAccessAPI.ensureApiKeySet();
    console.log('✅ ensureApiKeySet method executed successfully');
    
    // Check if Authorization header is set correctly
    const authHeader = unifiAccessAPI.axios.defaults.headers.common['Authorization'];
    if (authHeader === `Bearer ${process.env.UNIFI_ACCESS_API_KEY}`) {
      console.log('✅ Authorization header is set correctly');
    } else {
      console.log('❌ Authorization header is NOT set correctly');
    }
  } catch (error) {
    console.error('❌ ensureApiKeySet method failed:', error);
  }
  ```

### 3. Documentation

Updated the documentation (`UNIFI_ACCESS_ENV_VARS_IMPLEMENTATION.md`) to reflect the API key usage:

- Updated the environment variables section:
  ```markdown
  | Environment Variable | Description | Default |
  |----------------------|-------------|---------|
  | `UNIFI_ACCESS_HOST` | The hostname or IP address of the UniFi Access server | None (required) |
  | `UNIFI_ACCESS_API_KEY` | The API key for authentication | None (required) |
  | `UNIFI_ACCESS_PORT` | The port number | 443 |
  ```

- Added a note about the update:
  ```markdown
  > **Note**: As of 2025-07-30, the integration uses API key authentication instead of username/password authentication. The `UNIFI_ACCESS_USERNAME` and `UNIFI_ACCESS_PASSWORD` environment variables are no longer used.
  ```

## Testing

The changes were tested using the updated test script, which verifies:

1. The API correctly reads the environment variables
2. The API key is correctly set in the Authorization header
3. The `ensureApiKeySet` method works as expected

## Benefits

1. **Improved Security**: API keys are more secure than username/password authentication, especially for automated systems.
2. **Better Reliability**: API keys are less likely to expire or be affected by password policy changes.
3. **Simplified Authentication**: The authentication process is now simpler and more direct.
4. **Reduced Overhead**: No need to store and manage session tokens or cookies.

## Next Steps

1. Monitor the integration to ensure it continues to work correctly with the API key authentication.
2. Consider implementing API key rotation as a future enhancement.