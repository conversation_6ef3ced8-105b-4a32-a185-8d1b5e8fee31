# Spotify Authentication Fix

## Issue Description

The application was experiencing a 401 Unauthorized error when attempting to fetch Spotify devices:

```json
{
    "message": "Error fetching Spotify devices",
    "error": "Request failed with status code 401"
}
```

This error indicated that the authentication with the Spotify API was failing, likely due to issues with the access token or refresh token.

## Root Cause Analysis

After examining the code, the following potential causes were identified:

1. The Spotify refresh token might have expired or been revoked
2. The Spotify client ID or client secret might be invalid
3. The token refresh mechanism wasn't handling authentication failures properly
4. There was no retry logic for transient network issues

## Changes Made

### 1. Enhanced Token Refresh Mechanism

Updated the `authenticateSpotify` method in `wiimAPI.js` to:
- Handle new refresh tokens that might be returned by Spotify
- Provide more detailed error logging
- Add specific error handling for common authentication issues

```javascript
async authenticateSpotify() {
  try {
    // Check if we have the required Spotify credentials
    if (!this.spotifyClientId || !this.spotifyClientSecret || !this.spotifyRefreshToken) {
      throw new Error('Spotify credentials are missing. Please set SPOTIFY_CLIENT_ID, SPOTIFY_CLIENT_SECRET, and SPOTIFY_REFRESH_TOKEN in your environment variables.');
    }

    // Get a new access token using the refresh token
    const tokenResponse = await axios.post('https://accounts.spotify.com/api/token', 
      querystring.stringify({
        grant_type: 'refresh_token',
        refresh_token: this.spotifyRefreshToken
      }), 
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': 'Basic ' + Buffer.from(this.spotifyClientId + ':' + this.spotifyClientSecret).toString('base64')
        }
      }
    );

    // Store the access token and calculate expiry time
    this.spotifyAccessToken = tokenResponse.data.access_token;
    // Set expiry time to 5 minutes before actual expiry to be safe
    this.spotifyTokenExpiry = Date.now() + (tokenResponse.data.expires_in - 300) * 1000;

    // If we received a new refresh token, update it
    if (tokenResponse.data.refresh_token) {
      this.spotifyRefreshToken = tokenResponse.data.refresh_token;
      console.log('Received and updated Spotify refresh token');
    }

    // Update the Spotify axios instance with the new token
    this.spotifyAxios.defaults.headers.common['Authorization'] = `Bearer ${this.spotifyAccessToken}`;

    console.log('Successfully authenticated with Spotify');
    return this.spotifyAccessToken;
  } catch (error) {
    // Enhanced error logging with more details
    const errorDetails = {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    };
    
    console.error('Error authenticating with Spotify:', errorDetails);
    
    // Handle specific error cases
    if (error.response) {
      if (error.response.status === 400 && error.response.data?.error === 'invalid_grant') {
        throw new Error('Spotify refresh token is invalid or has been revoked. Please generate a new refresh token following the instructions in WIIM_SPOTIFY_INTEGRATION_CHANGES.md.');
      } else if (error.response.status === 401) {
        throw new Error('Spotify client credentials (client ID or client secret) are invalid. Please check your environment variables.');
      }
    }
    
    throw error;
  }
}
```

### 2. Added Retry Logic for Token Refresh

Updated the `ensureSpotifyAuthenticated` method in `wiimAPI.js` to add retry logic with exponential backoff:

```javascript
async ensureSpotifyAuthenticated(retryCount = 0, maxRetries = 2) {
  try {
    // If we don't have an access token or it's expired, get a new one
    if (!this.spotifyAccessToken || !this.spotifyTokenExpiry || Date.now() >= this.spotifyTokenExpiry) {
      return await this.authenticateSpotify();
    }
    return this.spotifyAccessToken;
  } catch (error) {
    console.error(`Error ensuring Spotify authentication (attempt ${retryCount + 1}/${maxRetries + 1}):`, error);
    
    // If we haven't reached the maximum number of retries and the error is potentially recoverable, retry
    if (retryCount < maxRetries && this.isRetryableError(error)) {
      console.log(`Retrying Spotify authentication (attempt ${retryCount + 2}/${maxRetries + 1})...`);
      // Wait for a short time before retrying (exponential backoff)
      const delay = Math.pow(2, retryCount) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
      return this.ensureSpotifyAuthenticated(retryCount + 1, maxRetries);
    }
    
    // If we've reached the maximum number of retries or the error is not retryable, throw the error
    throw error;
  }
}

isRetryableError(error) {
  // Network errors, timeouts, and 5xx server errors are generally retryable
  if (!error.response) {
    // Network error or timeout
    return true;
  }
  
  // 5xx server errors are retryable
  if (error.response.status >= 500 && error.response.status < 600) {
    return true;
  }
  
  // 429 Too Many Requests is retryable
  if (error.response.status === 429) {
    return true;
  }
  
  // 401/403 errors are generally not retryable unless they're due to token expiration,
  // which is already handled in authenticateSpotify
  return false;
}
```

### 3. Improved Error Handling in API Methods

Updated the `getSpotifyDevices` method in `wiimAPI.js` to provide more detailed error information:

```javascript
async getSpotifyDevices() {
  try {
    // Ensure we have a valid Spotify access token
    await this.ensureSpotifyAuthenticated();

    // Get available devices from Spotify Connect API
    const response = await this.spotifyAxios.get('/me/player/devices');
    return response.data.devices;
  } catch (error) {
    // Enhanced error logging with more details
    const errorDetails = {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    };
    
    console.error('Error getting Spotify devices:', errorDetails);
    
    // Create a more descriptive error based on the response
    if (error.response) {
      if (error.response.status === 401) {
        // Create a custom error for authentication failures
        const authError = new Error('Spotify authentication failed. Please check your Spotify credentials in the environment variables.');
        authError.status = 401;
        authError.isAuthError = true;
        throw authError;
      } else if (error.response.status === 403) {
        // Create a custom error for permission issues
        const permissionError = new Error('Spotify permission denied. Your Spotify account may not have the required permissions.');
        permissionError.status = 403;
        permissionError.isPermissionError = true;
        throw permissionError;
      }
    }
    
    throw error;
  }
}
```

### 4. Enhanced Controller Error Handling

Updated the `getSpotifyDevices` controller in `wiimController.js` to handle custom errors and return appropriate status codes:

```javascript
exports.getSpotifyDevices = async (req, res) => {
  try {
    ensureApiInitialized();
    const devices = await wiimAPI.getSpotifyDevices();
    res.json(devices);
  } catch (error) {
    console.error('Controller error fetching Spotify devices:', error);
    
    // Handle specific error types with appropriate status codes
    if (error.isAuthError) {
      return res.status(401).json({ 
        message: 'Error fetching Spotify devices', 
        error: error.message,
        details: 'Authentication failed. Please check your Spotify credentials in the environment variables.'
      });
    } else if (error.isPermissionError) {
      return res.status(403).json({ 
        message: 'Error fetching Spotify devices', 
        error: error.message,
        details: 'Permission denied. Your Spotify account may not have the required permissions.'
      });
    } else if (error.message && error.message.includes('refresh token is invalid or has been revoked')) {
      return res.status(401).json({ 
        message: 'Error fetching Spotify devices', 
        error: error.message,
        details: 'Spotify refresh token is invalid or has been revoked. Please generate a new refresh token.'
      });
    }
    
    // Default error response
    res.status(500).json({ message: 'Error fetching Spotify devices', error: error.message });
  }
};
```

### 5. Improved Client-Side Error Handling

Updated the client-side service (`wiimService.js`) to handle error responses and provide meaningful error messages:

```javascript
getSpotifyDevices: async () => {
  try {
    const response = await axios.get('/api/wiim/spotify/devices');
    return response.data;
  } catch (error) {
    // Enhanced error logging with more details
    console.error('Error fetching Spotify devices:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    
    // Create a more user-friendly error message based on the response
    if (error.response) {
      const errorData = error.response.data;
      
      if (error.response.status === 401) {
        // Authentication error
        const errorMessage = errorData.details || errorData.error || 'Authentication failed. Please check Spotify credentials.';
        const authError = new Error(errorMessage);
        authError.status = 401;
        throw authError;
      } else if (error.response.status === 403) {
        // Permission error
        const errorMessage = errorData.details || errorData.error || 'Permission denied. Your Spotify account may not have the required permissions.';
        const permissionError = new Error(errorMessage);
        permissionError.status = 403;
        throw permissionError;
      }
    }
    
    // For other errors, throw with the original message or a default one
    throw error.response?.data?.error 
      ? new Error(error.response.data.error)
      : error;
  }
},
```

### 6. Updated UI to Display Meaningful Error Messages

Updated the `fetchSpotifyDevices` function in `WiimPage.js` to display user-friendly error messages:

```javascript
const fetchSpotifyDevices = async () => {
  if (!configStatus) return;
  
  setLoadingSpotifyDevices(true);
  try {
    const devices = await wiimService.getSpotifyDevices();
    setSpotifyDevices(devices || []);
    
    // If we have devices and no device is selected, select the first active one or the first one
    if (devices && devices.length > 0 && !selectedSpotifyDevice) {
      const activeDevice = devices.find(device => device.is_active);
      if (activeDevice) {
        setSelectedSpotifyDevice(activeDevice.id);
      } else {
        setSelectedSpotifyDevice(devices[0].id);
      }
    }
    
    // Clear any previous errors
    setError(null);
  } catch (err) {
    console.error('Error fetching Spotify devices:', err);
    
    // Set a user-friendly error message based on the error
    if (err.status === 401) {
      setError(`Spotify authentication failed: ${err.message}. Please check the Spotify credentials in the server configuration.`);
    } else if (err.status === 403) {
      setError(`Spotify permission denied: ${err.message}. Your Spotify account may not have the required permissions.`);
    } else if (err.message && err.message.includes('refresh token')) {
      setError(`Spotify refresh token issue: ${err.message}. Please generate a new refresh token.`);
    } else {
      // Only set a generic error if it's not an authentication issue (which might be expected if Spotify is not configured)
      setError(`Error fetching Spotify devices: ${err.message}`);
    }
  } finally {
    setLoadingSpotifyDevices(false);
  }
};
```

## How to Fix the Issue

Based on the error message and the changes made, the most likely cause of the 401 error is an expired or revoked Spotify refresh token. To fix this issue:

1. Generate a new Spotify refresh token following the instructions in the `WIIM_SPOTIFY_INTEGRATION_CHANGES.md` file
2. Update the `SPOTIFY_REFRESH_TOKEN` environment variable with the new token
3. Restart the application

If the issue persists, check the server logs for more detailed error messages that can help identify the specific problem.

## Conclusion

The changes made to the codebase improve the handling of Spotify authentication errors by:

1. Providing more detailed error messages that help identify the specific issue
2. Adding retry logic for transient network issues
3. Handling expired or revoked refresh tokens gracefully
4. Displaying user-friendly error messages in the UI

These improvements make the application more robust and user-friendly when dealing with Spotify API authentication issues.