# UniFi Network Statistics Endpoint Fix

## Issue
The UniFi Network statistics endpoint was failing with the following error:
```
{
    "message": "UniFi Network devices endpoint not found",
    "error": "The requested endpoint does not exist or is not accessible. This may indicate an incompatible UniFi Network controller version.",
    "timestamp": "2025-07-29T20:30:57.264Z"
}
```

This indicated that the API endpoint being used for device statistics in the UniFi Network API wrapper was no longer valid or had changed in the current version of the UniFi Network Controller.

## Solution
The UniFi Network API wrapper has been updated to add multiple fallback endpoints for the device statistics method. This approach follows the same pattern used in other successful methods like `getDevices` and `getDeviceDetails`, which try multiple endpoint patterns to ensure compatibility with different UniFi Network Controller versions.

The following endpoints are now tried in sequence for the `getDeviceStatistics` method:

1. New integration API v2: `/proxy/network/integration/v2/sites/${site}/devices/${deviceId}/stats`
2. New integration API v1: `/proxy/network/integration/v1/sites/${site}/devices/${deviceId}/stats`
3. Old UniFi OS endpoint: `/proxy/network/api/s/${site}/stat/device/${deviceId}/stats`
4. Legacy endpoint: `/api/s/${site}/stat/device/${deviceId}/stats`
5. Direct network API: `/api/v1/network/devices/${deviceId}/stats`

If all endpoints fail, the method falls back to returning basic statistics from the device details.

## Implementation Details

### API Method Changes
The `getDeviceStatistics` method in the UniFi Network API wrapper has been updated to:

1. Add multiple fallback endpoints following the pattern used in other methods
2. Add detailed logging for each endpoint attempt
3. Improve error handling with the `_logDetailedError` method

### Controller Method Changes
The `getDeviceStatistics` method in the UniFi Network controller has been updated to:

1. Add more detailed logging
2. Check if device statistics are found and return a 404 if not
3. Improve error handling with specific status codes and messages for different error types
4. Include a timestamp in the error response

## Testing
A test script (`test-unifi-network-fix.js`) has been updated to verify the changes. The script now tests:

1. The `getDevices` method
2. The `getClients` method
3. The `getDeviceStatistics` method

To run the test:
```
node test-unifi-network-fix.js
```

## Notes
- This update ensures compatibility with a wide range of UniFi Network Controller versions by trying multiple endpoint patterns
- The fallback mechanism allows the integration to work even if some endpoints are not available in the specific UniFi Network Controller version being used
- Detailed logging has been added to help diagnose any issues with the device statistics endpoint

## References
- UniFi Network API Documentation: https://ubntwiki.com/products/software/unifi-controller/api
- Last updated: 2025-07-29