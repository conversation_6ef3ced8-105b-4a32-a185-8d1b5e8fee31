# Permission System Updates

This document describes the updates made to the permission system in the CSF Portal to hide menu links that a user's role doesn't allow them to access and to expand the user roles permissions system for more granular control.

## Overview

The permission system has been enhanced to:
1. Hide menu links that a user's role doesn't allow them to access
2. Add a 'delete' permission level between 'write' and 'admin' for more granular control

## Changes Made

### 1. Added 'delete' Permission Level

The permission hierarchy has been expanded to include a 'delete' level:
- none (no access)
- read (view only)
- write (create and edit)
- delete (create, edit, and delete)
- admin (full control)

### 2. Updated Server-Side Permission Checking

The `hasPermission` middleware in `middleware/auth.js` has been updated to handle the new permission hierarchy:
- 'read' access is granted if a user has 'write', 'delete', or 'admin' permissions
- 'write' access is granted if a user has 'delete' or 'admin' permissions
- 'delete' access is granted if a user has 'admin' permissions

### 3. Added Client-Side Permission Checking

A `hasPermission` function has been added to the `AuthContext` to check if a user has a specific permission:
- Checks if the user has the admin role (admins have all permissions)
- Checks if any of the user's roles have the required permission
- Handles hierarchical permissions (entity:level)

### 4. Updated Role Management UI

The role management UI in `AdminRolesPage.js` has been updated to include the 'delete' permission level in the permission selection UI.

### 5. Added Permission-Based Menu Filtering

The menu rendering in `Layout.js` has been updated to hide menu links based on user permissions:
- Each menu item now includes a required permission (e.g., 'dashboard:read')
- Menu items are filtered based on whether the user has the required permission
- Admin menu items require admin-level permissions (e.g., 'users:admin')

### 6. Added Role Details API Endpoint

A new API endpoint `/api/roles/details` has been added to fetch all roles with their permissions, which is used by the client to determine what menu items to display.

## How to Use

### Defining Required Permissions for Menu Items

Menu items in `Layout.js` now include a `permission` property that specifies the required permission to access that item:

```javascript
{ text: 'Dashboard', icon: <DashboardIcon />, path: '/dashboard', auth: true, permission: 'dashboard:read' }
```

### Checking Permissions in Components

You can use the `hasPermission` function from the `AuthContext` to check if a user has a specific permission:

```javascript
import { useAuth } from '../context/AuthContext';

const MyComponent = () => {
  const { hasPermission } = useAuth();
  
  // Check if user has read access to dashboard
  const canViewDashboard = hasPermission('dashboard:read');
  
  // Check if user has write access to users
  const canEditUsers = hasPermission('users:write');
  
  // Check if user has delete access to shortcuts
  const canDeleteShortcuts = hasPermission('shortcuts:delete');
  
  // Check if user has admin access to roles
  const canManageRoles = hasPermission('roles:admin');
  
  // ...
};
```

### Setting Permissions for Roles

Permissions can be set for each role in the role management UI. The available permission levels are:
- None: No access
- Read: View only
- Write: Create and edit
- Delete: Create, edit, and delete
- Admin: Full control

## Testing

To test the permission system:
1. Create roles with different permission levels for various features and integrations
2. Assign these roles to users
3. Log in as these users and verify that they only see menu items they have permission to access
4. Verify that they can only perform actions according to their permission levels