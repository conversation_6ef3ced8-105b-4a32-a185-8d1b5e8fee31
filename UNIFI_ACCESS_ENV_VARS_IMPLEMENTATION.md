# UniFi Access Environment Variables Implementation

## Overview

This document describes the changes made to implement environment variable-based configuration for the UniFi Access integration, replacing the previous web-based configuration approach.

## Changes Made

### 1. UniFi Access API

Modified the `UnifiAccessAPI` class to read configuration from environment variables:

- Updated the constructor to no longer accept parameters
- Added code to read configuration from the following environment variables:
  - `UNIFI_ACCESS_HOST`: The hostname or IP address of the UniFi Access server
  - `UNIFI_ACCESS_API_KEY`: The API key for authentication
  - `UNIFI_ACCESS_PORT`: The port number (defaults to 443)

> **Update (2025-07-30)**: The integration now uses API key authentication instead of username/password authentication for improved security and reliability.

### 2. UniFi Access Controller

Updated the controller to use environment variables instead of database configuration:

- Removed the import of the `UnifiAccessConfig` model
- Removed the `getLatestConfig` helper function
- Updated the API initialization to use the new constructor without parameters
- Removed calls to `getLatestConfig()` from all API methods
- Updated configuration-related endpoints to inform users about environment variables:
  - `saveConfig`: Returns a message about using environment variables
  - `getConfig`: Checks if environment variables are set and returns their values
  - `oneClickSetup`: Returns a message about using environment variables

### 3. Client-Side Components

Updated client-side components to reflect the change to environment variables:

- Updated the `UnifiAccessSetup.js` page to:
  - Display information about environment variables
  - Provide a table of required environment variables with descriptions
  - Include instructions on how to set environment variables in different environments
  - Remove the form for manual configuration and the one-click setup button
- Updated the `unifiAccessService.js` service to:
  - Mark configuration-related methods as deprecated
  - Handle the new response formats from the API
  - Provide better error handling and logging

### 4. Testing

Created a test script (`test-unifi-access-env.js`) to verify that the integration works correctly with environment variables:

- Sets test environment variables
- Initializes the UniFi Access API
- Verifies that the API uses the values from the environment variables

## Environment Variables

The following environment variables must be set for the UniFi Access integration to work:

| Environment Variable | Description | Default |
|----------------------|-------------|---------|
| `UNIFI_ACCESS_HOST` | The hostname or IP address of the UniFi Access server | None (required) |
| `UNIFI_ACCESS_API_KEY` | The API key for authentication | None (required) |
| `UNIFI_ACCESS_PORT` | The port number | 443 |

> **Note**: As of 2025-07-30, the integration uses API key authentication instead of username/password authentication. The `UNIFI_ACCESS_USERNAME` and `UNIFI_ACCESS_PASSWORD` environment variables are no longer used.

## Setting Environment Variables

Depending on your deployment environment, you can set these variables in different ways:

- **Docker**: Use environment variables in your docker-compose.yml file or docker run command
- **Linux/macOS**: Add them to your .env file or export them in your startup script
- **Windows**: Set them through System Properties or in your startup script
- **Cloud Platforms**: Use the environment variables configuration in your cloud provider's dashboard

## API Documentation

For more information about the UniFi Access API, refer to the official documentation at:
https://assets.identity.ui.com/unifi-access/api_reference.pdf