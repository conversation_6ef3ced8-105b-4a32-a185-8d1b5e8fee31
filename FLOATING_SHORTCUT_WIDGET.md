# Quick Links Toolbar

## Overview

The Quick Links Toolbar is a feature that provides easy access to your favorite portal links from anywhere in the application. It appears as a toolbar at the bottom of the screen and can be customized to show your most frequently used shortcuts.

## Features

- **Quick Access**: Access your favorite shortcuts from any page in the portal
- **Customizable**: Choose which shortcuts appear in the widget
- **Persistent**: Your preferences are saved to your user account and available across devices
- **Searchable**: Easily find shortcuts when customizing
- **Toggleable**: Enable or disable the widget as needed
- **Synchronized**: Preferences automatically sync between devices when you log in

## User Guide

### Accessing the Toolbar

The quick links toolbar appears at the bottom of the screen. It displays your favorite shortcuts as icons that you can click to navigate to those pages.

### Using Quick Links

1. Click on any icon in the toolbar to navigate to that page
2. Hover over an icon to see the name of the shortcut
3. Click the expand (up arrow) icon to see a full list of your shortcuts with descriptions

### Customizing Your Quick Links

1. Click the edit (pencil) icon in the toolbar
2. The customization panel will open from the bottom of the screen
3. Use the search box to find specific shortcuts
4. Click the star icon next to shortcuts to add or remove them from your favorites
5. Click the X icon to exit edit mode and save your changes

### Toolbar Settings

1. Click the settings (gear) icon in the toolbar
2. In the settings dialog, you can:
   - Enable or disable the quick links toolbar
3. Click "Close" to save your settings

## Technical Implementation

The Quick Links Toolbar is implemented as a React component that integrates with the existing shortcuts functionality in the portal.

### Component Structure

- `FloatingShortcutWidget.js`: The main component that renders the toolbar at the bottom of the screen
- Integrated into the main `Layout.js` component to ensure it's available on all pages

### State Management

The toolbar uses React's useState and useEffect hooks to manage:
- Loading and error states
- Toolbar expanded/collapsed state
- Edit mode
- User preferences

### Data Persistence

User preferences are stored in both the database and browser's localStorage:

**Database Storage:**
- Preferences are saved to the user's account in the database
- Stored in the `widgetPreferences.floatingShortcut` field of the User model
- Contains `favoriteShortcuts` (array of shortcut IDs) and `enabled` (boolean)
- Allows preferences to be synchronized across multiple devices

**Local Storage:**
- `favoriteShortcuts`: Array of shortcut IDs that the user has selected as favorites
- `floatingShortcutWidgetEnabled`: Boolean indicating whether the widget is enabled
- Used for quick access without database queries on every page load
- Synchronized with the database at login and when preferences change

### API Integration

The widget integrates with several APIs:
- Fetches shortcuts from `/api/shortcuts`
- Tracks shortcut clicks with `/api/shortcuts/:id/click`
- Retrieves preferences from `/api/users/me/widget-preferences`
- Saves preferences to `/api/users/me/widget-preferences`

## Developer Notes

### Adding the Toolbar to New Pages

The toolbar is automatically included on all pages that use the main Layout component. No additional integration is needed for new pages.

### Customizing the Toolbar

If you need to customize the toolbar's appearance or behavior:

1. Modify the `FloatingShortcutWidget.js` component
2. The toolbar's appearance can be adjusted by changing the `sx` props on the AppBar and Toolbar components
3. Additional settings can be added to the settings dialog

### Future Enhancements

Potential future enhancements for the toolbar:
- Drag and drop reordering of shortcuts
- Custom categories or grouping
- Different display modes (text labels, icons with text, etc.)
- Keyboard shortcuts for quick access
- Customizable toolbar position (top, bottom, side)