# Quick Links Toolbar Testing Guide

## Testing Checklist

### Basic Functionality

- [ ] **Toolbar Visibility**: The quick links toolbar appears at the bottom of the screen for authenticated users
- [ ] **Toolbar Absence**: The toolbar does not appear for unauthenticated users
- [ ] **Using Shortcuts**: Clicking on a shortcut icon in the toolbar navigates to the correct page
- [ ] **Tooltips**: Hovering over an icon shows the shortcut name in a tooltip
- [ ] **Expanding Panel**: Clicking the expand icon opens the expanded panel with full shortcut details
- [ ] **Collapsing Panel**: Clicking the collapse icon closes the expanded panel

### Customization

- [ ] **Edit Mode**: Clicking the edit icon opens the edit panel
- [ ] **Search Functionality**: The search box filters shortcuts correctly
- [ ] **Adding Favorites**: Clicking the star icon on an unstarred shortcut adds it to favorites
- [ ] **Removing Favorites**: Clicking the star icon on a starred shortcut removes it from favorites
- [ ] **Persistence**: Favorite shortcuts persist after page refresh
- [ ] **Empty State**: When no favorites are selected, an appropriate message and button are shown

### Settings

- [ ] **Settings Dialog**: Clicking the settings icon opens the settings dialog
- [ ] **Enable/Disable**: Toggling the "Show quick links toolbar" checkbox enables/disables the toolbar
- [ ] **Persistence**: The enabled/disabled state persists after page refresh

### Edge Cases

- [ ] **Loading State**: The toolbar shows a loading indicator while fetching shortcuts
- [ ] **Error State**: The toolbar shows an error message if fetching shortcuts fails
- [ ] **Empty Shortcuts**: The toolbar handles the case where no shortcuts exist in the system
- [ ] **Many Favorites**: The toolbar handles the case where many shortcuts are added as favorites (horizontal scrolling works)
- [ ] **Long Shortcut Names**: The toolbar properly displays tooltips for shortcuts with long names
- [ ] **Responsive Behavior**: The toolbar works correctly on different screen sizes

## Testing Process

1. **Setup**:
   - Ensure you have an authenticated user account
   - Clear localStorage to start with a clean state

2. **Basic Functionality Testing**:
   - Log in to the portal
   - Verify the quick links toolbar appears at the bottom of the screen
   - Hover over icons to verify tooltips show shortcut names
   - Click an icon and verify navigation
   - Click the expand icon to view the full list of shortcuts
   - Click the collapse icon to close the expanded panel
   - Log out and verify the toolbar disappears

3. **Customization Testing**:
   - Log in to the portal
   - Click the edit icon in the toolbar
   - Verify the edit panel opens
   - Search for specific shortcuts
   - Add and remove shortcuts from favorites
   - Exit edit mode
   - Refresh the page and verify your selections persist

4. **Settings Testing**:
   - Click the settings icon in the toolbar
   - Disable the toolbar and verify it disappears
   - Refresh the page and verify it remains disabled
   - Re-enable the toolbar and verify it reappears

5. **Cross-Browser Testing**:
   - Test the toolbar in Chrome, Firefox, Safari, and Edge
   - Verify all functionality works consistently

6. **Mobile Testing**:
   - Test the toolbar on mobile devices or using responsive design mode
   - Verify the toolbar is usable on small screens

## Expected Results

When implemented correctly, the quick links toolbar should:

1. Provide easy access to favorite shortcuts from any page in the portal
2. Allow users to easily customize which shortcuts appear
3. Remember user preferences between sessions
4. Be unobtrusive but easily accessible
5. Work consistently across different browsers and devices

## Troubleshooting Common Issues

- **Toolbar doesn't appear**: Verify the user is authenticated and the toolbar is enabled in settings
- **Favorites don't persist**: Check localStorage access in the browser
- **Shortcuts don't load**: Check network requests to `/api/shortcuts` endpoint
- **Toolbar appears in wrong position**: Check CSS positioning and z-index values
- **Edit mode doesn't work**: Verify state management in the component