# Deploying CSF Portal to Docker Desktop on Windows Server

This document provides step-by-step instructions for deploying the CSF Portal application to Docker Desktop running on Windows Server.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Installing Docker Desktop on Windows Server](#installing-docker-desktop-on-windows-server)
- [Preparing the Environment](#preparing-the-environment)
- [Building and Running the Docker Container](#building-and-running-the-docker-container)
- [Environment Variables Configuration](#environment-variables-configuration)
- [Persistent Storage](#persistent-storage)
- [Networking Configuration](#networking-configuration)
- [Maintenance and Updates](#maintenance-and-updates)
- [Troubleshooting](#troubleshooting)

## Prerequisites

Before deploying the CSF Portal to Docker Desktop on Windows Server, ensure you have the following:

1. Windows Server 2019 or newer with the latest updates installed
2. Administrator access to the Windows Server
3. Minimum system requirements:
   - 4 CPU cores
   - 8GB RAM
   - 50GB free disk space
4. Internet connection for downloading Docker and container images
5. Firewall configured to allow the required ports:
   - 8080/TCP for the web application
   - 1812/UDP and 1813/UDP for RADIUS server (if using this feature)

## Installing Docker Desktop on Windows Server

1. **Enable required Windows features**:

   Open PowerShell as Administrator and run:

   ```powershell
   Install-WindowsFeature -Name Containers
   Install-WindowsFeature -Name Hyper-V -IncludeManagementTools
   ```

2. **Restart the server**:

   ```powershell
   Restart-Computer -Force
   ```

3. **Download Docker Desktop for Windows**:

   Visit [Docker Desktop](https://www.docker.com/products/docker-desktop) and download the latest version for Windows.

4. **Install Docker Desktop**:

   Run the installer and follow the on-screen instructions. When prompted, select the option to use Windows containers.

5. **Verify installation**:

   Open PowerShell and run:

   ```powershell
   docker --version
   docker-compose --version
   ```

   You should see the version information for both Docker and Docker Compose.

## Preparing the Environment

1. **Create a directory for the application**:

   ```powershell
   New-Item -Path "C:\DockerApps\csfportal" -ItemType Directory
   cd C:\DockerApps\csfportal
   ```

2. **Clone the repository** (if you have Git installed):

   ```powershell
   git clone https://your-repository-url.git .
   ```

   Alternatively, copy the application files to this directory.

3. **Create a .env file for production**:

   Create a file named `.env.production` with the following content, replacing the placeholder values with your actual configuration:

   ```
   NODE_ENV=production
   PORT=8080
   MONGO_URI=mongodb://your-mongodb-server:27017/csfportal
   SESSION_SECRET=your_secure_session_secret

   # Google OAuth credentials
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   GOOGLE_CALLBACK_URL=https://your-domain.com/api/auth/google/callback

   # Google API credentials for Drive and Admin SDK
   GOOGLE_API_KEY=your_google_api_key

   # Allowed domains for Google login (comma-separated)
   ALLOWED_DOMAINS=yourchurch.org

   # GLPI API configuration (if used)
   GLPI_API_URL=https://your-glpi-instance.example.com
   GLPI_APP_TOKEN=your_glpi_app_token
   GLPI_USER_TOKEN=your_glpi_user_token

   # RADIUS server configuration (if used)
   RADIUS_PORT=1812
   RADIUS_AUTHORIZATION_PORT=1813
   RADIUS_ADDRESS=0.0.0.0
   RADIUS_AUTHENTICATION=StaticAuth
   RADIUS_SECRET=your_secure_radius_secret
   ```

## Building and Running the Docker Container

### Option 1: Building the Image Locally

1. **Build the Docker image**:

   ```powershell
   docker build -t csfportal:latest .
   ```

2. **Run the container**:

   ```powershell
   docker run -d --name csfportal `
     -p 8080:8080 `
     -p 1812:1812/udp `
     -p 1813:1813/udp `
     --env-file .env.production `
     -v csfportal-data:/app/data `
     --restart unless-stopped `
     csfportal:latest
   ```

### Option 2: Using Docker Compose

1. **Create a docker-compose.yml file**:

   Create a file named `docker-compose.yml` with the following content:

   ```yaml
   version: '3.8'

   services:
     csfportal:
       build: .
       container_name: csfportal
       ports:
         - "8080:8080"
         - "1812:1812/udp"
         - "1813:1813/udp"
       env_file:
         - .env.production
       volumes:
         - csfportal-data:/app/data
       restart: unless-stopped
       networks:
         - csfportal-network

     # If you want to run MongoDB in a container (optional)
     mongodb:
       image: mongo:4.4
       container_name: mongodb
       ports:
         - "27017:27017"
       volumes:
         - mongodb-data:/data/db
       restart: unless-stopped
       networks:
         - csfportal-network

   volumes:
     csfportal-data:
     mongodb-data:

   networks:
     csfportal-network:
       driver: bridge
   ```

2. **Start the services**:

   ```powershell
   docker-compose up -d
   ```

## Environment Variables Configuration

The CSF Portal application requires several environment variables to be configured properly. These are set in the `.env.production` file. Here's a detailed explanation of the key variables:

| Variable | Description | Example |
|----------|-------------|---------|
| NODE_ENV | Environment mode | production |
| PORT | Port the application runs on | 8080 |
| MONGO_URI | MongoDB connection string | mongodb://mongodb:27017/csfportal |
| SESSION_SECRET | Secret for session encryption | a-long-random-string |
| GOOGLE_CLIENT_ID | Google OAuth client ID | your-client-id.apps.googleusercontent.com |
| GOOGLE_CLIENT_SECRET | Google OAuth client secret | your-client-secret |
| GOOGLE_CALLBACK_URL | OAuth callback URL | https://your-domain.com/api/auth/google/callback |
| ALLOWED_DOMAINS | Domains allowed for login | yourchurch.org |
| RADIUS_SECRET | RADIUS server secret | your-radius-secret |

## Persistent Storage

To ensure data persistence across container restarts, the following volumes are used:

1. **csfportal-data**: For application data
2. **mongodb-data**: For MongoDB data (if using the containerized MongoDB)

You can manage these volumes using Docker commands:

```powershell
# List volumes
docker volume ls

# Inspect a volume
docker volume inspect csfportal-data

# Backup a volume
docker run --rm -v csfportal-data:/source -v C:\Backups:/backup alpine tar -czf /backup/csfportal-data-backup.tar.gz -C /source .
```

## Networking Configuration

### External Access

To make the application accessible from outside the server:

1. **Configure Windows Firewall**:

   ```powershell
   # Allow web application port
   New-NetFirewallRule -DisplayName "CSF Portal Web" -Direction Inbound -Action Allow -Protocol TCP -LocalPort 8080

   # Allow RADIUS ports (if used)
   New-NetFirewallRule -DisplayName "CSF Portal RADIUS Auth" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1812
   New-NetFirewallRule -DisplayName "CSF Portal RADIUS Accounting" -Direction Inbound -Action Allow -Protocol UDP -LocalPort 1813
   ```

2. **Set up a reverse proxy** (recommended):

   For production environments, it's recommended to use a reverse proxy like NGINX or IIS to handle HTTPS termination and forward requests to the Docker container.

### Using a Reverse Proxy

If you're using IIS as a reverse proxy:

1. Install the URL Rewrite module and Application Request Routing
2. Configure a new website in IIS
3. Set up URL rewriting rules to forward requests to http://localhost:8080

## Maintenance and Updates

### Updating the Application

To update the application to a new version:

1. **Pull the latest code**:

   ```powershell
   cd C:\DockerApps\csfportal
   git pull
   ```

2. **Rebuild and restart the container**:

   If using Docker directly:
   ```powershell
   docker build -t csfportal:latest .
   docker stop csfportal
   docker rm csfportal
   # Run the container again with the same command as in the initial setup
   ```

   If using Docker Compose:
   ```powershell
   docker-compose down
   docker-compose build
   docker-compose up -d
   ```

### Monitoring the Application

To monitor the application:

1. **View container logs**:

   ```powershell
   docker logs csfportal
   # For continuous monitoring
   docker logs -f csfportal
   ```

2. **Check container status**:

   ```powershell
   docker ps
   docker stats csfportal
   ```

## Troubleshooting

### Common Issues and Solutions

1. **Container fails to start**:
   - Check the logs: `docker logs csfportal`
   - Verify environment variables are correctly set
   - Ensure MongoDB is accessible

2. **Cannot connect to the application**:
   - Verify the container is running: `docker ps`
   - Check if the port is exposed: `netstat -an | findstr 8080`
   - Ensure firewall rules allow the traffic

3. **Database connection issues**:
   - Verify MongoDB connection string in `.env.production`
   - Check if MongoDB is running and accessible
   - Test connection: `docker exec csfportal node -e "const mongoose = require('mongoose'); mongoose.connect(process.env.MONGO_URI).then(() => console.log('Connected')).catch(err => console.error(err))"`

4. **Performance issues**:
   - Check container resource usage: `docker stats csfportal`
   - Consider increasing resources allocated to Docker Desktop

5. **Build errors with react-app-rewired**:
   - If you encounter an error like `../react-app-rewired/bin/index.js: not found` during the build process, this is due to a path resolution issue on Windows
   - The Dockerfile has been updated to use `npx react-app-rewired build` instead of `npm run build` to resolve this issue
   - If you're still experiencing this issue, ensure you're using the latest version of the Dockerfile

### Getting Help

If you encounter issues not covered in this guide:

1. Check the application logs for specific error messages
2. Consult the project documentation and issue tracker
3. Contact the development team for support

---

This deployment guide provides the basic steps to get the CSF Portal running on Docker Desktop on Windows Server. Adjust the configuration as needed for your specific environment and requirements.
