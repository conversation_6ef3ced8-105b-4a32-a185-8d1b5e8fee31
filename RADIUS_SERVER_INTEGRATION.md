# RADIUS Server Integration

This document describes the integration of a RADIUS server into the CSF Portal application.

## Overview

The RADIUS server is now properly integrated into the node process, allowing it to be started automatically when the application starts. This ensures that the RADIUS server endpoints are available for use by the application.

## Implementation Details

### 1. RADIUS Server Package

The `radius-server` package has been added to the project dependencies. This package provides a RADIUS server implementation that can be used to handle RADIUS authentication requests against various authentication backends, including LDAP (specifically Google LDAP Service), HTTP, IMAP, SMTP, and static credentials.

### 2. RADIUS Server Manager

A new file `server/integrations/radius/radiusServer.js` has been created to manage the RADIUS server. This file exports a singleton instance of the `RadiusServerManager` class, which provides methods for starting, stopping, and configuring the RADIUS server.

### 3. Server Initialization

The RADIUS server is now started automatically when the application starts. The server.js file has been updated to import the RADIUS server manager and start the server after the MongoDB connection is established.

### 4. RADIUS API Integration

The RADIUS API has been updated to use the local RADIUS server if it's available. The following methods in the `RadiusAPI` class have been updated:

- `isAuthenticated`: Now checks if the local RADIUS server is running and returns true if it is.
- `getServerStatus`: Now returns status information from the local RADIUS server if it's available.
- `getServerConfig`: Now returns configuration information from the local RADIUS server if it's available.
- `updateServerConfig`: Now updates the configuration of the local RADIUS server if it's available.

### 5. Enhanced API Endpoints

Several endpoints have been added to the RADIUS API:

- `/api/radius/is-local`: Returns information about the local RADIUS server status and Google Workspace authentication configuration
- `/api/radius/google-workspace-status`: Returns detailed Google Workspace authentication status (admin only)
- `/api/radius/test-connection`: Tests the RADIUS server connection and authentication

## Configuration

The RADIUS server can be configured using environment variables:

- `RADIUS_PORT`: The port on which the RADIUS server listens for authentication requests (default: 1812)
- `RADIUS_SECRET`: The shared secret used for RADIUS authentication (default: 'testing123')
- `RADIUS_ADDRESS`: The IP address to bind the RADIUS server to (default: '0.0.0.0'). If the specified address is not available, the server will automatically fall back to '0.0.0.0' (bind to all interfaces)
- `RADIUS_AUTHENTICATION`: The authentication method to use (default: 'GoogleWorkspaceAuth')
- `RADIUS_TLS_ENABLED`: Whether to enable TLS for the RADIUS server (default: false)
- `RADIUS_TLS_KEY_PATH`: The path to the TLS key file (required if TLS is enabled)
- `RADIUS_TLS_CERT_PATH`: The path to the TLS certificate file (required if TLS is enabled)

### Google Workspace Authentication

The RADIUS server now supports Google Workspace authentication via the Admin SDK. This requires:

**Environment Variables:**
- `GOOGLE_CLIENT_ID`: Google OAuth2 client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth2 client secret  
- `GOOGLE_REDIRECT_URI`: OAuth2 redirect URI
- `ALLOWED_DOMAINS`: Comma-separated list of allowed email domains

**OR Service Account (recommended for production):**
- `GOOGLE_ADMIN_SERVICE_ACCOUNT_EMAIL`: Service account email
- `GOOGLE_ADMIN_SERVICE_ACCOUNT_PRIVATE_KEY`: Service account private key
- `GOOGLE_ADMIN_IMPERSONATION_EMAIL`: Email to impersonate (admin user)

The authentication validates:
1. User exists in Google Workspace
2. User is not suspended or archived
3. User belongs to allowed domains
4. User has valid organizational unit

**Fallback Modes:**
- `StaticAuth`: Uses predefined username and password pairs (fallback if Google not configured)
- Other standard authentication backends are also supported

For more details on authentication configuration, refer to the [radius-server documentation](https://github.com/simllll/node-radius-server).

## Testing

A test script `test-radius-server.js` has been created to test the RADIUS server endpoints. This script makes requests to the `/api/radius/is-local` and `/api/radius/status` endpoints to check if the RADIUS server is running locally and to get its status.

## Note

For testing purposes, authentication has been temporarily disabled for the `/api/radius/status` and `/api/radius/is-local` endpoints. In a production environment, these endpoints should be protected with authentication.
