# Planning Center People Tab Updates

This document describes the updates made to the Planning Center People tab in the CSF Portal.

## Overview

The Planning Center People tab has been updated to include pagination and search functionality. This implementation ensures efficient loading and browsing of large datasets from the Planning Center API.

## Features Implemented

1. **Server-side Pagination**
   - People are loaded in pages (default: 25 per page)
   - Pagination controls allow navigation between pages
   - Total count of people is displayed

2. **Server-side Search**
   - Search input filters people by name
   - Search is performed on the server for better performance
   - Search results are also paginated

3. **UI Enhancements**
   - Added search input field at the top of the People tab
   - Added pagination controls at the bottom of the table
   - Improved display of total count

## Implementation Details

### Service Layer Updates

1. **New Method in planningCenterService.js**
   - Added `getPeopleDirectory` method that uses the `/api/planning-center/people-directory` endpoint
   - Method accepts pagination and search parameters
   - Returns a structured response with people array and pagination metadata

```javascript
/**
 * Get Planning Center people formatted for directory with pagination and search
 * @param {Object} params Query parameters including pagination (page, per_page) and search
 * @returns {Promise<Object>} Object containing people array and pagination metadata
 */
getPeopleDirectory: async (params = {}) => {
  try {
    const response = await axios.get('/api/planning-center/people-directory', { params });
    
    // The response includes people array and pagination metadata
    // Format: { people: [...], pagination: {...}, meta: {...}, links: {...} }
    return response.data;
  } catch (error) {
    console.error('Error fetching Planning Center people directory:', error);
    throw error;
  }
}
```

### Component Updates

1. **State Management in PlanningCenterPage.js**
   - Added state for search term: `const [searchTerm, setSearchTerm] = useState('')`
   - Added state for pagination:
     ```javascript
     const [peoplePagination, setPeoplePagination] = useState({
       currentPage: 1,
       totalPages: 1,
       totalCount: 0,
       perPage: 25
     });
     ```

2. **Event Handlers**
   - Added `handleSearchChange` function to update search term and reset pagination
   - Added `handlePageChange` function to update current page

3. **Data Fetching**
   - Updated the useEffect hook to use the new `getPeopleDirectory` method
   - Added dependencies to trigger refetching when pagination or search parameters change
   - Extracted and processed pagination data from the response

4. **UI Updates**
   - Added search input field with SearchIcon
   - Added pagination controls with first/last page buttons
   - Added display of total count

## How to Test

1. **Pagination**
   - Navigate to the Planning Center page
   - Select the People tab
   - If there are multiple pages, pagination controls should appear at the bottom
   - Click on page numbers, first/last buttons to navigate between pages
   - Verify that different people are loaded when changing pages

2. **Search**
   - Enter a search term in the search box
   - Verify that the results are filtered to match the search term
   - Verify that pagination works correctly with search results
   - Clear the search and verify that all people are displayed again

## Troubleshooting

If pagination or search is not working as expected:

1. Check browser console for errors
2. Verify that Planning Center API credentials are valid
3. Check network requests to see if pagination parameters are being sent correctly
4. Verify that the Planning Center API is responding with the expected data

## Future Improvements

Potential enhancements for the future:

1. Add more advanced search options (e.g., by email, phone)
2. Implement sorting options
3. Add ability to adjust items per page
4. Add filtering by categories or tags