# UniFi Network API Key Implementation

## Issue
The UniFi Network integration was previously updated to use username/password authentication with the `node-unifi` package. However, according to the current documentation, the UniFi Network API should use API key authentication instead.

## Solution
The UniFi Network API wrapper has been updated to use API key authentication instead of username/password authentication. This change involved:

1. Switching from the `node-unifi` package back to using axios with API key authentication
2. Updating all API methods to use the appropriate endpoints with fallbacks for different UniFi Network Controller versions
3. Updating the environment variables to use API key instead of username/password

## Implementation Details

### Authentication Changes
The authentication method has been changed from username/password-based to API key-based authentication:

1. The constructor now accepts an API key instead of a username
2. The `initialize` method no longer requires a password parameter
3. The `login` method has been removed, as API key authentication doesn't require a login step
4. API requests now include the API key in the `X-API-KEY` header

### API Method Changes
All API methods have been updated to use axios with API key authentication:

- `getDevices()` - Uses a series of fallback endpoints to get devices
- `getDeviceDetails(deviceId)` - Uses a series of fallback endpoints to get device details
- `getClients()` - Uses a series of fallback endpoints to get clients
- `getClientDetails(clientId)` - Uses a series of fallback endpoints to get client details
- `blockClient(clientId)` - Uses POST requests with appropriate command parameters
- `unblockClient(clientId)` - Uses POST requests with appropriate command parameters
- `getNetworkStats()` - Uses a series of fallback endpoints to get network statistics
- `getSiteInfo()` - Uses a series of fallback endpoints to get site information
- `getDeviceStatistics(deviceId)` - Uses a series of fallback endpoints to get device statistics

### Environment Variable Changes
The environment variables have been updated to use API key instead of username/password:

- Removed `UNIFI_NETWORK_USERNAME` and `UNIFI_NETWORK_PASSWORD`
- Added `UNIFI_NETWORK_API_KEY`
- Kept `UNIFI_NETWORK_HOST`, `UNIFI_NETWORK_PORT`, and `UNIFI_NETWORK_SITE` unchanged

### Controller Changes
The UniFi Network controller has been updated to use API key authentication:

- Updated the initialization to use API key instead of username/password
- Updated the `getConfig` method to return `apiKeyConfigured` instead of `usernameConfigured` and `passwordConfigured`

## Testing
The implementation has been tested with the following scripts:

1. `test-unifi-network-env-vars.js` - Tests that the configuration is correctly recognized with API key authentication
2. `test-unifi-network-fix.js` - Tests the API methods with API key authentication

## Notes
- The UniFi Network API integration now uses API key authentication instead of username/password authentication
- This update ensures compatibility with the current UniFi Network API documentation
- The implementation includes fallbacks for different UniFi Network Controller versions to ensure maximum compatibility

## References
- UniFi Network API Documentation: https://ubntwiki.com/products/software/unifi-controller/api
- Last updated: 2025-07-25