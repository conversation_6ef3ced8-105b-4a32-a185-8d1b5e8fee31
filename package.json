{"name": "csfportal", "version": "1.0.0", "description": "Intranet platform for church staff", "main": "server.js", "scripts": {"start": "node server.js", "server": "nodemon server.js", "client": "npm start --prefix client", "dev": "concurrently \"npm run server\" \"npm run client\"", "test": "jest", "test:watch": "jest --watch", "client-install": "npm install --prefix client", "build": "NPM_CONFIG_PRODUCTION=false npm install --prefix client && npm run build --prefix client", "heroku-postbuild": "npm run build", "setup-gmail-ticketing": "node setup-gmail-ticketing.js", "setup-gmail-simple": "node setup-gmail-simple.js"}, "private": true, "dependencies": {"@google-cloud/pubsub": "^4.11.0", "@panoptic-it-solutions/unifi-api-client": "^1.0.15", "axios": "1.11.0", "bcryptjs": "^2.4.3", "connect-mongo": "^4.6.0", "cors": "^2.8.5", "date-fns": "^2.30.0", "debug": "^4.4.1", "dotenv": "^16.0.3", "express": "^4.18.2", "express-session": "^1.17.3", "express-validator": "^6.14.2", "express-ws": "^5.0.2", "googleapis": "^110.0.0", "helmet": "^6.0.1", "imap": "^0.8.19", "jsonwebtoken": "^9.0.0", "mailparser": "^3.7.4", "mongoose": "^6.8.3", "multer": "2.0.2", "node-unifi": "^2.5.1", "nodemailer": "^7.0.5", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "qrcode": "^1.5.4", "radius-server": "^2.1.4", "react-markdown": "^10.1.0", "speakeasy": "^2.0.0", "unifi-protect": "^4.25.0", "uuid": "^9.0.1", "ws": "^8.18.3", "xml2js": "^0.6.2"}, "devDependencies": {"concurrently": "^7.6.0", "jest": "^29.5.0", "mongodb-memory-server": "^8.12.2", "nodemon": "^2.0.20", "supertest": "^6.3.3"}, "engines": {"node": "18.14.x"}}