# Lenel S2 NetBox API Quick Reference

## 🚀 Quick Start

```javascript
const LenelS2NetBoxAPI = require('./server/integrations/lenelS2NetBox/lenelS2NetBoxAPI');

// Initialize
const api = new LenelS2NetBoxAPI('netbox-host', 'username', 'password', 443);
await api.initialize();
```

## 📋 Core Methods (29 XML-based)

### Authentication & System
```javascript
await api.authenticate()                    // Login to NetBox
await api.getSystemStatus()                 // System status with connectivity test
await api.getSystemStatistics()             // Comprehensive system statistics
```

### Portals/Doors
```javascript
await api.getPortals()                      // Get all portals/doors
await api.getPortalDetails(portalId)        // Get specific portal details
await api.getPortalDetailsEnhanced(portalId) // Enhanced details with status/events
await api.getDoors()                        // Get all doors (filtered portals)
await api.getDoorStatus(doorId)             // Get current door status
await api.unlockDoor(doorId)                // Unlock door/portal
await api.lockDoor(doorId)                  // Lock door/portal
```

### Cardholders/Users
```javascript
await api.getCardholders()                  // Get all cardholders
await api.getCardholderDetails(id)          // Get specific cardholder
await api.getCardholderDetailsEnhanced(id)  // Enhanced details with activity
await api.createUser(userData)              // Create new cardholder
await api.updateUser(id, userData)          // Update cardholder
await api.deleteUser(id)                    // Delete cardholder
await api.getUserCredentials(id)            // Get user credentials
await api.getUserLogs(id, params)           // Get user access history
```

### Credentials
```javascript
await api.createUserCredential(userId, credData) // Create credential
await api.updateUserCredential(userId, credId, data) // Update credential
await api.deleteUserCredential(userId, credId) // Delete credential
```

### Access Levels
```javascript
await api.getAccessLevels()                 // Get all access levels
await api.getAccessLevelDetails(id)         // Get specific access level
await api.getAccessLevelDetailsEnhanced(id) // Enhanced with users/portals
await api.createAccessLevel(data)           // Create new access level
await api.updateAccessLevel(key, data)      // Update access level
await api.deleteAccessLevel(key)            // Delete access level
await api.assignAccessLevelToCardholder(userId, levelId) // Assign access
await api.removeAccessLevelFromCardholder(userId, levelId) // Remove access
```

### Access Groups
```javascript
await api.getAccessGroups()                 // Get all access groups
await api.getAccessGroupDetails(id)         // Get specific access group
```

### Time Specifications/Schedules
```javascript
await api.getDoorSchedules()                // Get all time specs
await api.getDoorScheduleDetails(id)        // Get specific time spec
await api.createTimeSpec(data)              // Create new time spec
await api.updateTimeSpec(key, data)         // Update time spec
await api.deleteTimeSpec(key)               // Delete time spec
```

### Events & Alarms
```javascript
await api.getEvents(params)                 // Get events with filters
await api.getAlarms(params)                 // Get alarms with filters
await api.acknowledgeAlarm(alarmId)         // Acknowledge alarm
```

### Badges
```javascript
await api.getBadges()                       // Get badge print requests
await api.getBadgeDetails(badgeId)          // Get badge details
```

## 🔍 Enhanced Search Methods (4 new)

### Advanced Search with Pagination
```javascript
// Search cardholders with filters
const results = await api.searchCardholders({
  status: 'Active',
  accessLevels: ['Employee', 'Contractor'],
  activationDateFrom: '2024-01-01',
  limit: 25,
  offset: 0
});

// Search events with date range
const events = await api.searchEvents({
  startDate: '2024-01-01 00:00:00',
  endDate: '2024-12-31 23:59:59',
  severity: 'high',
  limit: 50
});

// Search portals by type
const portals = await api.searchPortals({
  type: 'Portal',
  location: 'Building A',
  limit: 20
});

// Search active alarms
const alarms = await api.searchAlarms({
  active: true,
  ackPending: true,
  limit: 10
});
```

## 🛠️ Bulk Operations (2 new)

```javascript
// Bulk assign access levels
const result = await api.bulkAssignAccessLevels(
  ['user1', 'user2', 'user3'],           // User IDs
  ['Employee', 'Building Access']        // Access Level IDs
);

// Bulk remove access levels
const result = await api.bulkRemoveAccessLevels(
  ['user1', 'user2'],                    // User IDs
  ['Contractor']                         // Access Level IDs
);
```

## 🔧 Utility Functions (3 new)

```javascript
// Check credential status
const status = api.getCredentialStatusDescription(credential);
const isExpired = api.isCredentialExpired(credential);
const daysLeft = api.getDaysUntilExpiration(credential);
```

## 📊 Common Patterns

### Search with Pagination
```javascript
let offset = 0;
const limit = 50;
let hasMore = true;

while (hasMore) {
  const results = await api.searchCardholders({
    status: 'Active',
    limit: limit,
    offset: offset
  });
  
  // Process results.results
  console.log(`Processing ${results.results.length} cardholders`);
  
  hasMore = results.pagination.hasMore;
  offset += limit;
}
```

### Enhanced Details Workflow
```javascript
// Get basic list
const cardholders = await api.getCardholders();

// Get enhanced details for first cardholder
if (cardholders.length > 0) {
  const enhanced = await api.getCardholderDetailsEnhanced(cardholders[0].id);
  
  console.log('Access Levels:', enhanced.accessLevels.length);
  console.log('Recent Activity:', enhanced.recentActivity.length);
  console.log('Active Credentials:', enhanced.statistics.activeCredentials);
}
```

### CRUD Workflow
```javascript
// Create access level
const newLevel = await api.createAccessLevel({
  name: 'Temporary Access',
  description: 'Short-term access for visitors'
});

// Update it
await api.updateAccessLevel(newLevel.accessLevelKey, {
  description: 'Updated description'
});

// Assign to users
await api.bulkAssignAccessLevels(
  ['user1', 'user2'], 
  [newLevel.accessLevelKey]
);

// Clean up
await api.deleteAccessLevel(newLevel.accessLevelKey);
```

## 🚨 Error Handling

```javascript
try {
  const result = await api.searchCardholders({ status: 'Active' });
  console.log('Success:', result.results.length);
} catch (error) {
  console.error('API Error:', error.message);
  
  // Check for specific error types
  if (error.message.includes('authentication')) {
    // Handle auth error
    await api.authenticate();
  } else if (error.message.includes('not found')) {
    // Handle not found error
  }
}
```

## 📈 Performance Tips

1. **Use pagination** for large datasets
2. **Cache access levels** and portals (they change infrequently)
3. **Use bulk operations** for multiple updates
4. **Filter at API level** when possible to reduce data transfer
5. **Use enhanced details** only when needed (they make multiple API calls)

## 🧪 Testing

```bash
# Test basic XML conversion
node test_lenel_api.js

# Test enhanced features
node test_lenel_enhanced_features.js
```

## 📚 Documentation

- **LENEL_API_FIXES_SUMMARY.md** - Complete XML conversion details
- **LENEL_ENHANCED_FEATURES.md** - Detailed enhanced features documentation
- **NetBox_NBAPI2-3.txt** - Official NetBox API documentation
