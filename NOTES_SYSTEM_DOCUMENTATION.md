# Notes System Documentation

## Overview

The Notes System is a feature that allows users to create, manage, and organize personal notes and to-dos within the CSF Portal. Notes are tied to individual user accounts, ensuring privacy and personalization.

## Features

- **Personal Notes**: Create and manage text notes
- **To-Do Lists**: Create to-do items with completion tracking
- **Pinning**: Pin important notes to the top of the list
- **Color Coding**: Customize note colors for visual organization
- **Dashboard Widget**: Access notes directly from the dashboard

## User Guide

### Adding the Notes Widget to Your Dashboard

1. Navigate to your Dashboard
2. Enter Edit Mode by clicking the "Edit" button
3. Click the "+" button to add a new widget
4. Select "Notes" from the widget list
5. Click "Add" to add the widget to your dashboard
6. Save your dashboard changes

### Creating a New Note

1. From the Notes widget, click the "Add Note" button
2. Enter a title for your note (required)
3. Add content (optional)
4. Select the note type:
   - **Note**: Regular text note
   - **To-Do**: Task with completion tracking
5. Choose a color for your note (optional)
6. Check "Pin note" to keep it at the top of the list (optional)
7. Click "Save" to create the note

### Managing Notes

- **View Notes**: All your notes appear in the Notes widget
- **Edit Note**: Click the edit icon on a note to modify it
- **Delete Note**: Click the delete icon to remove a note
- **Pin/Unpin**: Click the pin icon to toggle pinned status
- **Complete To-Do**: Click the circle icon on a to-do to mark it as complete

### Widget Settings

The Notes widget can be configured with the following settings:

- **Number of notes to display**: Control how many notes are visible at once
- **Filter by type**: Show all notes, only regular notes, or only to-dos
- **Show completed to-dos**: Toggle visibility of completed to-do items

## Technical Documentation

### Database Model

The Notes system uses a MongoDB model with the following schema:

```javascript
const NoteSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  content: {
    type: String,
    trim: true
  },
  type: {
    type: String,
    enum: ['note', 'todo'],
    default: 'note'
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  completed: {
    type: Boolean,
    default: false
  },
  color: {
    type: String,
    default: '#ffffff'
  },
  pinned: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    trim: true
  }]
}, {
  timestamps: true
});
```

### API Endpoints

The Notes system provides the following API endpoints:

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET | /api/notes | Get all notes for the current user | Required |
| GET | /api/notes/:id | Get a specific note by ID | Required |
| POST | /api/notes | Create a new note | Required |
| PUT | /api/notes/:id | Update an existing note | Required |
| DELETE | /api/notes/:id | Delete a note | Required |
| PUT | /api/notes/:id/toggle-completion | Toggle completion status of a to-do | Required |
| PUT | /api/notes/:id/toggle-pinned | Toggle pinned status of a note | Required |

### Client-Side Components

The Notes system includes the following client-side components:

- **NotesWidget**: Dashboard widget for displaying and managing notes
- **notesService**: Service for communicating with the Notes API

### Security

The Notes system implements the following security measures:

- **Authentication**: All API endpoints require user authentication
- **Authorization**: Users can only access and modify their own notes
- **Validation**: Input validation is performed on all API requests
- **Error Handling**: Proper error handling and status codes are implemented

## Future Enhancements

Potential future enhancements for the Notes system:

- **Sharing**: Allow users to share notes with other users
- **Categories**: Add support for categorizing notes
- **Rich Text**: Add support for rich text formatting
- **Attachments**: Allow file attachments to notes
- **Reminders**: Add support for note reminders and notifications
- **Search**: Add search functionality for notes
- **Export/Import**: Add support for exporting and importing notes

## Troubleshooting

Common issues and solutions:

- **Notes not appearing**: Ensure you're logged in and have created notes
- **Cannot create notes**: Verify that you've entered a title (required field)
- **Widget not saving settings**: Ensure you've saved dashboard changes after modifying widget settings