# Google Forms Embedding Feature

This document describes the implementation of the Google Forms embedding feature, which allows users to open Google Forms directly from the portal without leaving it, embed the forms as if they are native to the portal, and add forms to their shortcuts dashboard widget.

## Feature Overview

The Google Forms embedding feature provides the following capabilities:

1. **Embedded Forms**: Users can view and interact with Google Forms directly within the portal, without being redirected to a new tab or window.
2. **Add to Shortcuts**: Users can add Google Forms to their shortcuts dashboard widget for quick access.
3. **Native Experience**: Forms are displayed with a consistent UI that matches the portal's design.

## User Guide

### Viewing Embedded Forms

1. Navigate to the Google Forms page in the portal.
2. Select a form from the list on the left side.
3. Click the "Embed Form" button to view the form embedded in the portal.
4. Interact with the form directly within the portal.
5. Use the "Show Details" button to return to the form details view.

### Adding Forms to Shortcuts

1. Navigate to the Google Forms page in the portal.
2. Select a form from the list on the left side.
3. Click the "Add to Shortcuts" button to add the form to your shortcuts.
4. A notification will appear confirming that the form has been added to your shortcuts.
5. The form will now appear in your shortcuts dashboard widget.

### Accessing Forms from Shortcuts

1. Forms added to shortcuts will appear in your shortcuts dashboard widget.
2. Google Forms shortcuts are indicated by a document icon.
3. Click on a Google Form shortcut to open the form in an embedded dialog within the portal.
4. Interact with the form directly within the dialog.
5. Click the close button (X) in the top-right corner to close the dialog when you're done.

## Technical Implementation

The feature was implemented with the following components:

1. **GoogleFormsViewer Component**: A reusable component that embeds Google Forms within an iframe.
2. **GoogleFormsPage Updates**: The Google Forms page was updated to use the GoogleFormsViewer component and add functionality to add forms to shortcuts.
3. **ShortcutsWidget Updates**: The shortcuts widget was updated to detect Google Forms URLs and open them in an embedded dialog instead of redirecting to a new tab.

### GoogleFormsViewer Component

The GoogleFormsViewer component is a reusable component that:

- Takes a form URL or ID and embeds it within an iframe
- Provides controls for fullscreen mode, opening in a new tab, and adding to shortcuts
- Handles loading states and errors
- Provides a responsive layout that adjusts based on the fullscreen state and props

### GoogleFormsPage Updates

The Google Forms page was updated to:

- Add state for tracking the embedded view
- Add functionality to add forms to shortcuts
- Add UI buttons for embedding forms and adding to shortcuts
- Show the embedded form when the "Embed Form" button is clicked
- Add a snackbar component for notifications

### ShortcutsWidget Updates

The shortcuts widget was updated to:

- Detect Google Forms URLs
- Open Google Forms in an embedded dialog instead of redirecting to a new tab
- Use a different icon for Google Forms shortcuts
- Track shortcut clicks

## Benefits

This feature provides several benefits:

1. **Improved User Experience**: Users can interact with Google Forms without leaving the portal, providing a more seamless experience.
2. **Increased Efficiency**: Users can quickly access frequently used forms from their shortcuts dashboard widget.
3. **Consistent UI**: Forms are displayed with a consistent UI that matches the portal's design.
4. **Reduced Context Switching**: Users don't need to switch between different tabs or windows to access forms.

## Future Enhancements

Potential future enhancements to this feature include:

1. **Form Creation**: Allow users to create new Google Forms directly within the portal.
2. **Form Editing**: Allow users to edit existing Google Forms directly within the portal.
3. **Response Visualization**: Provide visualizations of form responses directly within the portal.
4. **Form Categories**: Allow users to categorize forms for better organization.
5. **Form Sharing**: Allow users to share forms with other portal users.