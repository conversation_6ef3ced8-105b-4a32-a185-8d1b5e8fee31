# Rain Bird Frontend Implementation

This document describes the implementation of the frontend pages for the Rain Bird irrigation system integration.

## Overview

The Rain Bird integration allows users to control their Rain Bird irrigation system through the CSF Staff Portal. The frontend implementation consists of two main pages:

1. **Rain Bird Dashboard** - Main interface for controlling the irrigation system
2. **Rain Bird Setup** - Configuration page for setting up the Rain Bird integration

## Implementation Details

### Files Created

1. `client/src/pages/RainBird/RainBirdPage.js` - Main dashboard for controlling the Rain Bird irrigation system
2. `client/src/pages/RainBird/RainBirdSetup.js` - Setup page for configuring the Rain Bird integration
3. Updated `client/src/App.js` to add routes for the Rain Bird pages

### Rain Bird Dashboard Features

The Rain Bird dashboard provides the following features:

- **System Status** - Displays the current status of the irrigation system, including:
  - Irrigation active status
  - Rain sensor status
  - Rain delay status
  - Stop all zones button (when irrigation is active)

- **Zones Management** - Allows users to:
  - View all available irrigation zones
  - Start watering a zone with a specified duration
  - Stop watering a zone
  - See which zones are currently active

- **Programs Management** - Allows users to:
  - View all available irrigation programs (schedules)
  - Start a program
  - Stop a program

- **Settings** - Provides access to:
  - Rain delay configuration
  - System information

### Rain Bird Setup Features

The Rain Bird setup page provides the following features:

- **One-Click Setup** - Automatically configures the Rain Bird integration with default settings
- **Manual Configuration** - Allows users to manually configure:
  - Host (IP address or hostname)
  - Port
  - Password
  - Local network toggle
  - Cloud host (for cloud control)
  - Controller MAC address (for cloud control)
- **Configuration Status** - Shows the current configuration status

## Routes

The Rain Bird pages can be accessed at the following routes:

- `/rain-bird` - Main dashboard for controlling the Rain Bird irrigation system
- `/rain-bird/setup` - Setup page for configuring the Rain Bird integration (admin access required)

## Usage Instructions

### Setting Up Rain Bird Integration

1. Navigate to `/rain-bird/setup`
2. Choose one of the following setup methods:
   - **One-Click Setup**: Click the "Set Up with One Click" button to automatically configure with default settings
   - **Manual Configuration**: Fill in the required fields and click "Save Configuration"
3. Once configured, you'll see a success message and the configuration status will be updated

### Controlling Rain Bird Irrigation System

1. Navigate to `/rain-bird`
2. The dashboard will display the system status at the top
3. Use the tabs to navigate between:
   - **Zones**: Start or stop watering specific zones
   - **Programs**: Run or stop irrigation programs
   - **Settings**: Configure rain delay and view system information

#### Starting a Zone

1. Go to the "Zones" tab
2. For the zone you want to water:
   - Set the duration using the slider
   - Click the "Start" button
3. The zone will start watering and its status will update to "Running"

#### Stopping a Zone

1. Go to the "Zones" tab
2. For the active zone, click the "Stop" button
3. The zone will stop watering and its status will update to "Idle"

#### Running a Program

1. Go to the "Programs" tab
2. For the program you want to run, click the "Run Now" button
3. The program will start and the system status will update

#### Setting a Rain Delay

1. Go to the "Settings" tab
2. In the "Rain Delay" card, select the desired duration from the dropdown
3. The rain delay will be set and the system status will update

## Dependencies

The Rain Bird frontend implementation relies on:

- The Rain Bird API backend implementation (`server/integrations/rainBird/rainBirdAPI.js`)
- The Rain Bird API routes (`routes/api/rainBird.js`)
- The Rain Bird controller (`server/controllers/rainBirdController.js`)
- The Rain Bird service (`client/src/services/rainBirdService.js`)

## Conclusion

The Rain Bird frontend implementation provides a user-friendly interface for controlling a Rain Bird irrigation system through the CSF Staff Portal. Users can easily manage zones, programs, and system settings, while administrators can configure the integration to connect to their specific Rain Bird controller.