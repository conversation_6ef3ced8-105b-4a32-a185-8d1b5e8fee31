# WiiM Refresh Optimization

## Issue Description
The WiiM page and dashboard widget auto-refreshes, but it was calling both the playback endpoint and the config endpoint every time. Since the config data doesn't change frequently, it only needs to call the playback endpoint for refreshes and doesn't need to call the config endpoint every time.

## Changes Made

### WiimWidget.js
1. Added a new state variable `configStatus` to store the configuration data:
   ```javascript
   const [configStatus, setConfigStatus] = useState(null);
   ```

2. Modified the initial data fetch to store the config in this state variable:
   ```javascript
   // Get configuration status (only once during initialization)
   const config = await wiimService.getConfig();
   setConfigStatus(config);
   ```

3. Modified the refresh interval to use the stored `configStatus` instead of fetching it again:
   ```javascript
   // Only fetch playback data if we have a valid config
   if (configStatus) {
     const playbackData = await wiimService.getPlaybackStatus();
     // ...
   }
   ```

### WiimPage.js
No changes were needed for the WiimPage.js component as it already had the correct behavior (only calling getConfig during initialization).

## Rationale
1. **Performance Improvement**: Reduces unnecessary API calls to the config endpoint, which improves performance and reduces server load.
2. **Data Consistency**: The config data doesn't change frequently, so there's no need to fetch it on every refresh.
3. **Efficiency**: Only the playback and queue data, which change frequently, are fetched during the refresh interval.

## Edge Cases and Considerations
1. **Configuration Changes**: If the WiiM configuration is changed externally (e.g., through environment variables or the database), the widget won't reflect these changes until it's reloaded. This is acceptable since configuration changes are rare and typically require a page reload anyway.
2. **Error Handling**: The existing error handling in the widget is maintained, so any issues with fetching playback data will be properly handled.
3. **Initial Load**: The config endpoint is still called during the initial load to ensure the widget has the latest configuration data when it first renders.

## Testing
The changes have been tested to ensure that:
1. The WiiM widget still functions correctly with this optimization
2. The config endpoint is only called once during initialization
3. Only the playback and queue endpoints are called during the refresh interval

## Conclusion
This optimization reduces unnecessary API calls while maintaining the functionality of the WiiM widget. It improves performance and reduces server load without affecting the user experience.