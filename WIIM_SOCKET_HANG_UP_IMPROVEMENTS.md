# WiiM Socket Hang Up Error - Comprehensive Improvements

## Overview
This document outlines the comprehensive improvements made to address "socket hang up" errors in the WiiM integration. The original error was:

```json
{
    "message": "Error fetching WiiM playlist",
    "error": "socket hang up"
}
```

## Root Cause Analysis
Socket hang up errors occur when:
1. The server closes the connection before a response is received
2. Network timeouts or instability
3. Device overload or unresponsiveness
4. Insufficient retry logic and error handling

## Comprehensive Improvements Implemented

### 1. Enhanced HTTP Agent Configuration

#### WiiM Device HTTP Agent
```javascript
const httpAgent = new http.Agent({
  keepAlive: true,
  keepAliveMsecs: 30000,     // Keep connections alive for 30 seconds
  maxSockets: 5,             // Limit concurrent connections
  maxFreeSockets: 2,         // Keep connections in pool
  timeout: 10000,            // Socket timeout
  freeSocketTimeout: 15000   // Free socket timeout
});
```

#### Spotify API HTTPS Agent
```javascript
const httpsAgent = new https.Agent({
  keepAlive: true,
  keepAliveMsecs: 30000,     // Keep connections alive for 30 seconds
  maxSockets: 10,            // Allow more concurrent connections
  maxFreeSockets: 5,         // Keep more connections in pool
  timeout: 15000,            // Socket timeout
  freeSocketTimeout: 20000   // Free socket timeout
});
```

### 2. Intelligent Retry Logic with Exponential Backoff

#### Retry Strategy
- **Maximum retries**: 3 attempts
- **Exponential backoff**: 1s, 2s, 4s delays (capped at 5s)
- **Retry conditions**: Network errors, timeouts, server errors (5xx)
- **No retry for**: Client errors (4xx)

#### Retryable Error Codes
```javascript
const retryableErrors = [
  'ECONNRESET', 'ECONNREFUSED', 'ENOTFOUND',
  'ENETDOWN', 'ENETUNREACH', 'EHOSTDOWN',
  'EHOSTUNREACH', 'EPIPE', 'ECONNABORTED',
  'ETIMEDOUT', 'socket hang up'
];
```

### 3. Circuit Breaker Pattern

#### Circuit Breaker Logic
- **Failure threshold**: 3 failures within 5 minutes
- **Open circuit duration**: 5 minutes
- **Automatic reset**: After 5 minutes of no activity
- **Endpoints monitored**: `getPlaylists`, `getSpotifyPlaylists`

#### Circuit Breaker States
- **Closed**: Normal operation
- **Open**: Endpoint blocked due to repeated failures
- **Half-Open**: Testing if endpoint has recovered

### 4. Enhanced Timeout Configuration

#### Timeout Settings
- **WiiM device requests**: 10-20 seconds (depending on operation)
- **Spotify API requests**: 15-25 seconds
- **Playlist operations**: Extended timeouts (20-25 seconds)

### 5. Comprehensive Error Handling

#### Enhanced Error Messages
```javascript
if (error.message?.toLowerCase().includes('socket hang up')) {
  errorMessage += ' (Network connection was unexpectedly closed. This may be due to device timeout or network issues.)';
} else if (error.code === 'ECONNREFUSED') {
  errorMessage += ' (Connection refused. Please check if the WiiM device is powered on and accessible.)';
} else if (error.code === 'ETIMEDOUT') {
  errorMessage += ' (Request timed out. The WiiM device may be busy or unresponsive.)';
}
```

#### Error Context
- Original error preservation
- Circuit breaker state information
- Request configuration details
- Endpoint-specific error tracking

### 6. Request/Response Interceptors

#### Request Interceptor
- Logs all outgoing requests
- Adds retry count tracking
- Enhances request headers

#### Response Interceptor
- Logs response status and timing
- Implements retry logic
- Handles exponential backoff

### 7. Health Check System

#### Health Check Endpoint: `/api/wiim/health`
```javascript
{
  "status": "healthy|degraded|unhealthy",
  "timestamp": "2025-07-22T...",
  "host": "*************",
  "port": 80,
  "circuitBreakers": {
    "getPlaylists": { "failures": 0, "isOpen": false },
    "getSpotifyPlaylists": { "failures": 0, "isOpen": false }
  },
  "tests": {
    "connectivity": { "status": "pass", "responseTime": "150ms" },
    "playlists": { "status": "pass", "responseTime": "300ms" }
  }
}
```

### 8. Improved Request Headers

#### WiiM Device Headers
```javascript
headers: {
  'Content-Type': 'application/json',
  'Connection': 'keep-alive',
  'User-Agent': 'CSFPortal-WiiM-Client/1.0',
  'Accept': 'application/json, text/plain, */*',
  'Cache-Control': 'no-cache',
  'Pragma': 'no-cache'
}
```

#### Spotify API Headers
```javascript
headers: {
  'Content-Type': 'application/json',
  'Connection': 'keep-alive',
  'User-Agent': 'CSFPortal-Spotify-Client/1.0',
  'Authorization': `Bearer ${accessToken}`,
  'Accept': 'application/json'
}
```

## Usage Instructions

### 1. Monitor Health Status
```javascript
// Check WiiM integration health
const health = await wiimService.getHealthStatus();
console.log('WiiM Health:', health.status);
```

### 2. Handle Circuit Breaker Errors
```javascript
try {
  const playlists = await wiimService.getPlaylists();
} catch (error) {
  if (error.isCircuitBreakerError) {
    console.log('Circuit breaker is open, trying again later...');
    // Wait and retry or use alternative approach
  }
}
```

### 3. Error Monitoring
```javascript
// Enhanced error information is available
catch (error) {
  console.log('Error details:', {
    message: error.message,
    isWiimError: error.isWiimError,
    circuitBreakerFailures: error.circuitBreakerFailures,
    endpoint: error.endpoint
  });
}
```

## Testing the Improvements

### 1. Test Basic Connectivity
```bash
curl http://localhost:3000/api/wiim/health
```

### 2. Test Playlist Fetching
```bash
curl http://localhost:3000/api/wiim/playlists
```

### 3. Monitor Circuit Breaker
- Make multiple failing requests to trigger circuit breaker
- Verify circuit breaker opens after 3 failures
- Confirm automatic reset after 5 minutes

## Expected Outcomes

1. **Reduced socket hang up errors** through better connection management
2. **Improved reliability** with retry logic and circuit breakers
3. **Better error visibility** with enhanced logging and error messages
4. **Graceful degradation** when endpoints are failing
5. **Proactive monitoring** with health check endpoints

## Monitoring and Maintenance

### Key Metrics to Monitor
- Circuit breaker failure counts
- Response times for different endpoints
- Error rates and types
- Health check status trends

### Maintenance Tasks
- Review circuit breaker logs weekly
- Adjust timeout values based on network conditions
- Monitor for new error patterns
- Update retry strategies as needed

## Conclusion

These comprehensive improvements should significantly reduce "socket hang up" errors and provide a more robust, reliable WiiM integration. The combination of better connection management, intelligent retry logic, circuit breakers, and enhanced monitoring creates a resilient system that can handle network issues gracefully.
