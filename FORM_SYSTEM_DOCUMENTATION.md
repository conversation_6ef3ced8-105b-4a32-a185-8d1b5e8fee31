# Form Management and Form Building System

This document provides an overview of the form management and form building system implemented in the CSF Portal application.

## Overview

The form system allows users to create, manage, and submit forms for various use cases. It supports a wide range of field types, conditional logic, styling customizations, and integrations with other systems.

## Features

### Form Creation and Management

- **Form Builder Interface**: A comprehensive interface for creating and editing forms
- **Form Preview**: Real-time preview of how the form will appear to users
- **Form Settings**: Configuration options for form behavior, styling, and permissions
- **Form Status**: Draft, published, and archived states for forms

### Field Types

The system supports a wide range of field types:

- **Basic Fields**: Text, Textarea, Number, Email, Phone, Password
- **Date & Time**: Date, Time, DateTime
- **Choice Fields**: Select, Multiselect, Radio, Checkbox
- **File Upload**: File, Image
- **Complex Fields**: Name, Address, Rating, Slider
- **Layout Elements**: HTML content, Divider, Page Break, Hidden Field

### Form Structure

- **Field Groups**: Organize fields into logical groups
- **Multistep Forms**: Create forms with multiple steps/pages
- **Conditional Logic**: Show/hide fields or groups based on conditions
- **Field Validation**: Required fields, pattern validation, min/max length

### Styling and Customization

- **Themes**: Choose from predefined themes (default, light, dark, colorful, minimal)
- **Colors**: Customize primary and background colors
- **Fonts**: Choose custom font families
- **Custom CSS**: Add custom CSS for advanced styling
- **Field Width**: Configure field width (full, half, third, quarter)

### Permissions and Access Control

- **Access Levels**: Public, authenticated, specific roles, specific users, specific groups
- **Management Permissions**: Control who can manage forms
- **Submission Viewing**: Control who can view form submissions

### Form Submission

- **Validation**: Client-side and server-side validation
- **File Uploads**: Support for file uploads with size and type restrictions
- **Success Messages**: Customizable success messages
- **Redirects**: Optional redirect after submission

### Integrations

- **Ticket Creation**: Create tickets from form submissions
- **Task Creation**: Create tasks from form submissions
- **Custom Notifications**: Send email notifications based on form submissions
- **Custom Mapping**: Map form fields to ticket/task fields

## Technical Implementation

### Backend Components

1. **Models**:
   - `Form`: Stores form configuration, fields, and settings
   - `FormSubmission`: Stores form submissions and uploaded files

2. **Controllers**:
   - `formController`: Handles form CRUD operations
   - `formSubmissionController`: Handles form submission operations

3. **Routes**:
   - `/api/forms`: Authenticated form routes
   - `/api/public-forms`: Public form routes

### Frontend Components

1. **Pages**:
   - `FormBuilderPage`: Interface for creating and editing forms
   - `FormRenderer`: Page for displaying and submitting forms

2. **Components**:
   - `FieldTypeSelector`: Component for selecting field types
   - `FieldEditor`: Component for editing field properties
   - `FieldGroupEditor`: Component for editing field group properties
   - `ConditionBuilder`: Component for building conditional logic
   - `FormPreview`: Component for previewing forms
   - `FormSettings`: Component for configuring form settings
   - `FormContainer`: Component for rendering forms
   - `FieldRenderer`: Component for rendering individual fields

3. **Services**:
   - `formService`: Service for interacting with the form API

## Usage Examples

### Creating a Form

1. Navigate to the Forms section
2. Click "Create Form"
3. Add a title and description
4. Add field groups and fields
5. Configure field properties and validation
6. Set up conditional logic if needed
7. Configure form settings (permissions, styling, integrations)
8. Save the form as draft or publish it

### Submitting a Form

1. Navigate to the form URL
2. Fill out the form fields
3. Upload any required files
4. Submit the form
5. View success message or be redirected

### Viewing Form Submissions

1. Navigate to the Forms section
2. Select a form
3. Click "View Submissions"
4. Browse and filter submissions
5. View submission details and downloaded uploaded files

## Permissions

The form system integrates with the existing role-based permission system:

- `forms_create`: Permission to create new forms
- `forms_manage`: Permission to manage all forms
- `forms_view_submissions`: Permission to view form submissions

Additionally, each form has its own permission settings that control who can access, manage, and view submissions for that specific form.

## Public Forms

Forms can be made public, allowing users to submit them without logging in. Public forms are accessible via a unique URL that includes the form's slug.

## Conditional Logic

The conditional logic system allows fields and groups to be shown or hidden based on the values of other fields. Conditions can be combined using AND/OR logic and support various operators:

- Equals / Not Equals
- Contains / Does Not Contain
- Greater Than / Less Than
- Starts With / Ends With
- Is Empty / Is Not Empty

## File Uploads

The file upload system supports:

- Multiple file uploads
- File type restrictions
- File size limits
- Secure storage of uploaded files

## Integration with Other Systems

Forms can be integrated with other systems in the portal:

- **Ticketing System**: Create tickets from form submissions
- **Task System**: Create tasks from form submissions
- **Notification System**: Send email notifications based on form submissions

## Future Enhancements

Potential future enhancements for the form system:

- Form templates for common use cases
- Form analytics and reporting
- Advanced form logic with calculations
- Integration with additional external systems
- Form versioning and revision history
- Form duplication
- Form import/export

## Conclusion

The form management and form building system provides a comprehensive solution for creating and managing forms within the CSF Portal application. It offers a wide range of features and customization options to meet various use cases and requirements.