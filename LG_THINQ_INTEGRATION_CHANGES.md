# LG ThinQ Integration Changes

## Overview

This document outlines the changes made to update the LG ThinQ integration to use the API key authentication method as specified in the `lg-openapi.json` documentation.

## Changes Made

1. **API Integration File**
   - Updated the constructor to use API key, region, and country parameters instead of username/password
   - Added logic to set the base URL based on the region (Asia/Pacific, America, Europe/Middle East/Africa)
   - Updated the authentication methods to use API key authentication
   - Added a unique message ID (using uuid) to each API request as required by the OpenAPI spec
   - Added a generic `controlDevice` method for all device control operations
   - Updated specific control methods to use the generic `controlDevice` method

2. **Model File**
   - Updated the schema to store API key, region, and country instead of username/password
   - Added enum values for the region field to validate input

3. **Controller File**
   - Updated initialization and configuration methods to use the new API key authentication
   - Updated the `saveConfig` method to accept API key, region, and country parameters
   - Updated the `oneClickSetup` method to use environment variables for API key, region, and country
   - Updated the `getConfig` method to return region and country information
   - Modified the `getLatestConfig` function to prioritize environment variables over database configuration
   - All controller methods now use environment variables for authentication when available

4. **Environment Variables**
   - Added the following environment variables to `.env` and `.env.production.sample`:
     - `LG_THINQ_API_KEY`: The API key for authentication
     - `LG_THINQ_REGION`: The region for the API (default: 'america')
     - `LG_THINQ_COUNTRY`: The country code (default: 'US')

## Testing

A test script (`test-lg-thinq-api.js`) has been created to verify the LG ThinQ API integration. The script:

1. Loads environment variables from the `.env` file
2. Creates a new instance of the LG ThinQ API with the API key, region, and country
3. Initializes the API
4. Gets route information
5. Gets devices
6. If devices are found, gets details and status for the first device

## Production Deployment Instructions

To deploy the updated LG ThinQ integration to production:

1. **Obtain a valid API key**
   - Visit https://connect-pat.lgthinq.com
   - Log in with your ThinQ account
   - Select "ADD NEW TOKEN"
   - Enter a token name
   - Select the features you want
   - Select "CREATE TOKEN"
   - Copy the generated token

2. **Update Environment Variables (Recommended Method)**
   - Set the `LG_THINQ_API_KEY` environment variable to the token generated above
   - Set the `LG_THINQ_REGION` environment variable to the appropriate region:
     - 'asia' or 'pacific' for South Asia, East Asia and Pacific
     - 'america' for America
     - 'europe', 'middle_east', or 'africa' for Europe, Middle East, Africa
   - Set the `LG_THINQ_COUNTRY` environment variable to your country code (e.g., 'US')
   - **Important**: The integration now prioritizes environment variables over database configuration for authentication

3. **Test the Integration**
   - Run the test script: `node test-lg-thinq-api.js`
   - Verify that the API can connect and retrieve data

4. **Database Configuration (Not Recommended)**
   - Using database configuration for authentication is no longer recommended
   - The integration will fall back to database configuration only if environment variables are not set
   - If you must use database configuration, you can use the one-click setup feature to create a configuration with the API key from the environment variables

## Troubleshooting

If you encounter issues with the LG ThinQ integration:

1. **Check API Key**
   - Verify that the API key is valid and has the necessary permissions
   - Generate a new token if needed

2. **Check Region and Country**
   - Verify that the region and country values are correct for your location
   - The API uses different base URLs depending on the region

3. **Check API Response**
   - If you receive a 401 Unauthorized error, check your API key
   - If you receive a 400 Bad Request error, check the request parameters and headers

4. **Enable Debug Logging**
   - Set the `DEBUG` environment variable to include 'lg-thinq:*' to enable debug logging
   - This will provide more detailed information about API requests and responses