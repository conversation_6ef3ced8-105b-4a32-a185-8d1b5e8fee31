# Permission Model Design for CSF Portal

## Current System
- Roles have an array of string permissions
- Admin role has wildcard permission '*'
- User role has basic permissions like 'view_shortcuts', 'view_drive'
- Most API endpoints are restricted to 'admin' role
- Some endpoints allow additional roles like 'asset_manager' or 'content_manager'
- Users can have integrations enabled/disabled but not different permission levels

## New Permission Model

### Permission Structure
For each integration, we'll define three permission levels:
1. **read** - View-only access to the integration
2. **write** - Can view and modify data in the integration
3. **admin** - Full control over the integration, including configuration

### Permission Format
Permissions will be stored as strings in the format: `{integration}:{level}`

Examples:
- `googleDrive:read` - Read-only access to Google Drive
- `lenelS2NetBox:write` - Read and write access to Lenel S2 NetBox
- `wiim:admin` - Full control over Wiim integration

### Special Permissions
- `*` - Wildcard permission (grants all permissions)
- `{integration}:*` - All permissions for a specific integration

### Integration List
Based on the server/integrations directory, these are the integrations that need permission controls:

1. appleBusinessManager
2. canva
3. dreo
4. glpi
5. googleAdmin
6. googleCalendar
7. googleDrive
8. googleForms
9. lenelS2NetBox
10. lgThinq
11. mosyleBusiness
12. planningCenter
13. radius
14. rainBird
15. synology
16. unifiAccess
17. unifiNetwork
18. unifiProtect
19. wiim

### Additional Platform Management Permissions
In addition to integration-specific permissions, we'll add permissions for managing the platform:

1. **users:read** - View users
2. **users:write** - Create and modify users
3. **users:admin** - Full control over user management
4. **roles:read** - View roles
5. **roles:write** - Create and modify roles
6. **roles:admin** - Full control over role management
7. **shortcuts:read** - View shortcuts
8. **shortcuts:write** - Create and modify shortcuts
9. **shortcuts:admin** - Full control over shortcuts
10. **help:read** - View help content
11. **help:write** - Create and modify help content
12. **help:admin** - Full control over help content

### Default Roles
1. **admin** - Has wildcard permission '*'
2. **user** - Has basic read permissions for common integrations

## Database Schema Changes
No schema changes are needed for the Role model, as it already supports an array of string permissions. We'll just use a more structured format for the permission strings.

## Implementation Plan
1. Update the role edit UI to include permission selection for each integration
2. Add UI components for setting different permission levels (read/write/admin)
3. Update the backend to handle the new permission format
4. Update the permission checks in the application to use the new permission format