# ZeeVee HDbridge 2920-NA Integration

This document provides information about the ZeeVee HDbridge 2920-NA integration in CSFPortal.

## Overview

The ZeeVee HDbridge 2920-NA is a professional-grade video distribution device that encodes and modulates HDMI sources to IP or RF outputs. This integration allows you to control and monitor the ZeeVee HDbridge 2920-NA device through the CSFPortal interface.

## Features

- View device information and system status
- Manage encoders and decoders
- Control video channels
- Configure network settings
- Monitor device health

## Configuration

The ZeeVee integration requires the following environment variables to be set:

- `ZEEVEE_HOST`: The hostname or IP address of the ZeeVee HDbridge 2920-NA
- `ZEEVEE_PORT`: The port number (optional, defaults to 80)
- `ZEEVEE_USERNAME`: The username for authentication (optional)
- `ZEEVEE_PASSWORD`: The password for authentication (optional)

### Example Configuration

```
ZEEVEE_HOST=*************
ZEEVEE_PORT=80
ZEEVEE_USERNAME=admin
ZEEVEE_PASSWORD=password
```

## API Endpoints

The ZeeVee integration provides the following API endpoints:

### Device Information

- `GET /api/zeevee/device/info`: Get device information
- `GET /api/zeevee/system/status`: Get system status
- `GET /api/zeevee/health`: Get health status

### Encoders

- `GET /api/zeevee/encoders`: Get all encoders
- `GET /api/zeevee/encoders/:id`: Get encoder details
- `PUT /api/zeevee/encoders/:id/input`: Set encoder input

### Decoders

- `GET /api/zeevee/decoders`: Get all decoders
- `GET /api/zeevee/decoders/:id`: Get decoder details
- `PUT /api/zeevee/decoders/:id/output`: Set decoder output

### Channels

- `GET /api/zeevee/channels`: Get all video channels
- `GET /api/zeevee/channels/:id`: Get channel details

### System Operations

- `POST /api/zeevee/system/reboot`: Reboot device
- `GET /api/zeevee/network/settings`: Get network settings

### Configuration

- `GET /api/zeevee/config`: Get ZeeVee configuration
- `POST /api/zeevee/config`: Save ZeeVee configuration (disabled, use environment variables)
- `POST /api/zeevee/one-click-setup`: Set up ZeeVee with one click (disabled, use environment variables)

## Troubleshooting

If you encounter issues with the ZeeVee integration, check the following:

1. Verify that the ZeeVee HDbridge 2920-NA device is powered on and connected to the network
2. Ensure that the environment variables are correctly set
3. Check that the hostname or IP address is reachable from the CSFPortal server
4. Verify that the username and password are correct
5. Check the server logs for error messages

## References

- [ZeeVee HDbridge 2000 Series Documentation](https://www.zeevee.com/products/hdbridge-2000-series/hdbridge-2920-na/)
- [ZeeVee HDbridge 2000 Series API Guide](https://www.zeevee.com/wp-content/uploads/2018/11/HDbridge-2000-Series-API-Guide.pdf)