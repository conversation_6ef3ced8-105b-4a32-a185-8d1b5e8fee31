# SkyportCloud HVAC Controls Integration

This document provides information about the SkyportCloud HVAC Controls integration in the CSF Portal.

## Overview

The SkyportCloud integration allows you to control your HVAC systems directly from the CSF Portal. You can:

- View and control temperature settings
- Change HVAC modes (heat, cool, auto, off)
- Adjust fan settings
- Enable/disable hold and away modes
- View schedules
- Monitor energy usage
- Manage multi-zone systems

## Setup Instructions

### Prerequisites

1. You need a SkyportCloud account
2. You need either:
   - A SkyportCloud API key (recommended for better security)
   - Your SkyportCloud username and password
3. Your HVAC devices must be properly set up in your SkyportCloud account

### Configuration Steps

1. Log in to the CSF Portal with administrator privileges
2. Navigate to the SkyportCloud setup page at `/skyportcloud/setup`
3. Choose your authentication method:
   - **API Key**: Enter your SkyportCloud API key
   - **Username/Password**: Enter your SkyportCloud username and password
4. (Optional) If you're using a custom SkyportCloud API endpoint, enter it in the "API Base URL" field
5. Click "Save Configuration"
6. If the connection is successful, your devices will be loaded automatically
7. (Optional) Select a default device if you have multiple devices

## Using the SkyportCloud Integration

### Accessing the HVAC Controls

1. Log in to the CSF Portal
2. Navigate to the SkyportCloud page at `/skyportcloud`
3. If you have multiple devices, select the device you want to control from the dropdown menu

### Temperature Control

- Use the temperature slider to adjust the temperature
- The current temperature setting is displayed in the center of the control

### Mode Control

- Click on the mode buttons to switch between:
  - Cool: Activates cooling
  - Heat: Activates heating
  - Auto: Automatically switches between cooling and heating as needed
  - Off: Turns off the HVAC system

### Fan Control

- Select a fan mode from the dropdown menu:
  - Auto: Fan runs only when heating or cooling is active
  - On: Fan runs continuously
  - Circulate: Fan runs periodically to circulate air

### Settings

- Hold Temperature: Toggle this switch to hold the current temperature setting indefinitely
- Away Mode: Toggle this switch to enable away mode, which uses energy-saving settings

### Schedule

The Schedule tab displays the current schedule for your HVAC system. This is view-only; schedules must be created and modified in your SkyportCloud account.

### Energy Usage

The Energy Usage tab displays energy consumption data for your HVAC system. You can select different time periods (day, week, month, year) to view usage patterns.

### Zones (Multi-zone systems only)

If your HVAC system supports multiple zones, the Zones tab will be available. This tab displays information about each zone and allows you to control them individually.

## Troubleshooting

### Connection Issues

If you're having trouble connecting to your SkyportCloud account:

1. Verify that your authentication credentials are correct:
   - If using API key: Verify that your API key is correct and not expired
   - If using username/password: Verify that your username and password are correct
2. Check that your SkyportCloud account is active and has the necessary permissions
3. Ensure that your HVAC devices are properly set up in your SkyportCloud account
4. Check your network connection

### Control Issues

If you're having trouble controlling your HVAC system:

1. Verify that your device is online in your SkyportCloud account
2. Check that your device supports the feature you're trying to use
3. Try refreshing the page to get the latest device status
4. Try logging out and back in to the CSF Portal

## Technical Details

### API Endpoints

The SkyportCloud integration uses the following API endpoints:

- `/api/skyportcloud/config` - Get/save configuration
- `/api/skyportcloud/user` - Get user information
- `/api/skyportcloud/devices` - Get list of devices
- `/api/skyportcloud/devices/:deviceId` - Get device information
- `/api/skyportcloud/devices/:deviceId/status` - Get device status
- `/api/skyportcloud/devices/:deviceId/temperature` - Set temperature
- `/api/skyportcloud/devices/:deviceId/mode` - Set mode
- `/api/skyportcloud/devices/:deviceId/fan` - Set fan mode
- `/api/skyportcloud/devices/:deviceId/schedule` - Get schedule
- `/api/skyportcloud/devices/:deviceId/energy` - Get energy usage
- `/api/skyportcloud/devices/:deviceId/hold` - Set hold status
- `/api/skyportcloud/devices/:deviceId/away` - Set away mode
- `/api/skyportcloud/devices/:deviceId/zones` - Get zones
- `/api/skyportcloud/devices/:deviceId/zones/:zoneId` - Set zone settings

### Environment Variables

The following environment variables can be used to configure the SkyportCloud integration:

- `SKYPORTCLOUD_API_KEY` - Default API key (for API key authentication)
- `SKYPORTCLOUD_USERNAME` - Default username (for username/password authentication)
- `SKYPORTCLOUD_PASSWORD` - Default password (for username/password authentication)
- `SKYPORTCLOUD_BASE_URL` - Default API base URL

### Data Storage

The SkyportCloud integration stores the following data in the database:

- Authentication credentials (encrypted):
  - API key (if using API key authentication)
  - Username and password (if using username/password authentication)
- API base URL
- Default device ID

## Support

If you need help with the SkyportCloud integration, please contact your system administrator or the CSF Portal support team.

## Future Enhancements

Planned enhancements for the SkyportCloud integration include:

- Schedule editing
- Historical data visualization
- Integration with building management system
- Mobile app support