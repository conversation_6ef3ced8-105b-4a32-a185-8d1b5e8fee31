# Person Details View Implementation

## Overview

This document describes the implementation of the person details view functionality in the CSF Portal application. The feature allows users to view detailed information about people from both the local database and Planning Center.

## Changes Made

### 1. Backend API Endpoint

Added a new API endpoint to fetch detailed information for a specific Planning Center person:

- **Endpoint**: `GET /api/planning-center/people/:id`
- **Controller**: `getPersonById` in `planningCenterController.js`
- **API Method**: `getPersonById` in `planningCenterAPI.js`

The endpoint includes additional data such as emails, phone numbers, addresses, and households by default using the Planning Center API's "include" parameter.

### 2. Frontend Service Method

Added a new method to the people service to fetch a person by ID from Planning Center:

```javascript
export const getPlanningCenterPersonById = async (id, params = {}) => {
  try {
    if (!id) {
      throw new Error('Person ID is required');
    }
    
    // Remove the 'pc_' prefix if it exists (used in the directory view)
    const personId = id.startsWith('pc_') ? id.substring(3) : id;
    
    const response = await axios.get(`/api/planning-center/people/${personId}`, { params });
    return response.data.person;
  } catch (error) {
    console.error(`Error fetching Planning Center person with ID ${id}:`, error);
    throw error;
  }
};
```

### 3. PersonDetails Component

Created a new component `PersonDetails.js` to display detailed information about a person:

- Supports both local people and Planning Center people
- Fetches person details when the component mounts
- Displays basic information (name, email, phone, etc.)
- Shows categories and ministry associations
- For Planning Center people, displays additional contact details (emails, phone numbers, addresses, households)
- For local people, displays additional information (address, social media, emergency contact)
- Handles loading, error, and empty states

### 4. PeoplePage Component Updates

Modified the PeoplePage component to add details view functionality:

- Added state variables to manage the details dialog:
  ```javascript
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedPersonForDetails, setSelectedPersonForDetails] = useState(null);
  ```

- Added functions to handle opening and closing the details dialog:
  ```javascript
  const handleViewPersonDetails = (person) => {
    setSelectedPersonForDetails(person);
    setDetailsDialogOpen(true);
  };

  const handleCloseDetailsDialog = () => {
    setDetailsDialogOpen(false);
    setSelectedPersonForDetails(null);
  };
  ```

- Added a "View Details" button to the actions column in the people table:
  ```jsx
  <IconButton
    onClick={() => handleViewPersonDetails(person)}
    size="small"
    color="primary"
    title="View Details"
  >
    <VisibilityIcon />
  </IconButton>
  ```

- Added the details dialog component to the JSX:
  ```jsx
  <Dialog
    open={detailsDialogOpen}
    onClose={handleCloseDetailsDialog}
    maxWidth="md"
    fullWidth
  >
    <DialogTitle>
      Person Details
      {selectedPersonForDetails?.isPlanningCenterPerson && (
        <Chip 
          icon={<CloudIcon />} 
          label="Planning Center" 
          size="small" 
          color="primary" 
          sx={{ ml: 2 }}
        />
      )}
    </DialogTitle>
    <DialogContent>
      {selectedPersonForDetails && (
        <PersonDetails
          personId={selectedPersonForDetails.isPlanningCenterPerson ? 
            selectedPersonForDetails.planningCenterId : 
            selectedPersonForDetails._id}
          isPlanningCenterPerson={!!selectedPersonForDetails.isPlanningCenterPerson}
          onClose={handleCloseDetailsDialog}
        />
      )}
    </DialogContent>
  </Dialog>
  ```

## How to Use

1. Navigate to the People Directory page
2. Click the "View Details" button (eye icon) next to a person in the table
3. A dialog will open showing detailed information about the person
4. For Planning Center people, additional contact details will be displayed if available
5. Click the "Close" button to close the dialog

## Testing

### Local People

- Verify that basic information is displayed correctly
- Verify that address, social media, and emergency contact information is displayed if available
- Verify that categories and ministry associations are displayed correctly

### Planning Center People

- Verify that basic information is displayed correctly
- Verify that additional contact details (emails, phone numbers, addresses, households) are displayed if available
- Verify that the Planning Center icon is displayed next to the person's name

### Edge Cases

- Verify that loading state is displayed while fetching person details
- Verify that error state is displayed if there's an error fetching person details
- Verify that empty state is displayed if no person details are available