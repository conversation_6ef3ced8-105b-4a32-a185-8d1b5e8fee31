# Canva PKCE Implementation

This document describes the implementation of PKCE (Proof Key for Code Exchange) for the Canva OAuth flow in the CSF Portal application.

## Overview

PKCE is an extension to the OAuth 2.0 Authorization Code flow to prevent CSRF and authorization code injection attacks. It is required by Canva's authentication system as documented at https://www.canva.dev/docs/connect/authentication/.

The implementation adds the following components:

1. PKCE utility functions to generate code verifiers and code challenges
2. Code verifier storage to maintain the code verifier between the authorization request and token exchange
3. Updates to the Canva API client to include the code challenge in the authorization URL and the code verifier in the token exchange
4. Updates to the Canva controller to handle the code verifier storage and retrieval

## Implementation Details

### PKCE Utilities

A new utility module `pkceUtils.js` was created to generate the code verifier and code challenge:

```javascript
const crypto = require('crypto');

function generateCodeVerifier() {
  return crypto.randomBytes(96).toString('base64url');
}

function generateCodeChallenge(codeVerifier) {
  return crypto
    .createHash('sha256')
    .update(codeVerifier)
    .digest('base64url');
}

module.exports = {
  generateCodeVerifier,
  generateCodeChallenge
};
```

### Code Verifier Storage

A new utility module `codeVerifierStore.js` was created to store and retrieve code verifiers:

```javascript
const codeVerifiers = new Map();

function storeCodeVerifier(userId, codeVerifier, expiryMs = 10 * 60 * 1000) {
  codeVerifiers.set(userId, {
    codeVerifier,
    expiry: Date.now() + expiryMs
  });
  
  setTimeout(() => {
    const entry = codeVerifiers.get(userId);
    if (entry && entry.codeVerifier === codeVerifier) {
      codeVerifiers.delete(userId);
    }
  }, expiryMs);
}

function getAndRemoveCodeVerifier(userId) {
  const entry = codeVerifiers.get(userId);
  
  if (entry && entry.expiry > Date.now()) {
    codeVerifiers.delete(userId);
    return entry.codeVerifier;
  }
  
  codeVerifiers.delete(userId);
  return null;
}

module.exports = {
  storeCodeVerifier,
  getAndRemoveCodeVerifier
};
```

### Canva API Client Updates

The Canva API client was updated to include the code challenge in the authorization URL and the code verifier in the token exchange:

#### Updated `getAuthUrl` Method

```javascript
getAuthUrl(scopes = ['designs:read', 'templates:read', 'users:read', 'files:read'], codeVerifier = null) {
  if (!this.clientId || !this.redirectUri) {
    throw new Error('Client ID and redirect URI are required for OAuth authentication');
  }
  
  const pkceUtils = require('../../utils/pkceUtils');
  
  const verifier = codeVerifier || pkceUtils.generateCodeVerifier();
  const codeChallenge = pkceUtils.generateCodeChallenge(verifier);
  
  const params = {
    client_id: this.clientId,
    redirect_uri: this.redirectUri,
    response_type: 'code',
    scope: scopes.join(' '),
    code_challenge: codeChallenge,
    code_challenge_method: 'S256'
  };
  
  return {
    authUrl: `${this.authURL}/authorize?${querystring.stringify(params)}`,
    codeVerifier: verifier
  };
}
```

#### Updated `exchangeCode` Method

```javascript
async exchangeCode(code, codeVerifier) {
  if (!this.clientId || !this.clientSecret || !this.redirectUri) {
    throw new Error('Client ID, client secret, and redirect URI are required for OAuth authentication');
  }
  
  if (!codeVerifier) {
    throw new Error('Code verifier is required for PKCE OAuth authentication');
  }
  
  try {
    const response = await axios.post(`${this.authURL}/token`, querystring.stringify({
      client_id: this.clientId,
      client_secret: this.clientSecret,
      redirect_uri: this.redirectUri,
      grant_type: 'authorization_code',
      code: code,
      code_verifier: codeVerifier
    }), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
    
    // ... rest of the method
  } catch (error) {
    // ... error handling
  }
}
```

### Canva Controller Updates

The Canva controller was updated to handle the code verifier storage and retrieval:

#### Updated `getAuthUrl` Controller Method

```javascript
exports.getAuthUrl = async (req, res) => {
  try {
    // ... existing checks
    
    const api = new CanvaAPI(domain, '', clientId, clientSecret, redirectUri);
    const { authUrl, codeVerifier } = api.getAuthUrl();
    
    const codeVerifierStore = require('../utils/codeVerifierStore');
    codeVerifierStore.storeCodeVerifier(req.user.id, codeVerifier);
    
    return res.json({ 
      message: 'Please authorize with Canva to use the integration',
      authUrl: authUrl
    });
  } catch (error) {
    // ... error handling
  }
};
```

#### Updated `handleCallback` Controller Method

```javascript
exports.handleCallback = async (req, res) => {
  try {
    // ... existing checks
    
    const codeVerifierStore = require('../utils/codeVerifierStore');
    const codeVerifier = codeVerifierStore.getAndRemoveCodeVerifier(req.user.id);
    
    if (!codeVerifier) {
      return res.status(400).json({ 
        message: 'Code verifier not found or expired. Please try authenticating again.'
      });
    }
    
    const api = new CanvaAPI(domain, '', clientId, clientSecret, redirectUri);
    const tokens = await api.exchangeCode(code, codeVerifier);
    
    // ... rest of the method
  } catch (error) {
    // ... error handling
  }
};
```

## Testing

To test the implementation:

1. Navigate to the Canva integration page in the CSF Portal
2. Click the "Connect with Canva" button
3. Authorize the application in Canva
4. Verify that you are redirected back to the CSF Portal and the authentication is successful

## Notes

- The code verifier store is implemented as an in-memory store, which is suitable for development but may not be appropriate for production. In a production environment, consider using a more robust storage solution like Redis or a database.
- The code verifier expires after 10 minutes by default. This can be adjusted by modifying the `expiryMs` parameter in the `storeCodeVerifier` function.