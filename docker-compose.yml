version: '3.8'

services:
  csfportal:
    build: .
    container_name: csfportal
    ports:
      - "2001:8080"
      - "1812:1812/udp"
      - "1813:1813/udp"
    env_file:
      - .env.production
    volumes:
      - csfportal-data:/app/data
    restart: unless-stopped
    networks:
      - csfportal-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "host-172-16-0-4:**********"
      - "host-172-16-0-71:***********"
      - "host-172-17-0-25:***********"

  # If you want to run MongoDB in a container (optional)
  mongodb:
    image: mongo:4.4
    container_name: mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb-data:/data/db
    restart: unless-stopped
    networks:
      - csfportal-network

volumes:
  csfportal-data:
  mongodb-data:

networks:
  csfportal-network:
    driver: bridge
