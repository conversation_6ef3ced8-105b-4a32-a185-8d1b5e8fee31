# Google Drive Integration Troubleshooting Guide

This document provides guidance for diagnosing and resolving common issues with the Google Drive integration in the CSF Portal application.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Common Error Messages](#common-error-messages)
- [Service Account Authentication Issues](#service-account-authentication-issues)
- [API Call Issues](#api-call-issues)
- [Testing the Integration](#testing-the-integration)
- [Debugging Tips](#debugging-tips)

## Prerequisites

The Google Drive integration requires the following environment variables to be properly configured:

```
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key content here\n-----END PRIVATE KEY-----\n"
```

## Common Error Messages

### "Failed to load files from Google Drive"

This is a generic error message shown to users in the frontend. To diagnose the specific issue:

1. Check the server logs for detailed error messages
2. Look for error messages containing "Google Drive API" or "Service account"
3. Verify that the service account credentials are properly configured
4. Ensure the user has a valid email address in their profile

### "Service account credentials are missing or invalid"

This indicates that the environment variables for the service account are not properly configured:

1. Verify that `GOOGLE_SERVICE_ACCOUNT_EMAIL` is set and contains a valid service account email
2. Verify that `GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY` is set and contains a properly formatted private key
3. Check that the private key includes newline characters (`\n`) at the appropriate places

### "No user email provided for service account authentication"

The service account needs to impersonate a user to access Google Drive:

1. Ensure the user is properly authenticated in the application
2. Verify that the user has an email address in their profile
3. Check that the email address is associated with a Google account that has access to the Drive files

### "Invalid credentials when accessing Google Drive"

This can occur when:

1. The service account doesn't have the necessary permissions
2. The impersonated user doesn't have access to the requested resources
3. The service account has been disabled or deleted in the Google Cloud Console

## Service Account Authentication Issues

### Setting Up a Service Account

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create or select a project
3. Navigate to "IAM & Admin" > "Service Accounts"
4. Create a new service account with a descriptive name
5. Grant the service account the necessary roles (at minimum, "Service Account Token Creator")
6. Create a new key for the service account (JSON format)
7. Extract the email and private key from the JSON file and set them as environment variables

### Domain-Wide Delegation

For the service account to impersonate users, you need to configure domain-wide delegation:

1. Go to your Google Workspace Admin Console
2. Navigate to Security > API Controls
3. In the "Domain-wide Delegation" section, click "Manage Domain-wide Delegation"
4. Add a new API client with the service account's Client ID
5. Add the following OAuth scopes:
   - `https://www.googleapis.com/auth/drive`
   - `https://www.googleapis.com/auth/drive.file`
   - `https://www.googleapis.com/auth/drive.metadata`

### Private Key Format

The private key must be properly formatted with newline characters. When setting the environment variable:

```
GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAA...\n-----END PRIVATE KEY-----\n"
```

Note the `\n` characters that represent newlines. If these are missing, authentication will fail.

## API Call Issues

### Permission Issues

If the API calls are failing with permission errors:

1. Verify that the service account has domain-wide delegation configured
2. Check that the impersonated user has access to the requested files
3. Ensure the service account has the necessary OAuth scopes configured

### Rate Limiting

Google Drive API has rate limits that can cause failures:

1. Implement exponential backoff for retries
2. Consider caching frequently accessed data
3. Monitor API usage in the Google Cloud Console

## Testing the Integration

Use the provided test script to verify the integration is working correctly:

```bash
# Basic test
node test-google-drive-service-account.js

# With debug output
DEBUG=true node test-google-drive-service-account.js

# With specific test user
TEST_USER_EMAIL=<EMAIL> TEST_USER_PASSWORD=password123 node test-google-drive-service-account.js
```

The test script will:
1. Authenticate with the application
2. Test listing files from Google Drive
3. Test searching for files
4. Test getting file details
5. Test viewer and editor URLs
6. Test favorites functionality

## Debugging Tips

### Enable Detailed Logging

The application has been updated with enhanced error logging. To view these logs:

1. Check the server console output
2. Look for log messages prefixed with "Google Drive API:" or "Google Drive Controller:"
3. Error responses now include a `debug` field with additional information

### Check Integration Status

The application tracks the status of integrations:

1. Navigate to the admin dashboard
2. Check the "Integrations" section
3. Look for the status of the Google Drive integration
4. If it shows an error, the error message will provide guidance on how to fix it

### Test with a Specific User

If the integration works for some users but not others:

1. Verify that the user has a valid email address in their profile
2. Check that the user's email is associated with a Google account
3. Ensure the user has access to the Google Drive resources
4. Test impersonating the user with the service account manually:

```javascript
const { google } = require('googleapis');
const auth = new google.auth.JWT({
  email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
  key: process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY,
  scopes: ['https://www.googleapis.com/auth/drive'],
  subject: '<EMAIL>' // The user to impersonate
});

const drive = google.drive({ version: 'v3', auth });
drive.files.list().then(response => {
  console.log(response.data);
}).catch(error => {
  console.error('Error:', error);
});
```

### Common Solutions

1. **Regenerate the service account key** if you suspect it's corrupted or compromised
2. **Check for special characters** in the private key that might need escaping
3. **Verify OAuth scopes** match what's configured in the Google Workspace Admin Console
4. **Clear browser cache and cookies** if the frontend is showing authentication issues
5. **Restart the application server** after making changes to environment variables

If all else fails, check the [Google Drive API documentation](https://developers.google.com/drive/api/v3/reference) and [Google Auth Library documentation](https://github.com/googleapis/google-auth-library-nodejs) for more information.