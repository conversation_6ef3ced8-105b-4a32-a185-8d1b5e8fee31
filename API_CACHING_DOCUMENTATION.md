# API Caching Implementation

## Overview

This document describes the caching mechanism implemented for external API calls in the CSF Portal application. The caching system is designed to improve performance and reduce unnecessary API calls for data that doesn't change frequently, such as device details.

## Benefits

- **Improved Performance**: Reduces response times for frequently accessed data
- **Reduced API Load**: Minimizes the number of calls to external APIs
- **Better User Experience**: Provides faster access to device information
- **Reduced Resource Usage**: Decreases network traffic and server load

## Implementation Details

### Caching Utility

A simple in-memory caching utility has been implemented in `/server/utils/cacheUtil.js`. This utility provides:

- A Map-based storage system for cached values
- Configurable expiration times for different types of data
- Methods for getting, setting, and removing cached items
- Helper methods for creating cache keys

### Cached Endpoints

The following API endpoints now use caching:

#### WiiM Controller
- `GET /api/wiim/device` - Device information (10 minute cache)
- `GET /api/wiim/config` - Configuration settings (15 minute cache)
- `GET /api/wiim/inputs` - Available inputs (30 minute cache)
- `GET /api/wiim/equalizer` - Equalizer settings (10 minute cache)

#### Panasonic Controller
- `GET /api/panasonic/info` - Camera information (15 minute cache)
- `GET /api/panasonic/presets` - Camera presets (30 minute cache)
- `GET /api/panasonic/config` - Configuration settings (15 minute cache)

#### Authentication Tokens
- Apple Business Manager API - Access tokens (cached until expiration)
  - Tokens are cached based on client ID and key ID
  - Expiration time is determined by the `expires_in` value returned from the API
  - Tokens are automatically refreshed when they expire

### Cache Duration

Cache durations were selected based on how frequently the data is expected to change:
- Device information and configuration: 10-15 minutes
- Rarely changing data (inputs, presets): 30 minutes
- Authentication tokens: Based on expiration time returned by the authentication provider (typically 1 hour)
  - A safety buffer (5 minutes) is subtracted from the expiration time to ensure tokens are refreshed before they expire

### Client-Side Caching

In addition to server-side caching, appropriate Cache-Control headers are set to enable client-side caching:
```
Cache-Control: private, max-age=XXX
```

Where XXX is the cache duration in seconds.

## Testing

A test script is provided at `/test-cache.js` to verify the caching behavior. This script:
1. Makes multiple requests to each cached endpoint
2. Measures response times
3. Checks for cache-related headers
4. Verifies that subsequent requests are served from cache

To run the test:
```
node test-cache.js
```

## Maintenance Considerations

### Cache Invalidation

The cache automatically expires based on the configured duration. If you need to manually invalidate the cache:

1. Restart the server (clears all cache)
2. Use the `cacheUtil.remove(key)` or `cacheUtil.clear()` methods in your code

### Modifying Cache Duration

To adjust the cache duration for an endpoint, locate the corresponding controller method and modify the duration parameter in the `cacheUtil.set()` call:

```javascript
// Example: Change cache duration to 20 minutes
cacheUtil.set(cacheKey, data, 20 * 60 * 1000);
```

### Adding Caching to New Endpoints

To add caching to a new endpoint:

1. Import the cache utility:
   ```javascript
   const cacheUtil = require('../../utils/cacheUtil');
   ```

2. Create a cache key:
   ```javascript
   const cacheKey = cacheUtil.createKey('your-endpoint-name', params);
   ```

3. Check the cache before making API calls:
   ```javascript
   let data = cacheUtil.get(cacheKey);
   if (!data) {
     // Fetch data from API
     data = await yourAPI.getData();
     // Cache the result
     cacheUtil.set(cacheKey, data, duration);
   }
   ```

4. Set appropriate Cache-Control headers:
   ```javascript
   res.setHeader('Cache-Control', 'private, max-age=XXX');
   ```

### Adding Token Caching to Authentication Flows

To add token caching to an API authentication flow:

1. Import the cache utility:
   ```javascript
   const cacheUtil = require('../../utils/cacheUtil');
   ```

2. Create a cache key based on unique client identifiers:
   ```javascript
   const cacheKey = cacheUtil.createKey('api-name-token', {
     clientId: this.clientId,
     keyId: this.keyId
   });
   ```

3. Check for a cached token before authenticating:
   ```javascript
   const cachedToken = cacheUtil.get(cacheKey);
   if (cachedToken) {
     console.log('Using cached token');
     this.token = cachedToken.token;
     this.tokenExpiration = cachedToken.expiration;
     return this.token;
   }
   ```

4. When obtaining a new token, cache it with its expiration:
   ```javascript
   // Calculate token expiration (subtract safety buffer)
   const expiresIn = response.data.expires_in * 1000;
   this.tokenExpiration = Date.now() + expiresIn - (5 * 60 * 1000);
   
   // Cache the token with its expiration
   cacheUtil.set(cacheKey, {
     token: this.token,
     expiration: this.tokenExpiration
   }, expiresIn - (5 * 60 * 1000));
   ```

## Future Improvements

Potential enhancements to the caching system:

1. **Distributed Caching**: Replace in-memory cache with Redis or similar for multi-server deployments
2. **Cache Analytics**: Add monitoring to track cache hit/miss rates
3. **Selective Cache Clearing**: Add API endpoints to clear specific cache entries
4. **Cache Warming**: Proactively populate cache for frequently accessed data
5. **Stale-While-Revalidate**: Serve stale data while fetching fresh data in the background