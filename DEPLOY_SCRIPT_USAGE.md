# CSF Portal Deployment Script

## Overview

The `deploy.sh` script automates the deployment process for the CSF Portal application. It performs the following steps in sequence:

1. Pulls the latest code from the git repository
2. Builds the Docker containers
3. Stops the currently running containers
4. Starts the new containers in detached mode

The script includes error handling to stop the process if any step fails and provides clear error messages for debugging.

## Prerequisites

Before using this script, ensure you have:

- Git installed and configured
- Docker and Docker Compose installed
- Proper permissions to execute the script
- Proper permissions to access the git repository

## Usage

To use the deployment script, navigate to the project root directory and run:

```bash
./deploy.sh
```

## Error Handling

The script will automatically stop if any of the deployment steps fail. Error messages will be displayed in red to help identify and troubleshoot issues.

- If the git pull fails, check your network connection and git repository access.
- If the Docker build fails, review the error messages for specific build issues.
- If stopping or starting containers fails, check Docker's status and logs.

## Output

The script provides color-coded output to make it easy to follow the deployment process:

- Blue text: Informational messages about the current step
- Green text: Success messages when a step completes successfully
- Red text: Error messages when a step fails

## Customization

If you need to customize the deployment process, you can modify the `deploy.sh` script. The script is organized into clear sections for each deployment step, making it easy to add or modify functionality.

## Troubleshooting

If you encounter issues with the deployment script:

1. Ensure all prerequisites are met
2. Check the error messages displayed by the script
3. Verify that Docker and Docker Compose are functioning correctly
4. Check that the `.env.production` file exists and contains the correct configuration

For persistent issues, try running the deployment steps manually to identify the specific point of failure.