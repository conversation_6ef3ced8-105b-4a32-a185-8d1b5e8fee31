# Integration Status Tracker

Last Updated: 2025-08-06T00:25:57.082Z

| Integration | Status | Last Checked | Last Updated | Notes |
| ----------- | ------ | ------------ | ------------ | ----- |
| ----------- | ------ | 2025-06-26T04:09:59.700Z | N/A | ----- |
| UniFi Access | active | 2025-08-06T00:21:26.804Z | 2025-08-06T00:21:26.804Z | Integration is properly authenticated and ready to use. |
| Dreo | error | 2025-08-05T02:31:47.879Z | 2025-07-30T21:46:30.000Z | Authentication error: Incorrect account or password |
| LG ThinQ | active | 2025-07-30T05:11:25.686Z | 2025-07-30T05:11:25.686Z | Successfully connected to LG ThinQ API. |
| Lenel S2 NetBox | error | 2025-08-06T00:25:57.082Z | 2025-07-29T17:12:51.900Z | Authentication error: getaddrinfo ENOTFOUND host-172-16-0-71 |
| Google Calendar | active | 2025-07-29T18:48:12.468Z | 2025-07-29T18:48:12.468Z | Integration is properly authenticated using service account. |
| Planning Center | active | 2025-07-17T19:01:59.487Z | 2025-07-17T19:01:59.487Z | Integration is properly configured with valid credentials. |
| Synology | active | 2025-07-30T04:12:22.778Z | 2025-07-30T04:12:22.778Z | Successfully authenticated with Synology using API version 3. |
| Canva | error | 2025-06-26T04:10:00.709Z | 2023-06-26T00:00:00.000Z | Error: getaddrinfo ENOTFOUND api.test |
| GLPI | error | 2025-06-26T04:10:00.711Z | 2023-06-26T00:00:00.000Z | Error: Invalid URL |
| Google Admin | active | 2025-07-30T19:13:28.241Z | 2025-07-30T19:13:28.241Z | Integration is properly authenticated using service account. |
| Google Drive | active | 2025-07-30T18:10:09.370Z | 2025-07-30T18:10:09.370Z | Integration is properly authenticated with service account and ready to use. |
| Google Forms | active | 2025-08-06T00:21:26.486Z | 2025-08-06T00:21:26.486Z | Integration is properly authenticated using service account. |
| Mosyle Business | error | 2025-07-29T20:32:40.767Z | 2023-06-26T00:00:00.000Z | Error: getaddrinfo ENOTFOUND your_mosyle_subdomain.mosyle.com |
| UniFi Network | error | 2025-08-06T00:21:27.043Z | 2023-06-26T00:00:00.000Z | Error: Failed to connect to UniFi Network Controller at 172.16.0.4:8443. Error: Authentication failed: Error: API Error: 404 - "" |
| UniFi Protect | error | 2025-08-06T00:21:27.045Z | 2025-08-01T06:44:21.104Z | Error: Failed to connect to all UniFi Protect instances: Instance A: instance.api.bootstrap is not a function |
| Apple Business Manager | not_configured | 2025-08-05T15:58:57.073Z | 2023-06-26T00:00:00.000Z | Client ID, Organization ID, Key ID, Private Key, and Issuer ID are required for Apple Business Manager integration. |
| RADIUS | active | 2025-08-06T00:21:28.852Z | 2025-08-06T00:21:28.852Z | RADIUS server is running with static authentication. |
| Rain Bird | not_configured | 2025-06-26T04:10:00.712Z | 2023-06-26T00:00:00.000Z | Authentication failed. Check credentials and try again. |
| WiiM | active | 2025-08-06T00:21:26.825Z | 2025-08-06T00:21:26.825Z | Integration is properly configured and ready to use. |
| SkyportCloud | error | 2025-08-06T00:21:26.864Z | N/A | Error: Request failed with status code 404 |
