# Google Groups Role Mapping

This document explains how to use the Google Groups role mapping feature in the CSF Portal, which allows you to automatically assign roles to users based on their Google Groups membership.

## Overview

The Google Groups role mapping feature allows administrators to:

1. Define mappings between Google Groups and roles in the system
2. Automatically assign roles to users based on their Google Groups membership
3. Manage these mappings through a user-friendly interface

When a user signs in with Google authentication, the system checks which Google Groups they belong to and assigns the corresponding roles based on the configured mappings.

## Requirements

To use this feature, you need:

1. A Google Workspace account with administrative access
2. Service account credentials with domain-wide delegation
3. The following environment variables configured:
   - `GOOGLE_SERVICE_ACCOUNT_EMAIL`
   - `GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY`
   - `GOOGLE_ADMIN_IMPERSONATION_EMAIL`

The service account must have the following OAuth scopes:
- `https://www.googleapis.com/auth/admin.directory.user`
- `https://www.googleapis.com/auth/admin.directory.group`

## Configuration

### Setting Up Google Groups Role Mappings

1. Log in to the CSF Portal as an administrator
2. Navigate to the "Role Management" page
3. Click on the "Default Role Settings" tab
4. In the "Google Groups Role Mapping" section, you can:
   - View existing mappings
   - Add new mappings by clicking the "Add Mapping" button
   - Delete mappings by clicking the delete icon

### Adding a New Mapping

To add a new mapping:

1. Click the "Add Mapping" button
2. Enter the Google Group email address (e.g., `<EMAIL>`)
3. Select the role to assign to members of this group
4. Click "Add"

Users who are members of this Google Group will automatically be assigned the selected role when they sign in.

## How It Works

The Google Groups role mapping feature works as follows:

1. When a user signs in with Google authentication, the system retrieves their Google profile information
2. The system fetches the current role settings, including Google Groups role mappings
3. For each mapping, the system checks if the user is a member of the corresponding Google Group
4. If the user is a member of a mapped group, they are assigned the corresponding role
5. If the user is a member of multiple mapped groups, they receive all the corresponding roles

This process happens automatically during authentication, so users always have the correct roles based on their current Google Groups membership.

## Testing

You can test the Google Groups role mapping feature using the provided test script:

```bash
node test-google-groups-role-mapping.js
```

This script:
1. Tests the `isUserMemberOfGroup` method to verify that group membership checking works correctly
2. Simulates the role assignment process to verify that users are assigned the correct roles based on their Google Groups membership

## Troubleshooting

If users are not being assigned the expected roles based on their Google Groups membership, check the following:

1. Verify that the service account has the necessary OAuth scopes
2. Check that the Google Groups role mappings are correctly configured
3. Ensure that the user is actually a member of the Google Groups in question
4. Check the server logs for any errors related to Google Admin API calls

Common errors:

- **"Not authenticated"**: The service account credentials are missing or invalid
- **"Error checking if user is member of group"**: There was an error checking the user's membership in a specific group
- **"Error checking Google Groups for role assignment"**: There was a general error during the role assignment process

## Limitations

- The system can only check membership in Google Groups that are accessible to the service account
- Role assignments based on Google Groups are only updated when the user signs in
- If a user is removed from a Google Group, their roles will not be updated until they sign in again

## Best Practices

1. Use descriptive names for Google Groups to make it clear what roles they correspond to
2. Regularly audit the Google Groups role mappings to ensure they are up-to-date
3. Consider using Google Groups specifically for role assignment rather than repurposing existing groups
4. Test the role assignments after making changes to the mappings

## Related Documentation

- [Google Admin API Documentation](https://developers.google.com/admin-sdk/directory/reference/rest)
- [Service Account Authentication](https://developers.google.com/identity/protocols/oauth2/service-account)
- [Domain-Wide Delegation](https://developers.google.com/admin-sdk/directory/v1/guides/delegation)