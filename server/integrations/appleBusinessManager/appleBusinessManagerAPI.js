// Apple Business Manager API Wrapper
const axios = require('axios');
const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const integrationTracker = require('../../utils/integrationTracker');
const cacheUtil = require('../../utils/cacheUtil');

/**
 * Apple Business Manager API Wrapper
 * Documentation: https://developer.apple.com/documentation/applebusinessmanagerapi/
 * OAuth Setup: https://support.apple.com/guide/apple-business-manager/create-an-api-account-axm33189f66a/web
 */
class AppleBusinessManagerAPI {
  constructor(clientId, keyId, privateKeyPathOrContent, tokenExpiry = 10 * 60) { // 20 minutes default
    this.clientId = clientId;
    this.keyId = keyId;
    this.privateKeyPathOrContent = privateKeyPathOrContent;
    // Ensure tokenExpiry is a number
    this.tokenExpiry = parseInt(tokenExpiry, 10) || 20 * 60;
    this.baseURL = 'https://api-business.apple.com';
    this.token = null;
    this.tokenExpiration = null;
    this.integrationName = 'Apple Business Manager';
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    // Determine if privateKeyPathOrContent is a file path or the actual key content
    // If it starts with '-----BEGIN PRIVATE KEY-----', it's the key content
    this.isPrivateKeyContent = typeof privateKeyPathOrContent === 'string' && 
                              privateKeyPathOrContent.trim().startsWith('-----BEGIN EC PRIVATE KEY-----');
  }

  /**
   * Initialize the Apple Business Manager API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if all required credentials are provided
      if (!this.clientId ||
          !this.keyId || !this.privateKeyPathOrContent) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName,
          'not_configured',
          null,
          'Client ID, Organization ID, Key ID, Private Key, and Issuer ID are required for Apple Business Manager integration.'
        );
        return;
      }

      // Check if privateKeyPathOrContent is defined and not empty
      if (typeof this.privateKeyPathOrContent !== 'string' || this.privateKeyPathOrContent.trim() === '') {
        integrationTracker.updateStatus(
          this.integrationName,
          'error',
          null,
          'Private key path or content is undefined, empty, or not a string'
        );
        throw new Error('Private key path or content is undefined, empty, or not a string');
      }

      // If it's not the key content directly, check if the file exists
      if (!this.isPrivateKeyContent) {
        // Check if private key file exists
        if (!fs.existsSync(this.privateKeyPathOrContent)) {
          integrationTracker.updateStatus(
            this.integrationName,
            'error',
            null,
            `Private key file not found at path: ${this.privateKeyPathOrContent}`
          );
          throw new Error(`Private key file not found at path: ${this.privateKeyPathOrContent}`);
        }
      }

      // Authenticate with the API
      await this.authenticate();

      // Test the connection by making a simple API call
      await this.getDevices({ limit: 1 });

      // If we get here, the connection was successful
      integrationTracker.updateStatus(
        this.integrationName,
        'active',
        new Date(),
        'Integration is properly authenticated and ready to use.'
      );
    } catch (error) {
      console.error('Error initializing Apple Business Manager API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName,
        'error',
        null,
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Generate a JWT token for Apple Business Manager API authentication
   * @returns {string} JWT token
   * @private
   */
  _generateJWT() {
    try {
      // Check if privateKeyPathOrContent is defined and not empty
      if (typeof this.privateKeyPathOrContent !== 'string' || this.privateKeyPathOrContent.trim() === '') {
        throw new Error('Private key path or content is undefined, empty, or not a string');
      }
      
      // Get the private key - either directly from the content or by reading the file
      let privateKey;
      if (this.isPrivateKeyContent) {
        // Use the content directly
        privateKey = this.privateKeyPathOrContent;
      } else {
        // Read from file
        privateKey = fs.readFileSync(this.privateKeyPathOrContent, 'utf8');
      }

      // Current time in seconds
      const now = Math.floor(Date.now() / 1000);

      // JWT payload
        
      const payload = {
        iat: now,
        exp: now + this.tokenExpiry,
        aud: 'https://account.apple.com/auth/oauth2/v2/token',
        iss: this.clientId,
        sub: this.clientId,
        jti: crypto.randomUUID()
      };

      // JWT header
      const header = {
        alg: 'ES256',
        kid: this.keyId
      };

      // Sign the JWT
      return jwt.sign(payload, privateKey, { 
        algorithm: 'ES256',
        header: header
      });
    } catch (error) {
      console.error('Error generating JWT token:', error);
      throw error;
    }
  }

  /**
   * Authenticate with Apple Business Manager API
   * This method implements token caching to reduce authentication calls to Apple's servers.
   * It follows this process:
   * 1. Check if a valid token exists in the shared cache
   * 2. If not, check if the instance has a valid token
   * 3. If not, request a new token from Apple
   * 4. Cache the new token for future use by all instances
   * 
   * @returns {Promise<string>} Authentication token
   */
  async authenticate() {
    try {
      // Create a cache key for this client based on unique identifiers
      // This ensures different API clients get different cached tokens
      const cacheKey = cacheUtil.createKey('apple-business-manager-token', {
        clientId: this.clientId,
        keyId: this.keyId
      });
      
      // Check if we have a valid token in the shared cache
      // This allows token sharing across different instances of the API client
      const cachedToken = cacheUtil.get(cacheKey);
      if (cachedToken) {
        console.log('Using cached token for Apple Business Manager API');
        this.token = cachedToken.token;
        this.tokenExpiration = cachedToken.expiration;
        // Set the token for all future requests
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
        return this.token;
      }
      
      // Fallback: Check if instance token is still valid
      // This is a secondary check in case the token wasn't in the cache
      if (this.token && this.tokenExpiration && Date.now() < this.tokenExpiration) {
        return this.token;
      }

      // Using JWT bearer flow for authentication
      console.log('Using JWT bearer flow for authentication');
      
      // Generate JWT token
      const jwtToken = this._generateJWT();
      console.log('Generated JWT token for authentication');

      // Prepare request parameters for JWT bearer flow
        
      const requestParams = {
        grant_type: 'client_credentials',
        client_id: this.clientId,
        scope: 'business.api',
        client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
        client_assertion: jwtToken
      };
      
      console.log('Authentication request parameters:', JSON.stringify({
        ...requestParams,
        client_assertion: '[REDACTED]' // Don't log the actual JWT token
      }, null, 2));

      // Exchange JWT token for access token
      try {
        // Use the specific OAuth endpoint for Apple Business Manager
        const oauthEndpoint = 'https://account.apple.com/auth/oauth2/v2/token';
        
        // Use this.axios but with the full URL to override the baseURL for this specific request
        const requestBody = new URLSearchParams(requestParams).toString();
        const response = await this.axios.post(oauthEndpoint, requestBody, {
          baseURL: '',
          headers: {
            'User-Agent': 'csfportal/1.0',
            'Content-Type': 'application/x-www-form-urlencoded'
          }

        });
        
        console.log('Authentication response status:', response.status);
        console.log('Authentication response headers:', JSON.stringify(response.headers, null, 2));
        console.log('Authentication response data:', JSON.stringify(response.data, null, 2));
        
        // Store the access token from the response
        this.token = response.data.access_token;
        
        // Calculate token expiration time in milliseconds
        // We subtract a 5-minute safety buffer to ensure we refresh before the token actually expires
        const expiresIn = response.data.expires_in * 1000; // Convert seconds to milliseconds
        this.tokenExpiration = Date.now() + expiresIn - (5 * 60 * 1000);
        
        // Store the token in the shared cache for use by all API instances
        // The cache entry includes both the token and its expiration timestamp
        // The cache duration is set to match the token's expiration (minus safety buffer)
        // This ensures the cache entry will be automatically invalidated when the token expires
        cacheUtil.set(cacheKey, {
          token: this.token,
          expiration: this.tokenExpiration
        }, expiresIn - (5 * 60 * 1000));
        
        console.log(`Cached Apple Business Manager token with expiration ${new Date(this.tokenExpiration).toISOString()}`);
        
        // Set the token for all future requests made by this instance
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;

        return this.token;
      } catch (error) {
        console.error('Error in authentication request:');
        if (error.response) {
          console.error('Error response status:', error.response.status);
          console.error('Error response headers:', JSON.stringify(error.response.headers, null, 2));
          console.error('Error response data:', JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
          console.error('No response received. Request:', error.request);
        } else {
          console.error('Error setting up request:', error.message);
        }
        throw error;
      }
    } catch (error) {
      console.error('Error authenticating with Apple Business Manager API:', error.message);
      throw error;
    }
  }

  /**
   * Get all devices
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of devices
   */
  async getDevices(params = {}) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Using the correct endpoint for devices from the Apple Business Manager API
      const response = await this.axios.get(`/v1/orgDevices`, {
        params: {
          ...params,
          limit: params.limit || 100,
          organizationId: this.organizationId
        }
      });

      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching Apple Business Manager devices:', error);
      throw error;
    }
  }

  /**
   * Get device details
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device details
   */
  async getDeviceDetails(deviceId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Using the correct endpoint for device details from the Apple Business Manager API
      const response = await this.axios.get(`/v1/orgDevices/${deviceId}`, {
        params: {
          organizationId: this.organizationId
        }
      });

      return response.data.data || {};
    } catch (error) {
      console.error(`Error fetching Apple Business Manager device details for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Get device MDM assignment information
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} MDM assignment information
   */
  async getDeviceMdmAssignment(deviceId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Using the correct endpoint for device MDM assignment from the Apple Business Manager API
      const response = await this.axios.get(`/v1/orgDevices/${deviceId}/mdmAssignment`, {
        params: {
          organizationId: this.organizationId
        }
      });

      return response.data.data || {};
    } catch (error) {
      console.error(`Error fetching MDM assignment for device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Assign device to MDM service
   * @param {string} deviceId Device ID
   * @param {string} mdmServerId MDM service ID
   * @returns {Promise<Object>} Assignment result
   */
  async assignDeviceToMdm(deviceId, mdmServerId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Using the correct endpoint for device MDM assignment from the Apple Business Manager API
      const response = await this.axios.post(`/v1/orgDevices/${deviceId}/mdmAssignment`, {
        mdmServerId: mdmServerId,
        organizationId: this.organizationId
      });

      return response.data.data || {};
    } catch (error) {
      console.error(`Error assigning device ${deviceId} to MDM service ${mdmServerId}:`, error);
      throw error;
    }
  }

  /**
   * Reassign device to a different MDM service
   * @param {string} deviceId Device ID
   * @param {string} mdmServerId New MDM service ID
   * @returns {Promise<Object>} Reassignment result
   */
  async reassignDeviceToMdm(deviceId, mdmServerId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Using the correct endpoint for device MDM reassignment from the Apple Business Manager API
      const response = await this.axios.put(`/v1/orgDevices/${deviceId}/mdmAssignment`, {
        mdmServerId: mdmServerId,
        organizationId: this.organizationId
      });

      return response.data.data || {};
    } catch (error) {
      console.error(`Error reassigning device ${deviceId} to MDM service ${mdmServerId}:`, error);
      throw error;
    }
  }

  /**
   * Unassign device from MDM service
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Unassignment result
   */
  async unassignDeviceFromMdm(deviceId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Using the correct endpoint for device MDM unassignment from the Apple Business Manager API
      const response = await this.axios.delete(`/v1/orgDevices/${deviceId}/mdmAssignment`, {
        params: {
          organizationId: this.organizationId
        }
      });

      return response.data.data || {};
    } catch (error) {
      console.error(`Error unassigning device ${deviceId} from MDM service:`, error);
      throw error;
    }
  }

  /**
   * Get organization details
   * @returns {Promise<Object>} Organization details
   */
  async getOrganizationDetails() {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Using the correct endpoint for organization details from the Apple Business Manager API
      const response = await this.axios.get(`/v1/organizations/${this.organizationId}`);

      return response.data.data || {};
    } catch (error) {
      console.error('Error fetching Apple Business Manager organization details:', error);
      throw error;
    }
  }

  /**
   * Get all MDM servers
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of MDM servers
   */
  async getMdmServers(params = {}) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Using the correct endpoint for MDM servers from the Apple Business Manager API
      const response = await this.axios.get(`/v1/mdmServers`, {
        params: {
          ...params,
          limit: params.limit || 100,
          organizationId: this.organizationId
        }
      });

      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching Apple Business Manager MDM servers:', error);
      throw error;
    }
  }
}

module.exports = AppleBusinessManagerAPI;
