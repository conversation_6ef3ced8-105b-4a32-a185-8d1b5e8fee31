// UniFi Protect API Wrapper using unifi-protect npm package
// Using dynamic import for ES Module
const integrationTracker = require('../../utils/integrationTracker');

// We'll initialize ProtectApi later using dynamic import

/**
 * UniFi Protect API Wrapper
 * Uses the unifi-protect npm package: https://github.com/hjdhjd/unifi-protect
 */
class UnifiProtectAPI {
  constructor() {
    this.instances = {};
    this.integrationName = 'UniFi Protect';
    this.ProtectApiModule = null;
    
    // We'll initialize instances after loading the ProtectApi module
    this.initializeInstances();
  }
  
  /**
   * Initialize instances based on environment variables
   * This is separated from the constructor to allow for async operations
   */
  async initializeInstances() {
    try {
      // Dynamically import the unifi-protect module
      const module = await import('unifi-protect');
      this.ProtectApiModule = module.ProtectApi;
      
      // Initialize instances if environment variables are set
      if (process.env.UNIFI_PROTECT_HOST_A && process.env.UNIFI_PROTECT_API_KEY_A) {
        await this.addInstance('A', process.env.UNIFI_PROTECT_HOST_A, process.env.UNIFI_PROTECT_API_KEY_A);
      }
      
      if (process.env.UNIFI_PROTECT_HOST_B && process.env.UNIFI_PROTECT_API_KEY_B) {
        await this.addInstance('B', process.env.UNIFI_PROTECT_HOST_B, process.env.UNIFI_PROTECT_API_KEY_B);
      }
      
      // For backward compatibility
      if (process.env.UNIFI_PROTECT_HOST && process.env.UNIFI_PROTECT_USERNAME && process.env.UNIFI_PROTECT_PASSWORD) {
        await this.addLegacyInstance(
          process.env.UNIFI_PROTECT_HOST,
          process.env.UNIFI_PROTECT_USERNAME,
          process.env.UNIFI_PROTECT_PASSWORD,
          process.env.UNIFI_PROTECT_PORT || 443
        );
      }
    } catch (error) {
      console.error('Error initializing UniFi Protect instances:', error);
    }
  }
  
  /**
   * Add a new UniFi Protect instance using API key
   * @param {string} id - Instance identifier (e.g., 'A', 'B')
   * @param {string} host - Host or IP address
   * @param {string} apiKey - API key for authentication
   * @param {number} port - Port number (default: 443)
   * @returns {Promise<void>}
   */
  async addInstance(id, host, apiKey, port = 443) {
    // Ensure ProtectApiModule is loaded
    if (!this.ProtectApiModule) {
      console.warn(`[UniFi Protect ${id}] ProtectApi module not loaded yet, waiting for import to complete`);
      try {
        const module = await import('unifi-protect');
        this.ProtectApiModule = module.ProtectApi;
      } catch (error) {
        console.error(`[UniFi Protect ${id}] Failed to import ProtectApi module:`, error);
        return;
      }
    }
    
    // Create a custom logger that prefixes log messages with the instance ID
    const logger = {
      debug: (message, ...parameters) => console.debug(`[UniFi Protect ${id}] ${message}`, ...parameters),
      error: (message, ...parameters) => console.error(`[UniFi Protect ${id}] ${message}`, ...parameters),
      info: (message, ...parameters) => console.log(`[UniFi Protect ${id}] ${message}`, ...parameters),
      warn: (message, ...parameters) => console.warn(`[UniFi Protect ${id}] ${message}`, ...parameters)
    };
    
    // Create a new ProtectApi instance
    const protectApi = new this.ProtectApiModule(logger);
    
    // Store the instance information
    this.instances[id] = {
      id,
      host,
      apiKey,
      port,
      api: protectApi,
      isInitialized: false
    };
  }
  
  /**
   * Add a legacy UniFi Protect instance using username/password
   * @param {string} host - Host or IP address
   * @param {string} username - Username for authentication
   * @param {string} password - Password for authentication
   * @param {number} port - Port number (default: 443)
   * @returns {Promise<void>}
   */
  async addLegacyInstance(host, username, password, port = 443) {
    // Ensure ProtectApiModule is loaded
    if (!this.ProtectApiModule) {
      console.warn('[UniFi Protect Legacy] ProtectApi module not loaded yet, waiting for import to complete');
      try {
        const module = await import('unifi-protect');
        this.ProtectApiModule = module.ProtectApi;
      } catch (error) {
        console.error('[UniFi Protect Legacy] Failed to import ProtectApi module:', error);
        return;
      }
    }
    
    // Create a custom logger for the legacy instance
    const logger = {
      debug: (message, ...parameters) => console.debug(`[UniFi Protect Legacy] ${message}`, ...parameters),
      error: (message, ...parameters) => console.error(`[UniFi Protect Legacy] ${message}`, ...parameters),
      info: (message, ...parameters) => console.log(`[UniFi Protect Legacy] ${message}`, ...parameters),
      warn: (message, ...parameters) => console.warn(`[UniFi Protect Legacy] ${message}`, ...parameters)
    };
    
    // Create a new ProtectApi instance
    const protectApi = new this.ProtectApiModule(logger);
    
    // Store the instance information
    this.instances['legacy'] = {
      id: 'legacy',
      host,
      username,
      password,
      port,
      api: protectApi,
      isInitialized: false
    };
  }

  /**
   * Initialize the UniFi Protect API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Ensure the ProtectApi module is loaded
      if (!this.ProtectApiModule) {
        try {
          console.log('Loading UniFi Protect API module...');
          const module = await import('unifi-protect');
          this.ProtectApiModule = module.ProtectApi;
          console.log('UniFi Protect API module loaded successfully');
        } catch (error) {
          console.error('Failed to import UniFi Protect API module:', error);
          integrationTracker.updateStatus(
            this.integrationName,
            'error',
            null,
            `Failed to import UniFi Protect API module: ${error.message}`
          );
          throw error;
        }
      }

      // Check if we have any instances configured
      const instanceIds = Object.keys(this.instances);
      
      if (instanceIds.length === 0) {
        // No instances configured
        integrationTracker.updateStatus(
          this.integrationName,
          'not_configured',
          null,
          'No UniFi Protect instances configured. Please set UNIFI_PROTECT_HOST_A/B and UNIFI_PROTECT_API_KEY_A/B environment variables.'
        );
        return;
      }
      
      // Test connection to all instances
      let successCount = 0;
      let errorMessages = [];
      
      for (const id of instanceIds) {
        try {
          // Get the instance
          const instance = this.instances[id];
          
          // Ensure the instance has a valid API object
          if (!instance.api && this.ProtectApiModule) {
            // Create a custom logger
            const logger = {
              debug: (message, ...parameters) => console.debug(`[UniFi Protect ${id}] ${message}`, ...parameters),
              error: (message, ...parameters) => console.error(`[UniFi Protect ${id}] ${message}`, ...parameters),
              info: (message, ...parameters) => console.log(`[UniFi Protect ${id}] ${message}`, ...parameters),
              warn: (message, ...parameters) => console.warn(`[UniFi Protect ${id}] ${message}`, ...parameters)
            };
            
            // Create a new ProtectApi instance
            instance.api = new this.ProtectApiModule(logger);
          }
          
          // Login to the Protect controller
          if (id === 'legacy') {
            // Use username/password for legacy instance
            const loginSuccess = await instance.api.login(instance.host, instance.username, instance.password);
            if (!loginSuccess) {
              throw new Error('Login failed');
            }
          } else {
            // Use API key for modern instances
            // The unifi-protect package doesn't have direct API key support, so we need to set headers manually
            // This is a workaround until the package supports API keys directly
            instance.api.nvrAddress = instance.host;
            instance.api.headers = {
              'Authorization': `Bearer ${instance.apiKey}`
            };
            
            // Test the connection by bootstrapping
            await instance.api.bootstrap();
          }
          
          // Mark the instance as initialized
          instance.isInitialized = true;
          successCount++;
        } catch (error) {
          errorMessages.push(`Instance ${id}: ${error.message}`);
        }
      }
      
      if (successCount === 0) {
        // All instances failed
        integrationTracker.updateStatus(
          this.integrationName,
          'error',
          null,
          `Failed to connect to all UniFi Protect instances: ${errorMessages.join('; ')}`
        );
        throw new Error(`Failed to connect to all UniFi Protect instances: ${errorMessages.join('; ')}`);
      }

      // If we get here, at least one connection was successful
      integrationTracker.updateStatus(
        this.integrationName,
        'active',
        new Date(),
        'Integration is properly authenticated and ready to use.'
      );
    } catch (error) {
      console.error('Error initializing UniFi Protect API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName,
        'error',
        null,
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Get the appropriate ProtectApi instance for the given instance ID
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B', 'legacy')
   * @returns {Object} ProtectApi instance
   * @private
   */
  async _getApiInstance(instanceId) {
    // Ensure the ProtectApi module is loaded
    if (!this.ProtectApiModule) {
      try {
        console.log('Loading UniFi Protect API module from _getApiInstance...');
        const module = await import('unifi-protect');
        this.ProtectApiModule = module.ProtectApi;
        console.log('UniFi Protect API module loaded successfully from _getApiInstance');
      } catch (error) {
        console.error('Failed to import UniFi Protect API module from _getApiInstance:', error);
        throw error;
      }
    }
    
    // If no instanceId is provided, use the first available instance
    if (!instanceId) {
      const instanceIds = Object.keys(this.instances);
      if (instanceIds.length === 0) {
        throw new Error('No UniFi Protect instances configured');
      }
      instanceId = instanceIds[0];
    }
    
    // Check if the requested instance exists
    if (!this.instances[instanceId]) {
      throw new Error(`UniFi Protect instance '${instanceId}' not found`);
    }
    
    const instance = this.instances[instanceId];
    
    // Ensure the instance has a valid API object
    if (!instance.api && this.ProtectApiModule) {
      // Create a custom logger
      const logger = {
        debug: (message, ...parameters) => console.debug(`[UniFi Protect ${instanceId}] ${message}`, ...parameters),
        error: (message, ...parameters) => console.error(`[UniFi Protect ${instanceId}] ${message}`, ...parameters),
        info: (message, ...parameters) => console.log(`[UniFi Protect ${instanceId}] ${message}`, ...parameters),
        warn: (message, ...parameters) => console.warn(`[UniFi Protect ${instanceId}] ${message}`, ...parameters)
      };
      
      // Create a new ProtectApi instance
      instance.api = new this.ProtectApiModule(logger);
    }
    
    // Initialize the instance if it's not already initialized
    if (!instance.isInitialized) {
      if (instanceId === 'legacy') {
        // Use username/password for legacy instance
        const loginSuccess = await instance.api.login(instance.host, instance.username, instance.password);
        if (!loginSuccess) {
          throw new Error('Login failed');
        }
      } else {
        // Use API key for modern instances
        instance.api.nvrAddress = instance.host;
        instance.api.headers = {
          'Authorization': `Bearer ${instance.apiKey}`
        };
        
        // Bootstrap the API
        await instance.api.bootstrap();
      }
      
      // Mark the instance as initialized
      instance.isInitialized = true;
    }
    
    return instance.api;
  }

  /**
   * Get all cameras
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Array>} List of cameras
   */
  async getCameras(instanceId = null) {
    try {
      const api = await this._getApiInstance(instanceId);
      
      // Get the bootstrap data which contains all cameras
      const bootstrap = await api.bootstrap();
      
      // Extract cameras from the bootstrap data
      const cameras = bootstrap.cameras || [];
      
      // Add instance information to each camera
      if (instanceId && cameras.length) {
        cameras.forEach(camera => {
          camera.instanceId = instanceId;
        });
      }
      
      return cameras;
    } catch (error) {
      console.error(`Error fetching UniFi Protect cameras from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }
  
  /**
   * Get all cameras from all instances
   * @returns {Promise<Array>} List of cameras from all instances
   */
  async getAllCameras() {
    const instanceIds = Object.keys(this.instances);
    
    // If no instances are configured, throw an error
    if (instanceIds.length === 0) {
      throw new Error('No UniFi Protect instances configured');
    }
    
    // Get cameras from all instances
    const results = await Promise.allSettled(
      instanceIds.map(id => this.getCameras(id))
    );
    
    // Combine results from successful calls
    const cameras = [];
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        cameras.push(...result.value);
      } else {
        console.error(`Error fetching cameras from instance ${instanceIds[index]}:`, result.reason);
      }
    });
    
    return cameras;
  }

  /**
   * Get camera details
   * @param {string} cameraId - Camera ID
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Object>} Camera details
   */
  async getCameraDetails(cameraId, instanceId = null) {
    try {
      const api = await this._getApiInstance(instanceId);
      
      // Get the bootstrap data which contains all cameras
      const bootstrap = await api.bootstrap();
      
      // Find the specific camera by ID
      const camera = bootstrap.cameras.find(cam => cam.id === cameraId);
      
      if (!camera) {
        throw new Error(`Camera ${cameraId} not found`);
      }
      
      // Add instance information
      if (instanceId) {
        camera.instanceId = instanceId;
      }
      
      return camera;
    } catch (error) {
      console.error(`Error fetching UniFi Protect camera details for ${cameraId} from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }

  /**
   * Get camera snapshot
   * @param {string} cameraId - Camera ID
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Buffer>} Camera snapshot image
   */
  async getCameraSnapshot(cameraId, instanceId = null) {
    try {
      const api = await this._getApiInstance(instanceId);
      
      // Get the camera details first to ensure it exists
      const bootstrap = await api.bootstrap();
      const camera = bootstrap.cameras.find(cam => cam.id === cameraId);
      
      if (!camera) {
        throw new Error(`Camera ${cameraId} not found`);
      }
      
      // Get the snapshot using the unifi-protect package
      // The package doesn't have a direct method for snapshots, so we need to use the underlying API
      const response = await api.retrieve(`/proxy/protect/api/cameras/${cameraId}/snapshot`, {
        responseType: 'arraybuffer'
      });
      
      return Buffer.from(response);
    } catch (error) {
      console.error(`Error fetching UniFi Protect camera snapshot for ${cameraId} from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }

  /**
   * Get all events
   * @param {Object} params - Query parameters
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Array>} List of events
   */
  async getEvents(params = {}, instanceId = null) {
    try {
      const api = await this._getApiInstance(instanceId);
      
      // Get events using the unifi-protect package
      // The package doesn't have a direct method for events with filtering, so we need to use the underlying API
      const response = await api.retrieve('/proxy/protect/api/events', {
        params: params
      });
      
      const events = response || [];
      
      // Add instance information to each event
      if (instanceId && events.length) {
        events.forEach(event => {
          event.instanceId = instanceId;
        });
      }
      
      return events;
    } catch (error) {
      console.error(`Error fetching UniFi Protect events from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }
  
  /**
   * Get events from all instances
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} List of events from all instances
   */
  async getAllEvents(params = {}) {
    const instanceIds = Object.keys(this.instances);
    
    // If no instances are configured, throw an error
    if (instanceIds.length === 0) {
      throw new Error('No UniFi Protect instances configured');
    }
    
    // Get events from all instances
    const results = await Promise.allSettled(
      instanceIds.map(id => this.getEvents(params, id))
    );
    
    // Combine results from successful calls
    const events = [];
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        events.push(...result.value);
      } else {
        console.error(`Error fetching events from instance ${instanceIds[index]}:`, result.reason);
      }
    });
    
    return events;
  }

  /**
   * Get system status
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Object>} System status
   */
  async getSystemStatus(instanceId = null) {
    try {
      const api = await this._getApiInstance(instanceId);
      
      // Get the bootstrap data which contains system information
      const bootstrap = await api.bootstrap();
      
      // Extract relevant system information
      const systemInfo = {
        nvr: bootstrap.nvr,
        version: bootstrap.version,
        systemInfo: bootstrap.systemInfo,
        lastUpdateId: bootstrap.lastUpdateId,
        lastMotionEventTime: bootstrap.lastMotionEventTime,
        cameras: bootstrap.cameras.length,
        doorbells: bootstrap.cameras.filter(cam => cam.type === 'DOORBELL').length,
        lights: bootstrap.lights?.length || 0,
        sensors: bootstrap.sensors?.length || 0,
        viewers: bootstrap.viewers?.length || 0
      };
      
      // Add instance information
      if (instanceId) {
        systemInfo.instanceId = instanceId;
      }
      
      return systemInfo;
    } catch (error) {
      console.error(`Error fetching UniFi Protect system status from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }
  
  /**
   * Get system status from all instances
   * @returns {Promise<Array>} System status from all instances
   */
  async getAllSystemStatus() {
    const instanceIds = Object.keys(this.instances);
    
    // If no instances are configured, throw an error
    if (instanceIds.length === 0) {
      throw new Error('No UniFi Protect instances configured');
    }
    
    // Get system status from all instances
    const results = await Promise.allSettled(
      instanceIds.map(id => this.getSystemStatus(id))
    );
    
    // Combine results from successful calls
    const statuses = [];
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        statuses.push({
          ...result.value,
          instanceId: instanceIds[index]
        });
      } else {
        console.error(`Error fetching system status from instance ${instanceIds[index]}:`, result.reason);
      }
    });
    
    return statuses;
  }

  /**
   * Control PTZ camera
   * @param {string} cameraId - Camera ID
   * @param {string} action - PTZ action (goto, patrol_start, patrol_stop, zoom, move)
   * @param {Object} params - Action parameters
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Object>} Response data
   */
  async controlPTZ(cameraId, action, params = {}, instanceId = null) {
    try {
      const api = await this._getApiInstance(instanceId);
      
      // Get the camera details first to ensure it exists
      const bootstrap = await api.bootstrap();
      const camera = bootstrap.cameras.find(cam => cam.id === cameraId);
      
      if (!camera) {
        throw new Error(`Camera ${cameraId} not found`);
      }
      
      // Check if the camera supports PTZ
      if (!camera.featureFlags.hasPtz) {
        throw new Error(`Camera ${cameraId} does not support PTZ`);
      }
      
      let endpoint;
      let method = 'POST';
      let data = {};
      
      // Determine the correct endpoint and data based on action
      switch (action) {
        case 'goto':
          endpoint = `/proxy/protect/api/cameras/${cameraId}/ptz/goto/${params.slot || 0}`;
          break;
        case 'patrol_start':
          endpoint = `/proxy/protect/api/cameras/${cameraId}/ptz/patrol/start/${params.slot || 0}`;
          break;
        case 'patrol_stop':
          endpoint = `/proxy/protect/api/cameras/${cameraId}/ptz/patrol/stop`;
          break;
        case 'move':
          endpoint = `/proxy/protect/api/cameras/${cameraId}/ptz/move`;
          data = params.data || {};
          break;
        case 'zoom':
          endpoint = `/proxy/protect/api/cameras/${cameraId}/ptz/zoom`;
          data = params.data || {};
          break;
        default:
          throw new Error(`Unsupported PTZ action: ${action}`);
      }
      
      // Execute the PTZ command
      const response = await api.retrieve(endpoint, {
        method: method,
        data: data
      });
      
      return response || { success: true };
    } catch (error) {
      console.error(`Error controlling UniFi Protect PTZ camera ${cameraId} from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }

  /**
   * Get all viewers
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Array>} List of viewers
   */
  async getViewers(instanceId = null) {
    try {
      const api = await this._getApiInstance(instanceId);
      
      // Get the bootstrap data which contains all viewers
      const bootstrap = await api.bootstrap();
      
      // Extract viewers from the bootstrap data
      const viewers = bootstrap.viewers || [];
      
      // Add instance information to each viewer
      if (instanceId && viewers.length) {
        viewers.forEach(viewer => {
          viewer.instanceId = instanceId;
        });
      }
      
      return viewers;
    } catch (error) {
      console.error(`Error fetching UniFi Protect viewers from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }

  /**
   * Update viewer settings
   * @param {string} viewerId - Viewer ID
   * @param {Object} settings - Viewer settings to update
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Object>} Updated viewer data
   */
  async updateViewer(viewerId, settings, instanceId = null) {
    try {
      const api = await this._getApiInstance(instanceId);
      
      // Get the viewer details first to ensure it exists
      const bootstrap = await api.bootstrap();
      const viewer = bootstrap.viewers.find(v => v.id === viewerId);
      
      if (!viewer) {
        throw new Error(`Viewer ${viewerId} not found`);
      }
      
      // Update the viewer using the unifi-protect package
      const response = await api.retrieve(`/proxy/protect/api/viewers/${viewerId}`, {
        method: 'PATCH',
        data: settings
      });
      
      // Add instance information
      if (instanceId && response) {
        response.instanceId = instanceId;
      }
      
      return response;
    } catch (error) {
      console.error(`Error updating UniFi Protect viewer ${viewerId} from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }
}

module.exports = UnifiProtectAPI;