const AppleBusinessManagerAPI = require('../integrations/appleBusinessManager/appleBusinessManagerAPI');

// Initialize Apple Business Manager API with null (will be properly initialized when needed)
let appleBusinessManagerAPI = null;

// Helper function to get the configuration from environment variables
const getLatestConfig = async () => {
  try {
    // Get configuration from environment variables
    const envClientId = process.env.APPLE_BUSINESS_MANAGER_CLIENT_ID || '';
    const envKeyId = process.env.APPLE_BUSINESS_MANAGER_KEY_ID || '';
    const envPrivateKey = process.env.APPLE_BUSINESS_MANAGER_PRIVATE_KEY || '';
    const envTokenExpiry = process.env.APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY || 1200;
    
    // Determine which private key source to use (content takes precedence over path)
    const privateKeySource = envPrivateKey;
    
    // If environment variables are set, use them directly
    if (envClientId && envKeyId &&
        privateKeySource && typeof privateKeySource === 'string' && privateKeySource.trim() !== '') {
      appleBusinessManagerAPI = new AppleBusinessManagerAPI(
        envClientId, 
        envKeyId,
        privateKeySource,
        envTokenExpiry
      );
      return {
        clientId: envClientId,
        keyId: envKeyId,
        privateKeySource: 'content',
        privateKeyPath: envPrivateKey,
        tokenExpiry: envTokenExpiry,
        updatedAt: new Date(),
        fromEnv: true
      };
    }
    
    // If environment variables are not set, return null
    console.error('Apple Business Manager environment variables not set. Please configure them in the .env file.');
    appleBusinessManagerAPI = null;
    return null;
  } catch (error) {
    console.error('Error initializing Apple Business Manager configuration:', error);
    appleBusinessManagerAPI = null;
    throw error;
  }
};

/**
 * Get all Apple Business Manager devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDevices = async (req, res) => {
  try {
    await getLatestConfig();
    
    // Check if API is properly initialized
    if (!appleBusinessManagerAPI) {
      return res.status(400).json({ 
        message: 'Error fetching Apple Business Manager devices', 
        error: 'Apple Business Manager is not properly configured. Please check your configuration.' 
      });
    }
    
    const devices = await appleBusinessManagerAPI.getDevices(req.query);
    res.json(devices);
  } catch (error) {
    console.error('Controller error fetching Apple Business Manager devices:', error);
    res.status(500).json({ message: 'Error fetching Apple Business Manager devices', error: error.message });
  }
};

/**
 * Get Apple Business Manager device details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceDetails = async (req, res) => {
  try {
    await getLatestConfig();
    
    // Check if API is properly initialized
    if (!appleBusinessManagerAPI) {
      return res.status(400).json({ 
        message: 'Error fetching Apple Business Manager device details', 
        error: 'Apple Business Manager is not properly configured. Please check your configuration.' 
      });
    }
    
    const deviceId = req.params.id;
    const deviceDetails = await appleBusinessManagerAPI.getDeviceDetails(deviceId);
    res.json(deviceDetails);
  } catch (error) {
    console.error('Controller error fetching Apple Business Manager device details:', error);
    res.status(500).json({ message: 'Error fetching Apple Business Manager device details', error: error.message });
  }
};

/**
 * Get device MDM assignment information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceMdmAssignment = async (req, res) => {
  try {
    await getLatestConfig();
    
    // Check if API is properly initialized
    if (!appleBusinessManagerAPI) {
      return res.status(400).json({ 
        message: 'Error fetching Apple Business Manager device MDM assignment', 
        error: 'Apple Business Manager is not properly configured. Please check your configuration.' 
      });
    }
    
    const deviceId = req.params.id;
    const mdmAssignment = await appleBusinessManagerAPI.getDeviceMdmAssignment(deviceId);
    res.json(mdmAssignment);
  } catch (error) {
    console.error('Controller error fetching Apple Business Manager device MDM assignment:', error);
    res.status(500).json({ message: 'Error fetching Apple Business Manager device MDM assignment', error: error.message });
  }
};

/**
 * Assign device to MDM service
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.assignDeviceToMdm = async (req, res) => {
  try {
    await getLatestConfig();
    
    // Check if API is properly initialized
    if (!appleBusinessManagerAPI) {
      return res.status(400).json({ 
        message: 'Error assigning Apple Business Manager device to MDM service', 
        error: 'Apple Business Manager is not properly configured. Please check your configuration.' 
      });
    }
    
    const deviceId = req.params.id;
    const { mdmServerId } = req.body;
    
    if (!mdmServerId) {
      return res.status(400).json({ 
        message: 'Error assigning Apple Business Manager device to MDM service', 
        error: 'MDM server ID is required' 
      });
    }
    
    const result = await appleBusinessManagerAPI.assignDeviceToMdm(deviceId, mdmServerId);
    res.json(result);
  } catch (error) {
    console.error('Controller error assigning Apple Business Manager device to MDM service:', error);
    res.status(500).json({ message: 'Error assigning Apple Business Manager device to MDM service', error: error.message });
  }
};

/**
 * Reassign device to a different MDM service
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.reassignDeviceToMdm = async (req, res) => {
  try {
    await getLatestConfig();
    
    // Check if API is properly initialized
    if (!appleBusinessManagerAPI) {
      return res.status(400).json({ 
        message: 'Error reassigning Apple Business Manager device to MDM service', 
        error: 'Apple Business Manager is not properly configured. Please check your configuration.' 
      });
    }
    
    const deviceId = req.params.id;
    const { mdmServerId } = req.body;
    
    if (!mdmServerId) {
      return res.status(400).json({ 
        message: 'Error reassigning Apple Business Manager device to MDM service', 
        error: 'MDM server ID is required' 
      });
    }
    
    const result = await appleBusinessManagerAPI.reassignDeviceToMdm(deviceId, mdmServerId);
    res.json(result);
  } catch (error) {
    console.error('Controller error reassigning Apple Business Manager device to MDM service:', error);
    res.status(500).json({ message: 'Error reassigning Apple Business Manager device to MDM service', error: error.message });
  }
};

/**
 * Unassign device from MDM service
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.unassignDeviceFromMdm = async (req, res) => {
  try {
    await getLatestConfig();
    
    // Check if API is properly initialized
    if (!appleBusinessManagerAPI) {
      return res.status(400).json({ 
        message: 'Error unassigning Apple Business Manager device from MDM service', 
        error: 'Apple Business Manager is not properly configured. Please check your configuration.' 
      });
    }
    
    const deviceId = req.params.id;
    const result = await appleBusinessManagerAPI.unassignDeviceFromMdm(deviceId);
    res.json(result);
  } catch (error) {
    console.error('Controller error unassigning Apple Business Manager device from MDM service:', error);
    res.status(500).json({ message: 'Error unassigning Apple Business Manager device from MDM service', error: error.message });
  }
};

/**
 * Save Apple Business Manager configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling Apple Business Manager configuration request:', error);
    res.status(500).json({ message: 'Error handling Apple Business Manager configuration request', error: error.message });
  }
};

/**
 * Get Apple Business Manager configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Get configuration from environment variables
    const envClientId = process.env.APPLE_BUSINESS_MANAGER_CLIENT_ID || '';
    const envKeyId = process.env.APPLE_BUSINESS_MANAGER_KEY_ID || '';
    const envPrivateKey = process.env.APPLE_BUSINESS_MANAGER_PRIVATE_KEY || '';
    const envTokenExpiry = process.env.APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY || 1200;
    
    // Determine which private key source to use (content takes precedence over path)
    const hasPrivateKey = envPrivateKey && envPrivateKey.trim() !== '';

    // If environment variables are set, use them directly
    if (envClientId && envKeyId && hasPrivateKey) {
      // Don't send sensitive information back to the client for security
      // For private key, only indicate which method is being used, not the actual content
      res.json({
        clientId: envClientId,
        keyId: envKeyId,
        privateKeySource: 'content',
        privateKeyConfigured: hasPrivateKey,
        tokenExpiry: envTokenExpiry,
        configuredAt: new Date(),
        fromEnv: true
      });
      return;
    }
    
    // If environment variables are not set, return 404
    return res.status(404).json({ 
      message: 'Apple Business Manager configuration not found. Please set the required environment variables.' 
    });
  } catch (error) {
    console.error('Error fetching Apple Business Manager configuration:', error);
    res.status(500).json({ message: 'Error fetching Apple Business Manager configuration', error: error.message });
  }
};

/**
 * Get Apple Business Manager organization details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getOrganizationDetails = async (req, res) => {
  try {
    await getLatestConfig();
    
    // Check if API is properly initialized
    if (!appleBusinessManagerAPI) {
      return res.status(400).json({ 
        message: 'Error fetching Apple Business Manager organization details', 
        error: 'Apple Business Manager is not properly configured. Please check your configuration.' 
      });
    }
    
    const orgDetails = await appleBusinessManagerAPI.getOrganizationDetails();
    res.json(orgDetails);
  } catch (error) {
    console.error('Controller error fetching Apple Business Manager organization details:', error);
    res.status(500).json({ 
      message: 'Error fetching Apple Business Manager organization details', 
      error: error.message 
    });
  }
};

/**
 * Get all MDM servers
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getMdmServers = async (req, res) => {
  try {
    await getLatestConfig();
    
    // Check if API is properly initialized
    if (!appleBusinessManagerAPI) {
      return res.status(400).json({ 
        message: 'Error fetching Apple Business Manager MDM servers', 
        error: 'Apple Business Manager is not properly configured. Please check your configuration.' 
      });
    }
    
    const mdmServers = await appleBusinessManagerAPI.getMdmServers(req.query);
    res.json(mdmServers);
  } catch (error) {
    console.error('Controller error fetching Apple Business Manager MDM servers:', error);
    res.status(500).json({ 
      message: 'Error fetching Apple Business Manager MDM servers', 
      error: error.message 
    });
  }
};
