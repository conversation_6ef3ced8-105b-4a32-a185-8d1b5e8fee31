const SkyportCloudAPI = require('../integrations/skyportcloud/skyportcloudAPI');
const SkyportCloudConfig = require('../../models/SkyportCloudConfig');

// Initialize SkyportCloud API with default values
// Will be updated with actual configuration when available
let skyportCloudAPI = new SkyportCloudAPI({});

// Initialize SkyportCloud API with configuration from environment variables only
const initializeSkyportCloudAPI = async () => {
  try {
    // Only use environment variables for authentication
    const envApiKey = process.env.SKYPORTCLOUD_API_KEY || '';
    const envUsername = process.env.SKYPORTCLOUD_USERNAME || '';
    const envPassword = process.env.SKYPORTCLOUD_PASSWORD || '';
    const envBaseUrl = process.env.SKYPORTCLOUD_BASE_URL || 'https://api.skyportcloud.com';
    
    // Create options object for API initialization
    const options = {};
    let hasCredentials = false;
    
    // Add authentication credentials based on what's available in environment variables
    // Check if API key is provided and not a placeholder value
    if (envApiKey && !envApiKey.includes('your_api_key_here')) {
      options.apiKey = envApiKey;
      hasCredentials = true;
    } else if (envUsername && envPassword) {
      options.username = envUsername;
      options.password = envPassword;
      hasCredentials = true;
    } else if (process.env.NODE_ENV !== 'production') {
      // For development/testing only - use a mock API key
      console.log('Using mock API key for development/testing');
      options.apiKey = 'test_api_key_for_development';
      hasCredentials = true;
    }
    
    if (hasCredentials) {
      skyportCloudAPI = new SkyportCloudAPI(options, envBaseUrl);
      console.log(`SkyportCloud API initialized with environment variables`);
      
      // Initialize the API to update integration status
      await skyportCloudAPI.initialize();
      return;
    }
    
    // If environment variables are not set, log an error
    console.error('SKYPORTCLOUD_API_KEY or SKYPORTCLOUD_USERNAME/PASSWORD environment variables not set. SkyportCloud integration will not be available.');
    
    // Initialize with empty options to ensure the API object exists but will report as not configured
    skyportCloudAPI = new SkyportCloudAPI({}, envBaseUrl);
    await skyportCloudAPI.initialize(); // This will mark the integration as not_configured
  } catch (error) {
    console.error('Error initializing SkyportCloud API:', error);
  }
};

// Call the initialization function
initializeSkyportCloudAPI();

// Helper function to ensure API is initialized
const ensureApiInitialized = () => {
  // Check if either API key or username/password is available
  if (!skyportCloudAPI.apiKey && (!skyportCloudAPI.username || !skyportCloudAPI.password)) {
    throw new Error('SkyportCloud configuration is missing. Please configure the SkyportCloud integration with either an API key or username/password.');
  }
  return true;
};

/**
 * Get user information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUserInfo = async (req, res) => {
  try {
    ensureApiInitialized();
    const userInfo = await skyportCloudAPI.getUserInfo();
    res.json(userInfo);
  } catch (error) {
    console.error('Controller error fetching SkyportCloud user information:', error);
    res.status(500).json({ message: 'Error fetching SkyportCloud user information', error: error.message });
  }
};

/**
 * Get list of devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDevices = async (req, res) => {
  try {
    ensureApiInitialized();
    const devices = await skyportCloudAPI.getDevices();
    res.json(devices);
  } catch (error) {
    console.error('Controller error fetching SkyportCloud devices:', error);
    res.status(500).json({ message: 'Error fetching SkyportCloud devices', error: error.message });
  }
};

/**
 * Get device information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceInfo = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId } = req.params;
    const deviceInfo = await skyportCloudAPI.getDeviceInfo(deviceId);
    res.json(deviceInfo);
  } catch (error) {
    console.error('Controller error fetching SkyportCloud device information:', error);
    res.status(500).json({ message: 'Error fetching SkyportCloud device information', error: error.message });
  }
};

/**
 * Get device status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceStatus = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId } = req.params;
    const deviceStatus = await skyportCloudAPI.getDeviceStatus(deviceId);
    res.json(deviceStatus);
  } catch (error) {
    console.error('Controller error fetching SkyportCloud device status:', error);
    res.status(500).json({ message: 'Error fetching SkyportCloud device status', error: error.message });
  }
};

/**
 * Set device temperature
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setTemperature = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId } = req.params;
    const { temperature } = req.body;
    
    if (temperature === undefined) {
      return res.status(400).json({ message: 'Temperature is required' });
    }
    
    const result = await skyportCloudAPI.setTemperature(deviceId, temperature);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting SkyportCloud device temperature:', error);
    res.status(500).json({ message: 'Error setting SkyportCloud device temperature', error: error.message });
  }
};

/**
 * Set device mode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setMode = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId } = req.params;
    const { mode } = req.body;
    
    if (!mode) {
      return res.status(400).json({ message: 'Mode is required' });
    }
    
    if (!['heat', 'cool', 'auto', 'off'].includes(mode)) {
      return res.status(400).json({ message: 'Mode must be one of: heat, cool, auto, off' });
    }
    
    const result = await skyportCloudAPI.setMode(deviceId, mode);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting SkyportCloud device mode:', error);
    res.status(500).json({ message: 'Error setting SkyportCloud device mode', error: error.message });
  }
};

/**
 * Set fan mode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setFanMode = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId } = req.params;
    const { fanMode } = req.body;
    
    if (!fanMode) {
      return res.status(400).json({ message: 'Fan mode is required' });
    }
    
    if (!['auto', 'on', 'circulate'].includes(fanMode)) {
      return res.status(400).json({ message: 'Fan mode must be one of: auto, on, circulate' });
    }
    
    const result = await skyportCloudAPI.setFanMode(deviceId, fanMode);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting SkyportCloud device fan mode:', error);
    res.status(500).json({ message: 'Error setting SkyportCloud device fan mode', error: error.message });
  }
};

/**
 * Get device schedule
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSchedule = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId } = req.params;
    const schedule = await skyportCloudAPI.getSchedule(deviceId);
    res.json(schedule);
  } catch (error) {
    console.error('Controller error fetching SkyportCloud device schedule:', error);
    res.status(500).json({ message: 'Error fetching SkyportCloud device schedule', error: error.message });
  }
};

/**
 * Set device schedule
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setSchedule = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId } = req.params;
    const schedule = req.body;
    
    if (!schedule) {
      return res.status(400).json({ message: 'Schedule is required' });
    }
    
    const result = await skyportCloudAPI.setSchedule(deviceId, schedule);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting SkyportCloud device schedule:', error);
    res.status(500).json({ message: 'Error setting SkyportCloud device schedule', error: error.message });
  }
};

/**
 * Get device energy usage
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEnergyUsage = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId } = req.params;
    const { period } = req.query;
    const energyUsage = await skyportCloudAPI.getEnergyUsage(deviceId, period);
    res.json(energyUsage);
  } catch (error) {
    console.error('Controller error fetching SkyportCloud device energy usage:', error);
    res.status(500).json({ message: 'Error fetching SkyportCloud device energy usage', error: error.message });
  }
};

/**
 * Set hold status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setHold = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId } = req.params;
    const { enabled, until } = req.body;
    
    if (enabled === undefined) {
      return res.status(400).json({ message: 'Enabled parameter is required' });
    }
    
    const result = await skyportCloudAPI.setHold(deviceId, enabled, until);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting SkyportCloud device hold:', error);
    res.status(500).json({ message: 'Error setting SkyportCloud device hold', error: error.message });
  }
};

/**
 * Set away mode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setAwayMode = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId } = req.params;
    const { enabled } = req.body;
    
    if (enabled === undefined) {
      return res.status(400).json({ message: 'Enabled parameter is required' });
    }
    
    const result = await skyportCloudAPI.setAwayMode(deviceId, enabled);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting SkyportCloud device away mode:', error);
    res.status(500).json({ message: 'Error setting SkyportCloud device away mode', error: error.message });
  }
};

/**
 * Get device zones
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getZones = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId } = req.params;
    const zones = await skyportCloudAPI.getZones(deviceId);
    res.json(zones);
  } catch (error) {
    console.error('Controller error fetching SkyportCloud device zones:', error);
    res.status(500).json({ message: 'Error fetching SkyportCloud device zones', error: error.message });
  }
};

/**
 * Set zone settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setZoneSettings = async (req, res) => {
  try {
    ensureApiInitialized();
    const { deviceId, zoneId } = req.params;
    const settings = req.body;
    
    if (!settings) {
      return res.status(400).json({ message: 'Settings are required' });
    }
    
    const result = await skyportCloudAPI.setZoneSettings(deviceId, zoneId, settings);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting SkyportCloud device zone settings:', error);
    res.status(500).json({ message: 'Error setting SkyportCloud device zone settings', error: error.message });
  }
};

/**
 * Get SkyportCloud configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    console.log('getConfig method called');
    
    // Only use environment variables for authentication
    const envApiKey = process.env.SKYPORTCLOUD_API_KEY || '';
    const envUsername = process.env.SKYPORTCLOUD_USERNAME || '';
    const envPassword = process.env.SKYPORTCLOUD_PASSWORD || '';
    const envBaseUrl = process.env.SKYPORTCLOUD_BASE_URL || 'https://api.skyportcloud.com';
    
    // Log the environment variables for debugging (without showing actual values)
    console.log('SkyportCloud environment variables:');
    console.log(`SKYPORTCLOUD_API_KEY: ${envApiKey ? 'Set' : 'Not set'}`);
    console.log(`SKYPORTCLOUD_USERNAME: ${envUsername ? 'Set' : 'Not set'}`);
    console.log(`SKYPORTCLOUD_PASSWORD: ${envPassword ? 'Set' : 'Not set'}`);
    console.log(`SKYPORTCLOUD_BASE_URL: ${envBaseUrl}`);
    
    // Log the actual values for debugging (length only for sensitive data)
    console.log('Environment variable details:');
    console.log(`SKYPORTCLOUD_API_KEY length: ${envApiKey.length}`);
    console.log(`SKYPORTCLOUD_USERNAME length: ${envUsername.length}`);
    console.log(`SKYPORTCLOUD_PASSWORD length: ${envPassword.length}`);
    
    // Check if environment variables are set
    // First check for username/password, then check for API key
    if (envUsername && envPassword) {
      console.log('SkyportCloud configured with username/password');
      console.log(`Username: ${envUsername}`);
      console.log(`Password length: ${envPassword.length}`);
      
      const configData = {
        baseUrl: envBaseUrl,
        configuredAt: new Date(),
        hasApiKey: false,
        hasUsernamePassword: true,
        authMethod: 'usernamePassword',
        fromEnv: true
      };
      
      console.log('Returning config data:', JSON.stringify(configData));
      res.json(configData);
      return;
    } else if (envApiKey && !envApiKey.includes('your_api_key_here')) {
      console.log('SkyportCloud configured with API key');
      console.log(`API key length: ${envApiKey.length}`);
      
      const configData = {
        baseUrl: envBaseUrl,
        configuredAt: new Date(),
        hasApiKey: true,
        hasUsernamePassword: false,
        authMethod: 'apiKey',
        fromEnv: true
      };
      
      console.log('Returning config data:', JSON.stringify(configData));
      res.json(configData);
      return;
    }
    
    // For development/testing only - use a mock configuration
    if (process.env.NODE_ENV !== 'production') {
      console.log('Using mock configuration for development/testing');
      const configData = {
        baseUrl: envBaseUrl,
        configuredAt: new Date(),
        hasApiKey: true,
        hasUsernamePassword: false,
        authMethod: 'apiKey',
        fromEnv: true,
        isDevelopmentMock: true
      };
      
      res.json(configData);
      return;
    }
    
    // If environment variables are not set, return a 404 with a message
    return res.status(404).json({ 
      message: 'SkyportCloud configuration not found. Please set the required environment variables SKYPORTCLOUD_API_KEY or SKYPORTCLOUD_USERNAME/PASSWORD.',
      configuredWithEnv: false
    });
  } catch (error) {
    console.error('Error fetching SkyportCloud configuration:', error);
    res.status(500).json({ message: 'Error fetching SkyportCloud configuration', error: error.message });
  }
};

/**
 * Save SkyportCloud configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling SkyportCloud configuration request:', error);
    res.status(500).json({ message: 'Error handling SkyportCloud configuration request', error: error.message });
  }
};