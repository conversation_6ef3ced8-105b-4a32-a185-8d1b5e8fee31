const UnifiProtectAPI = require('../integrations/unifiProtect/unifiProtectAPI');
const UnifiProtectConfig = require('../../models/UnifiProtectConfig');

// Initialize UniFi Protect API with environment variables
let unifiProtectAPI = new UnifiProtectAPI();

/**
 * Get the latest UniFi Protect configuration
 * @returns {Promise<Object|null>} The latest configuration or null if not found
 */
const getLatestConfig = async () => {
  try {
    // Check if new environment variables are set for multiple instances
    const envHostA = process.env.UNIFI_PROTECT_HOST_A || '';
    const envApiKeyA = process.env.UNIFI_PROTECT_API_KEY_A || '';
    const envHostB = process.env.UNIFI_PROTECT_HOST_B || '';
    const envApiKeyB = process.env.UNIFI_PROTECT_API_KEY_B || '';
    const envPort = process.env.UNIFI_PROTECT_PORT || 443;
    
    const configs = [];
    
    // Check if we have API key based configuration
    if ((envHostA && envApiKeyA) || (envHostB && envApiKeyB)) {
      if (envHostA && envApiKeyA) {
        configs.push({
          id: 'A',
          host: envHostA,
          apiKey: envApiKeyA,
          port: envPort,
          updatedAt: new Date(),
          fromEnv: true
        });
      }
      
      if (envHostB && envApiKeyB) {
        configs.push({
          id: 'B',
          host: envHostB,
          apiKey: envApiKeyB,
          port: envPort,
          updatedAt: new Date(),
          fromEnv: true
        });
      }
      
      return configs;
    }
    
    // For backward compatibility, check if legacy environment variables are set
    const envHost = process.env.UNIFI_PROTECT_HOST || '';
    const envUsername = process.env.UNIFI_PROTECT_USERNAME || '';
    const envPassword = process.env.UNIFI_PROTECT_PASSWORD || '';
    
    // If legacy environment variables are set, use them directly
    if (envHost && envUsername && envPassword) {
      return [{
        id: 'legacy',
        host: envHost,
        username: envUsername,
        password: envPassword,
        port: envPort,
        updatedAt: new Date(),
        fromEnv: true
      }];
    }
    
    // If no environment variables are set, log a warning
    console.warn('UniFi Protect environment variables not set. Please configure UNIFI_PROTECT_HOST_A/B and UNIFI_PROTECT_API_KEY_A/B in your environment.');
    
    return [];
  } catch (error) {
    console.error('Error fetching UniFi Protect configuration:', error);
    throw error;
  }
};

// Export the getLatestConfig function for use by other modules
exports.getLatestConfig = getLatestConfig;

/**
 * Get all UniFi Protect cameras
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCameras = async (req, res) => {
  try {
    const configs = await getLatestConfig();
    
    // Check if we have multiple instances configured
    if (configs.length > 1) {
      // Get cameras from all instances
      const cameras = await unifiProtectAPI.getAllCameras();
      res.json(cameras);
    } else if (configs.length === 1) {
      // Get cameras from the single instance
      const instanceId = configs[0].id !== 'legacy' ? configs[0].id : null;
      const cameras = await unifiProtectAPI.getCameras(instanceId);
      res.json(cameras);
    } else {
      // No instances configured
      res.status(404).json({ message: 'UniFi Protect not configured. Please set the required environment variables.' });
    }
  } catch (error) {
    console.error('Controller error fetching UniFi Protect cameras:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect cameras', error: error.message });
  }
};

/**
 * Get UniFi Protect camera details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCameraDetails = async (req, res) => {
  try {
    const configs = await getLatestConfig();
    const cameraId = req.params.id;
    const instanceId = req.query.instance || null;
    
    // If an instance ID is specified, use that instance
    if (instanceId) {
      const cameraDetails = await unifiProtectAPI.getCameraDetails(cameraId, instanceId);
      res.json(cameraDetails);
      return;
    }
    
    // If we have multiple instances and no instance ID is specified, try each instance
    if (configs.length > 1) {
      for (const config of configs) {
        try {
          const cameraDetails = await unifiProtectAPI.getCameraDetails(cameraId, config.id !== 'legacy' ? config.id : null);
          res.json(cameraDetails);
          return;
        } catch (err) {
          // If this instance doesn't have the camera, try the next one
          console.log(`Camera ${cameraId} not found in instance ${config.id}, trying next instance...`);
        }
      }
      // If we get here, the camera wasn't found in any instance
      res.status(404).json({ message: `Camera ${cameraId} not found in any UniFi Protect instance` });
    } else if (configs.length === 1) {
      // Get camera details from the single instance
      const instanceId = configs[0].id !== 'legacy' ? configs[0].id : null;
      const cameraDetails = await unifiProtectAPI.getCameraDetails(cameraId, instanceId);
      res.json(cameraDetails);
    } else {
      // No instances configured
      res.status(404).json({ message: 'UniFi Protect not configured. Please set the required environment variables.' });
    }
  } catch (error) {
    console.error('Controller error fetching UniFi Protect camera details:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect camera details', error: error.message });
  }
};

/**
 * Get UniFi Protect camera snapshot
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCameraSnapshot = async (req, res) => {
  try {
    const configs = await getLatestConfig();
    const cameraId = req.params.id;
    const instanceId = req.query.instance || null;
    
    // If an instance ID is specified, use that instance
    if (instanceId) {
      const snapshot = await unifiProtectAPI.getCameraSnapshot(cameraId, instanceId);
      res.set('Content-Type', 'image/jpeg');
      res.send(snapshot);
      return;
    }
    
    // If we have multiple instances and no instance ID is specified, try each instance
    if (configs.length > 1) {
      for (const config of configs) {
        try {
          const snapshot = await unifiProtectAPI.getCameraSnapshot(cameraId, config.id !== 'legacy' ? config.id : null);
          res.set('Content-Type', 'image/jpeg');
          res.send(snapshot);
          return;
        } catch (err) {
          // If this instance doesn't have the camera, try the next one
          console.log(`Camera ${cameraId} snapshot not found in instance ${config.id}, trying next instance...`);
        }
      }
      // If we get here, the camera wasn't found in any instance
      res.status(404).json({ message: `Camera ${cameraId} not found in any UniFi Protect instance` });
    } else if (configs.length === 1) {
      // Get camera snapshot from the single instance
      const instanceId = configs[0].id !== 'legacy' ? configs[0].id : null;
      const snapshot = await unifiProtectAPI.getCameraSnapshot(cameraId, instanceId);
      res.set('Content-Type', 'image/jpeg');
      res.send(snapshot);
    } else {
      // No instances configured
      res.status(404).json({ message: 'UniFi Protect not configured. Please set the required environment variables.' });
    }
  } catch (error) {
    console.error('Controller error fetching UniFi Protect camera snapshot:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect camera snapshot', error: error.message });
  }
};

/**
 * Get UniFi Protect events
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEvents = async (req, res) => {
  try {
    const configs = await getLatestConfig();
    const instanceId = req.query.instance || null;
    
    // Remove instance from query params for API call
    const params = { ...req.query };
    delete params.instance;
    
    // If an instance ID is specified, use that instance
    if (instanceId) {
      const events = await unifiProtectAPI.getEvents(params, instanceId);
      res.json(events);
      return;
    }
    
    // Check if we have multiple instances configured
    if (configs.length > 1) {
      // Get events from all instances
      const events = await unifiProtectAPI.getAllEvents(params);
      res.json(events);
    } else if (configs.length === 1) {
      // Get events from the single instance
      const instanceId = configs[0].id !== 'legacy' ? configs[0].id : null;
      const events = await unifiProtectAPI.getEvents(params, instanceId);
      res.json(events);
    } else {
      // No instances configured
      res.status(404).json({ message: 'UniFi Protect not configured. Please set the required environment variables.' });
    }
  } catch (error) {
    console.error('Controller error fetching UniFi Protect events:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect events', error: error.message });
  }
};

/**
 * Get UniFi Protect system status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemStatus = async (req, res) => {
  try {
    const configs = await getLatestConfig();
    const instanceId = req.query.instance || null;
    
    // If an instance ID is specified, use that instance
    if (instanceId) {
      const status = await unifiProtectAPI.getSystemStatus(instanceId);
      res.json(status);
      return;
    }
    
    // Check if we have multiple instances configured
    if (configs.length > 1) {
      // Get system status from all instances
      const statuses = await unifiProtectAPI.getAllSystemStatus();
      res.json(statuses);
    } else if (configs.length === 1) {
      // Get system status from the single instance
      const instanceId = configs[0].id !== 'legacy' ? configs[0].id : null;
      const status = await unifiProtectAPI.getSystemStatus(instanceId);
      res.json(status);
    } else {
      // No instances configured
      res.status(404).json({ message: 'UniFi Protect not configured. Please set the required environment variables.' });
    }
  } catch (error) {
    console.error('Controller error fetching UniFi Protect system status:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect system status', error: error.message });
  }
};

/**
 * Save UniFi Protect configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling UniFi Protect configuration request:', error);
    res.status(500).json({ message: 'Error handling UniFi Protect configuration request', error: error.message });
  }
};

/**
 * Get UniFi Protect configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Always prioritize environment variables for authentication
    const envHost = process.env.UNIFI_PROTECT_HOST || '';
    const envUsername = process.env.UNIFI_PROTECT_USERNAME || '';
    const envPassword = process.env.UNIFI_PROTECT_PASSWORD || '';
    const envPort = process.env.UNIFI_PROTECT_PORT || 443;
    
    // If environment variables are set, use them directly
    if (envHost && envUsername && envPassword) {
      // Don't send the actual password back to the client for security
      res.json({
        host: envHost,
        username: envUsername,
        port: envPort,
        configuredAt: new Date(),
        fromEnv: true
      });
      return;
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('UniFi Protect environment variables not set. Falling back to database config (not recommended).');
    
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'UniFi Protect configuration not found. Please set the required environment variables.' });
    }

    // Don't send the actual password back to the client for security
    res.json({
      host: config.host,
      username: config.username,
      port: config.port,
      configuredAt: config.updatedAt,
      fromEnv: !!config.fromEnv
    });
  } catch (error) {
    console.error('Error fetching UniFi Protect configuration:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect configuration', error: error.message });
  }
};

/**
 * Set up UniFi Protect with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling UniFi Protect one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling UniFi Protect one-click setup request', 
      error: error.message 
    });
  }
};

/**
 * Control PTZ camera
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.controlPTZ = async (req, res) => {
  try {
    const configs = await getLatestConfig();
    const cameraId = req.params.id;
    const { action, ...params } = req.body;
    const instanceId = req.query.instance || null;
    
    // If an instance ID is specified, use that instance
    if (instanceId) {
      const result = await unifiProtectAPI.controlPTZ(cameraId, action, params, instanceId);
      res.json(result);
      return;
    }
    
    // If we have multiple instances and no instance ID is specified, try each instance
    if (configs.length > 1) {
      for (const config of configs) {
        try {
          const result = await unifiProtectAPI.controlPTZ(cameraId, action, params, config.id !== 'legacy' ? config.id : null);
          res.json(result);
          return;
        } catch (err) {
          // If this instance doesn't have the camera, try the next one
          console.log(`Camera ${cameraId} PTZ control failed in instance ${config.id}, trying next instance...`);
        }
      }
      // If we get here, the camera wasn't found in any instance
      res.status(404).json({ message: `Camera ${cameraId} not found in any UniFi Protect instance` });
    } else if (configs.length === 1) {
      // Control PTZ for the single instance
      const instanceId = configs[0].id !== 'legacy' ? configs[0].id : null;
      const result = await unifiProtectAPI.controlPTZ(cameraId, action, params, instanceId);
      res.json(result);
    } else {
      // No instances configured
      res.status(404).json({ message: 'UniFi Protect not configured. Please set the required environment variables.' });
    }
  } catch (error) {
    console.error('Controller error controlling UniFi Protect PTZ camera:', error);
    res.status(500).json({ message: 'Error controlling UniFi Protect PTZ camera', error: error.message });
  }
};

/**
 * Get all viewers
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getViewers = async (req, res) => {
  try {
    const configs = await getLatestConfig();
    const instanceId = req.query.instance || null;
    
    // If an instance ID is specified, use that instance
    if (instanceId) {
      const viewers = await unifiProtectAPI.getViewers(instanceId);
      res.json(viewers);
      return;
    }
    
    // Check if we have multiple instances configured
    if (configs.length > 1) {
      // Get viewers from all instances
      const allViewers = [];
      for (const config of configs) {
        try {
          const viewers = await unifiProtectAPI.getViewers(config.id !== 'legacy' ? config.id : null);
          allViewers.push(...viewers);
        } catch (err) {
          console.log(`Failed to get viewers from instance ${config.id}:`, err.message);
        }
      }
      res.json(allViewers);
    } else if (configs.length === 1) {
      // Get viewers from the single instance
      const instanceId = configs[0].id !== 'legacy' ? configs[0].id : null;
      const viewers = await unifiProtectAPI.getViewers(instanceId);
      res.json(viewers);
    } else {
      // No instances configured
      res.status(404).json({ message: 'UniFi Protect not configured. Please set the required environment variables.' });
    }
  } catch (error) {
    console.error('Controller error fetching UniFi Protect viewers:', error);
    res.status(500).json({ message: 'Error fetching UniFi Protect viewers', error: error.message });
  }
};

/**
 * Update viewer settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateViewer = async (req, res) => {
  try {
    const configs = await getLatestConfig();
    const viewerId = req.params.id;
    const settings = req.body;
    const instanceId = req.query.instance || null;
    
    // If an instance ID is specified, use that instance
    if (instanceId) {
      const updatedViewer = await unifiProtectAPI.updateViewer(viewerId, settings, instanceId);
      res.json(updatedViewer);
      return;
    }
    
    // If we have multiple instances and no instance ID is specified, try each instance
    if (configs.length > 1) {
      for (const config of configs) {
        try {
          const updatedViewer = await unifiProtectAPI.updateViewer(viewerId, settings, config.id !== 'legacy' ? config.id : null);
          res.json(updatedViewer);
          return;
        } catch (err) {
          // If this instance doesn't have the viewer, try the next one
          console.log(`Viewer ${viewerId} not found in instance ${config.id}, trying next instance...`);
        }
      }
      // If we get here, the viewer wasn't found in any instance
      res.status(404).json({ message: `Viewer ${viewerId} not found in any UniFi Protect instance` });
    } else if (configs.length === 1) {
      // Update viewer in the single instance
      const instanceId = configs[0].id !== 'legacy' ? configs[0].id : null;
      const updatedViewer = await unifiProtectAPI.updateViewer(viewerId, settings, instanceId);
      res.json(updatedViewer);
    } else {
      // No instances configured
      res.status(404).json({ message: 'UniFi Protect not configured. Please set the required environment variables.' });
    }
  } catch (error) {
    console.error('Controller error updating UniFi Protect viewer:', error);
    res.status(500).json({ message: 'Error updating UniFi Protect viewer', error: error.message });
  }
};