const UnifiAccessAPI = require('../integrations/unifiAccess/unifiAccessAPI');

// Initialize UniFi Access API with environment variables
let unifiAccessAPI = new UnifiAccessAPI();

/**
 * Get all UniFi Access doors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDoors = async (req, res) => {
  try {
    const doors = await unifiAccessAPI.getDoors();
    res.json(doors);
  } catch (error) {
    console.error('Controller error fetching UniFi Access doors:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access doors', error: error.message });
  }
};

/**
 * Get UniFi Access door details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDoorDetails = async (req, res) => {
  try {
    const doorId = req.params.id;
    const doorDetails = await unifiAccessAPI.getDoorDetails(doorId);
    res.json(doorDetails);
  } catch (error) {
    console.error('Controller error fetching UniFi Access door details:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access door details', error: error.message });
  }
};

/**
 * Unlock a door
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.unlockDoor = async (req, res) => {
  try {
    const doorId = req.params.id;
    const result = await unifiAccessAPI.unlockDoor(doorId);
    res.json(result);
  } catch (error) {
    console.error('Controller error unlocking UniFi Access door:', error);
    res.status(500).json({ message: 'Error unlocking UniFi Access door', error: error.message });
  }
};

/**
 * Lock a door - This method has been removed as the lockDoor function does not exist in the UniFi Access API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.lockDoor = async (req, res) => {
  console.error('The lockDoor function does not exist in the UniFi Access API');
  return res.status(400).json({ 
    message: 'The lock door functionality is not supported by the UniFi Access API',
    error: 'UNSUPPORTED_OPERATION'
  });
};

/**
 * Get all UniFi Access access points
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAccessPoints = async (req, res) => {
  try {
    const accessPoints = await unifiAccessAPI.getAccessPoints();
    res.json(accessPoints);
  } catch (error) {
    console.error('Controller error fetching UniFi Access access points:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access access points', error: error.message });
  }
};

/**
 * Get all UniFi Access users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUsers = async (req, res) => {
  try {
    const users = await unifiAccessAPI.getUsers();
    res.json(users);
  } catch (error) {
    console.error('Controller error fetching UniFi Access users:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access users', error: error.message });
  }
};

/**
 * Get UniFi Access events
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEvents = async (req, res) => {
  try {
    const events = await unifiAccessAPI.getEvents(req.query);
    res.json(events);
  } catch (error) {
    console.error('Controller error fetching UniFi Access events:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access events', error: error.message });
  }
};

/**
 * Get UniFi Access system status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemStatus = async (req, res) => {
  try {
    const status = await unifiAccessAPI.getSystemStatus();
    res.json(status);
  } catch (error) {
    console.error('Controller error fetching UniFi Access system status:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access system status', error: error.message });
  }
};

/**
 * Save UniFi Access configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  res.status(400).json({ 
    message: 'UniFi Access configuration is now managed through environment variables. Please set UNIFI_ACCESS_HOST, UNIFI_ACCESS_API_KEY, and optionally UNIFI_ACCESS_PORT in your environment.' 
  });
};

/**
 * Get UniFi Access configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  console.log('UniFi Access getConfig method called');
  
  // Check if environment variables are set
  const host = process.env.UNIFI_ACCESS_HOST;
  const apiKey = process.env.UNIFI_ACCESS_API_KEY;
  const port = process.env.UNIFI_ACCESS_PORT || 443;
  
  if (!host || !apiKey) {
    console.log('UniFi Access configuration not found - missing environment variables');
    return res.status(404).json({ 
      message: 'UniFi Access configuration not found. Please set UNIFI_ACCESS_HOST, UNIFI_ACCESS_API_KEY, and optionally UNIFI_ACCESS_PORT in your environment.',
      error: 'MISSING_ENV_VARS'
    });
  }
  
  // Return configuration status without sensitive information
  console.log('UniFi Access configuration found - returning data');
  res.json({
    host: host,
    port: port,
    configuredAt: new Date(),
    message: 'UniFi Access is configured using environment variables'
  });
};

/**
 * Set up UniFi Access with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  res.status(400).json({ 
    message: 'One-click setup is no longer available. UniFi Access configuration is now managed through environment variables. Please set UNIFI_ACCESS_HOST, UNIFI_ACCESS_API_KEY, and optionally UNIFI_ACCESS_PORT in your environment.',
    environmentVariables: {
      UNIFI_ACCESS_HOST: 'Your UniFi Access host or IP address',
      UNIFI_ACCESS_API_KEY: 'Your UniFi Access API key',
      UNIFI_ACCESS_PORT: 'Your UniFi Access port (default: 443)'
    }
  });
};

/**
 * Get door status
 * @param {string} doorId - Door ID
 * @returns {Promise<Object>} Door status
 */
exports.getDoorStatus = async (doorId) => {
  try {
    // Get door details which includes status information
    const doorDetails = await unifiAccessAPI.getDoorDetails(doorId);
    
    // Extract and return relevant status information
    return {
      id: doorDetails.id,
      name: doorDetails.name,
      status: doorDetails.status,
      locked: doorDetails.locked,
      open: doorDetails.open,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Controller error fetching UniFi Access door status for ${doorId}:`, error);
    throw error;
  }
};
