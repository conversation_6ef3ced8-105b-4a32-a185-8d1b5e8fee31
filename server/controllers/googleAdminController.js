const GoogleAdminAPI = require('../integrations/googleAdmin/googleAdminAPI');
const User = require('../../models/User');
const RoleSettings = require('../../models/RoleSettings');
const path = require('path');
const fs = require('fs');
const mongoose = require('mongoose');

// Check if service account credentials are available (primary authentication method)
const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;
const impersonationEmail = process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL;
const usingServiceAccount = !!(serviceAccountEmail && serviceAccountPrivateKey && impersonationEmail);

// OAuth fallback variables (for backwards compatibility only)
const clientId = process.env.GOOGLE_ADMIN_CLIENT_ID || '';
const clientSecret = process.env.GOOGLE_ADMIN_CLIENT_SECRET || '';
const redirectUri = process.env.GOOGLE_ADMIN_REDIRECT_URI || '';
const tokenPath = path.resolve(process.cwd(), process.env.GOOGLE_ADMIN_TOKEN_PATH || './google-admin-token.json');

// Create a global API instance
let googleAdminAPI = new GoogleAdminAPI(
  clientId,
  clientSecret,
  redirectUri,
  tokenPath,
  null, // No user tokens
  null, // No user ID
  impersonationEmail // Pass the impersonation email for service account authentication
);

// Helper function to ensure API is initialized
const ensureApiInitialized = () => {
  if (usingServiceAccount) {
    if (!serviceAccountEmail || !serviceAccountPrivateKey || !impersonationEmail) {
      throw new Error('Google service account configuration is missing. Please check your environment variables (GOOGLE_SERVICE_ACCOUNT_EMAIL, GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY, and GOOGLE_ADMIN_IMPERSONATION_EMAIL).');
    }
  } else if (!clientId || !clientSecret || !redirectUri) {
    throw new Error('Google Admin configuration is missing. Please check your environment variables.');
  }
  return true;
};

// Helper function to get an API instance with user email for service account impersonation
// or fall back to user tokens if service account is not configured
const getApiWithUser = async (userId) => {
  try {
    // Ensure environment variables are set
    ensureApiInitialized();

    // Get the user
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    if (usingServiceAccount) {
      // Use service account with user impersonation
      if (!user.email) {
        throw new Error('User email is required for service account impersonation');
      }

      const api = new GoogleAdminAPI(
        clientId,
        clientSecret,
        redirectUri,
        tokenPath,
        null, // No user tokens
        userId,
        user.email // Pass the user email for service account impersonation
      );

      await api.initialize();
      return api;
    } else {
      // Fall back to OAuth tokens if service account is not configured
      if (!user.googleAccessToken) {
        throw new Error('User missing Google tokens and service account not configured');
      }

      // Create a new API instance with the user's tokens
      const userTokens = {
        accessToken: user.googleAccessToken,
        refreshToken: user.googleRefreshToken
      };

      const api = new GoogleAdminAPI(
        clientId,
        clientSecret,
        redirectUri,
        tokenPath,
        userTokens,
        userId
      );

      await api.initialize();
      return api;
    }
  } catch (error) {
    console.error('Error creating API with user:', error);
    throw error;
  }
};

// Helper function to get the best available API instance
const getApiInstance = async (req) => {
  let api;

  // Try to use the user's credentials if available
  if (req.user && req.user.id) {
    // Check if user is authenticated via Google
    if (req.user.authType !== 'google') {
      throw new Error('You are not logged in using Google authentication. Google Admin integration will not work. Please log in with your Google account.');
    }
    
    try {
      api = await getApiWithUser(req.user.id);
      return api;
    } catch (userError) {
      console.log('Could not use user credentials, falling back to global config:', userError.message);
      // Fall back to global config if user credentials don't work
    }
  }

  // No user in the request or user credentials failed, use global config
  // Ensure environment variables are set
  ensureApiInitialized();

  // Initialize the API if needed
  try {
    await googleAdminAPI.initialize();
  } catch (error) {
    console.error('Error initializing Google Admin API:', error);
  }

  return googleAdminAPI;
};

// Helper function to check authentication and return appropriate response
const checkAuthentication = (api) => {
  if (!api.isAuthenticated()) {
    // If using service account but not authenticated, it's a configuration error
    if (api.usingServiceAccount) {
      return { 
        authenticated: false,
        status: 401,
        response: {
          message: 'Service account authentication failed. Check your service account configuration.',
          usingServiceAccount: true
        }
      };
    }
    
    // Otherwise, it's a standard OAuth authentication issue
    return {
      authenticated: false,
      status: 401,
      response: {
        message: 'Not authenticated with Google Admin',
        authUrl: api.getAuthUrl(),
        usingServiceAccount: false
      }
    };
  }
  
  return { authenticated: true };
};

/**
 * Save Google Admin configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling Google Admin configuration request:', error);
    res.status(500).json({ message: 'Error handling Google Admin configuration request', error: error.message });
  }
};

/**
 * Get Google Admin configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    ensureApiInitialized();

    // Initialize the API if needed
    try {
      await googleAdminAPI.initialize();
    } catch (error) {
      console.error('Error initializing Google Admin API:', error);
    }

    // Check if environment variables are being used
    const usingEnvVars = {
      serviceAccount: !!(process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL && 
                        process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY &&
                        process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL),
      clientId: !!process.env.GOOGLE_ADMIN_CLIENT_ID,
      clientSecret: !!process.env.GOOGLE_ADMIN_CLIENT_SECRET,
      redirectUri: !!process.env.GOOGLE_ADMIN_REDIRECT_URI,
      tokenPath: !!process.env.GOOGLE_ADMIN_TOKEN_PATH
    };

    // Check if service account is being used
    const usingServiceAccount = googleAdminAPI ? googleAdminAPI.usingServiceAccount : false;
    
    // Don't send the actual client secret back to the client for security
    res.json({
      clientId: process.env.GOOGLE_ADMIN_CLIENT_ID,
      redirectUri: process.env.GOOGLE_ADMIN_REDIRECT_URI,
      tokenPath: process.env.GOOGLE_ADMIN_TOKEN_PATH,
      configuredAt: new Date(),
      isAuthenticated: googleAdminAPI ? googleAdminAPI.isAuthenticated() : false,
      usingEnvVars,
      usingServiceAccount,
      serviceAccountEmail: usingServiceAccount ? process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL : null,
      impersonationEmail: usingServiceAccount ? process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL : null
    });
  } catch (error) {
    console.error('Error fetching Google Admin configuration:', error);
    res.status(500).json({ message: 'Error fetching Google Admin configuration', error: error.message });
  }
};

/**
 * Get authentication URL
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAuthUrl = async (req, res) => {
  try {
    ensureApiInitialized();

    const authUrl = googleAdminAPI.getAuthUrl();
    res.json({ authUrl });
  } catch (error) {
    console.error('Error generating Google Admin auth URL:', error);
    res.status(500).json({ message: 'Error generating Google Admin auth URL', error: error.message });
  }
};

/**
 * Handle OAuth2 callback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.handleCallback = async (req, res) => {
  try {
    const { code } = req.query;

    if (!code) {
      return res.status(400).json({ message: 'Authorization code is required' });
    }

    ensureApiInitialized();

    await googleAdminAPI.getToken(code);
    res.json({ message: 'Google Admin authentication successful' });
  } catch (error) {
    console.error('Error handling Google Admin callback:', error);
    res.status(500).json({ message: 'Error handling Google Admin callback', error: error.message });
  }
};

/**
 * List users in the domain
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.listUsers = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    
    // Check authentication
    const authCheck = checkAuthentication(api);
    if (!authCheck.authenticated) {
      return res.status(authCheck.status).json(authCheck.response);
    }

    const options = req.query;
    const users = await api.listUsers(options);
    res.json(users);
  } catch (error) {
    console.error('Error listing users from Google Admin:', error);

    if (error.message === 'Google Admin configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error listing users from Google Admin', error: error.message });
  }
};

/**
 * Get user information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUser = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    
    // Check authentication
    const authCheck = checkAuthentication(api);
    if (!authCheck.authenticated) {
      return res.status(authCheck.status).json(authCheck.response);
    }

    const { userKey } = req.params;

    if (!userKey) {
      return res.status(400).json({ message: 'User key is required' });
    }

    const user = await api.getUser(userKey);
    res.json(user);
  } catch (error) {
    console.error('Error getting user from Google Admin:', error);

    if (error.message === 'Google Admin configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error getting user from Google Admin', error: error.message });
  }
};

/**
 * Create a new user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createUser = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    
    // Check authentication
    const authCheck = checkAuthentication(api);
    if (!authCheck.authenticated) {
      return res.status(authCheck.status).json(authCheck.response);
    }

    const userData = req.body;

    if (!userData || !userData.primaryEmail || !userData.name || !userData.password) {
      return res.status(400).json({ message: 'User data is incomplete. Required fields: primaryEmail, name, password' });
    }

    const user = await api.createUser(userData);
    res.json(user);
  } catch (error) {
    console.error('Error creating user in Google Admin:', error);

    if (error.message === 'Google Admin configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error creating user in Google Admin', error: error.message });
  }
};

/**
 * Update user information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateUser = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    
    // Check authentication
    const authCheck = checkAuthentication(api);
    if (!authCheck.authenticated) {
      return res.status(authCheck.status).json(authCheck.response);
    }

    const { userKey } = req.params;
    const userData = req.body;

    if (!userKey) {
      return res.status(400).json({ message: 'User key is required' });
    }

    if (!userData) {
      return res.status(400).json({ message: 'User data is required' });
    }

    const user = await api.updateUser(userKey, userData);
    res.json(user);
  } catch (error) {
    console.error('Error updating user in Google Admin:', error);

    if (error.message === 'Google Admin configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error updating user in Google Admin', error: error.message });
  }
};

/**
 * Delete a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteUser = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    
    // Check authentication
    const authCheck = checkAuthentication(api);
    if (!authCheck.authenticated) {
      return res.status(authCheck.status).json(authCheck.response);
    }

    const { userKey } = req.params;

    if (!userKey) {
      return res.status(400).json({ message: 'User key is required' });
    }

    await api.deleteUser(userKey);
    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user in Google Admin:', error);

    if (error.message === 'Google Admin configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error deleting user in Google Admin', error: error.message });
  }
};

/**
 * List groups in the domain
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.listGroups = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    
    // Check authentication
    const authCheck = checkAuthentication(api);
    if (!authCheck.authenticated) {
      return res.status(authCheck.status).json(authCheck.response);
    }

    const options = req.query;
    const groups = await api.listGroups(options);
    res.json(groups);
  } catch (error) {
    console.error('Error listing groups from Google Admin:', error);

    if (error.message === 'Google Admin configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error listing groups from Google Admin', error: error.message });
  }
};

/**
 * Add user to a group
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addUserToGroup = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    
    // Check authentication
    const authCheck = checkAuthentication(api);
    if (!authCheck.authenticated) {
      return res.status(authCheck.status).json(authCheck.response);
    }

    const { groupKey } = req.params;
    const { userEmail } = req.body;

    if (!groupKey) {
      return res.status(400).json({ message: 'Group key is required' });
    }

    if (!userEmail) {
      return res.status(400).json({ message: 'User email is required' });
    }

    const member = await api.addUserToGroup(groupKey, userEmail);
    res.json(member);
  } catch (error) {
    console.error('Error adding user to group in Google Admin:', error);

    if (error.message === 'Google Admin configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error adding user to group in Google Admin', error: error.message });
  }
};

/**
 * Remove user from a group
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.removeUserFromGroup = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    
    // Check authentication
    const authCheck = checkAuthentication(api);
    if (!authCheck.authenticated) {
      return res.status(authCheck.status).json(authCheck.response);
    }

    const { groupKey, memberKey } = req.params;

    if (!groupKey || !memberKey) {
      return res.status(400).json({ message: 'Group key and member key are required' });
    }

    await api.removeUserFromGroup(groupKey, memberKey);
    res.json({ message: 'User removed from group successfully' });
  } catch (error) {
    console.error('Error removing user from group in Google Admin:', error);

    if (error.message === 'Google Admin configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error removing user from group in Google Admin', error: error.message });
  }
};

/**
 * Sync all users from Google Workspace to the system
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.syncAllUsers = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    
    // Check authentication
    const authCheck = checkAuthentication(api);
    if (!authCheck.authenticated) {
      return res.status(authCheck.status).json(authCheck.response);
    }

    // Get all users from Google Workspace
    const googleUsers = await api.listUsers();

    if (!googleUsers || !Array.isArray(googleUsers)) {
      return res.status(500).json({ message: 'Failed to retrieve users from Google Workspace' });
    }

    // Import required models
    const Group = require('../../models/Group');
    const Team = require('../../models/Team');

    const results = {
      total: googleUsers.length,
      created: 0,
      existing: 0,
      errors: 0,
      groupsProcessed: 0,
      teamsProcessed: 0,
      errorDetails: []
    };

    // Process each Google user
    for (const googleUser of googleUsers) {
      try {
        // Skip suspended or non-active users
        if (googleUser.suspended || !googleUser.isAdmin && googleUser.archived) {
          continue;
        }

        // Check if user already exists in our system
        const existingUser = await User.findOne({ 
          $or: [
            { googleId: googleUser.id },
            { email: googleUser.primaryEmail }
          ]
        });

        let user;
        if (existingUser) {
          // User already exists, update if needed
          user = existingUser;
          results.existing++;
        } else {
          // Create new user
          // Get default role settings from database or use 'user' as fallback
          let defaultRole = 'user';
          let googleGroupsRoleMapping = null;
          
          try {
            // Get settings from database
            const settings = await RoleSettings.getCurrentSettings();
            defaultRole = settings.defaultRoleGoogleUsers;
            
            // Convert array of objects to object with groupEmail as key and roleName as value
            if (settings.googleGroupsRoleMapping && settings.googleGroupsRoleMapping.length > 0) {
              googleGroupsRoleMapping = {};
              settings.googleGroupsRoleMapping.forEach(mapping => {
                googleGroupsRoleMapping[mapping.groupEmail] = mapping.roleName;
              });
            }
          } catch (error) {
            console.error('Error getting role settings, using defaults:', error);
            // Continue with default role if there's an error
          }
          
          let roles = [defaultRole];

          // If Google Groups role mapping is configured, check user's groups and assign roles
          if (googleGroupsRoleMapping) {
            try {
              // Get user's email
              const userEmail = googleUser.primaryEmail;
              
              // Get all groups
              const groups = await api.listGroups();
              
              // For each group in the mapping
              for (const [groupEmail, role] of Object.entries(googleGroupsRoleMapping)) {
                // Find the group
                const group = groups.find(g => g.email.toLowerCase() === groupEmail.toLowerCase());
                
                if (group) {
                  try {
                    // Check if user is a member of the group
                    const isMember = await api.isUserMemberOfGroup(userEmail, group.email);
                    
                    if (isMember) {
                      // Add the role if not already in the roles array
                      if (!roles.includes(role)) {
                        roles.push(role);
                      }
                    }
                  } catch (groupCheckError) {
                    console.error(`Error checking if user is member of group ${group.email}:`, groupCheckError);
                  }
                }
              }
            } catch (error) {
              console.error('Error checking Google Groups for role assignment:', error);
              // Continue with default role if there's an error
            }
          }

          const newUser = new User({
            googleId: googleUser.id,
            name: googleUser.name.fullName,
            email: googleUser.primaryEmail,
            avatar: googleUser.thumbnailPhotoUrl,
            roles: roles,
            // Don't set tokens as they're only available during OAuth flow
          });

          await newUser.save();
          user = newUser;
          results.created++;
        }

        // Now sync the user's groups and teams from Google
        try {
          // Get user's groups from Google Admin
          const googleGroups = await api.listUserGroups(googleUser.primaryEmail);
          
          if (googleGroups && googleGroups.length > 0) {
            // Clear existing groups
            user.groups = [];
            
            // Process each Google group
            for (const googleGroup of googleGroups) {
              // Find or create the group in our database
              let group = await Group.findOne({ name: googleGroup.name });
              
              if (!group) {
                // Create a new group if it doesn't exist
                group = new Group({
                  name: googleGroup.name,
                  description: googleGroup.description || `Google group: ${googleGroup.name}`,
                  // No owner is set as this is from Google
                });
                await group.save();
              }
              
              // Add the group to the user's groups
              if (!user.groups.includes(group._id)) {
                user.groups.push(group._id);
              }
              
              // Check if this group should be treated as a team based on naming convention
              // For example, if the group name starts with "Team-"
              if (googleGroup.name.startsWith('Team-')) {
                // Find or create the team
                const teamName = googleGroup.name.substring(5); // Remove "Team-" prefix
                let team = await Team.findOne({ name: teamName });
                
                if (!team) {
                  // Create a new team if it doesn't exist
                  team = new Team({
                    name: teamName,
                    description: googleGroup.description || `Team derived from Google group: ${googleGroup.name}`,
                    // No leader is set as this is from Google
                  });
                  await team.save();
                  results.teamsProcessed++;
                }
                
                // Add the team to the user's teams if not already there
                if (!user.teams) {
                  user.teams = [];
                }
                if (!user.teams.includes(team._id)) {
                  user.teams.push(team._id);
                }
              }
              
              results.groupsProcessed++;
            }
            
            await user.save();
          }
        } catch (groupError) {
          console.error(`Error syncing groups for user ${googleUser.primaryEmail}:`, groupError);
          results.errorDetails.push({
            email: googleUser.primaryEmail,
            error: `Error syncing groups: ${groupError.message}`
          });
        }
      } catch (userError) {
        console.error(`Error processing user ${googleUser.primaryEmail}:`, userError);
        results.errors++;
        results.errorDetails.push({
          email: googleUser.primaryEmail,
          error: userError.message
        });
      }
    }

    res.json({
      message: 'Google Workspace users, groups, and teams sync completed',
      results
    });
  } catch (error) {
    console.error('Error syncing users from Google Workspace:', error);

    if (error.message === 'Google Admin configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ 
      message: 'Error syncing users from Google Workspace', 
      error: error.message 
    });
  }
};

/**
 * Set up Google Admin with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling Google Admin one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling Google Admin one-click setup request', 
      error: error.message 
    });
  }
};
