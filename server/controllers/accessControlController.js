/**
 * Access Control Controller
 * 
 * This controller provides a unified interface for managing access control systems
 * including Unifi Access and Lenel S2 NetBox.
 */

const unifiAccessController = require('./unifiAccessController');
const lenelS2NetBoxController = require('./lenelS2NetBoxController');

/**
 * Get configuration status for all access control systems
 */
exports.getConfigStatus = async (req, res) => {
  try {
    // Create a mock request and response for each controller
    const unifiReq = { ...req };
    const unifiRes = {
      json: (data) => data,
      status: (code) => ({ 
        json: (data) => {
          // For successful responses, return the data
          if (code >= 200 && code < 300) {
            return data;
          }
          // For error responses, return null to indicate not configured
          return null;
        }
      })
    };
    
    const lenelReq = { ...req };
    const lenelRes = {
      json: (data) => data,
      status: (code) => ({ 
        json: (data) => {
          // For successful responses, return the data
          if (code >= 200 && code < 300) {
            return data;
          }
          // For error responses, return null to indicate not configured
          return null;
        }
      })
    };
    
    // Check if environment variables are set for Unifi Access
    const unifiHost = process.env.UNIFI_ACCESS_HOST;
    const unifiApiKey = process.env.UNIFI_ACCESS_API_KEY;
    
    // Check if environment variables are set for Lenel S2 NetBox
    const lenelHost = process.env.LENEL_S2_NETBOX_HOST;
    const lenelUsername = process.env.LENEL_S2_NETBOX_USERNAME;
    const lenelPassword = process.env.LENEL_S2_NETBOX_PASSWORD;
    
    // Get configuration status from both systems
    let unifiConfig = null;
    let lenelConfig = null;
    
    // If Unifi Access environment variables are set, try to get config
    if (unifiHost && unifiApiKey) {
      try {
        unifiConfig = await unifiAccessController.getConfig(unifiReq, unifiRes);
        // If we got a response but it's not valid, create a basic config object
        if (!unifiConfig) {
          unifiConfig = {
            host: unifiHost,
            port: process.env.UNIFI_ACCESS_PORT || 443,
            configuredAt: new Date(),
            message: 'UniFi Access is configured using environment variables'
          };
        }
      } catch (error) {
        console.error('Error getting Unifi Access config:', error);
        // Create a basic config object even if there was an error
        unifiConfig = {
          host: unifiHost,
          port: process.env.UNIFI_ACCESS_PORT || 443,
          configuredAt: new Date(),
          message: 'UniFi Access is configured using environment variables'
        };
      }
    }
    
    // If Lenel S2 NetBox environment variables are set, try to get config
    if (lenelHost && lenelUsername && lenelPassword) {
      try {
        lenelConfig = await lenelS2NetBoxController.getConfig(lenelReq, lenelRes);
        // If we got a response but it's not valid, create a basic config object
        if (!lenelConfig) {
          lenelConfig = {
            host: lenelHost,
            username: lenelUsername,
            port: process.env.LENEL_S2_NETBOX_PORT || 443,
            localNetwork: process.env.LENEL_S2_NETBOX_LOCAL_NETWORK === 'true',
            configuredAt: new Date()
          };
        }
      } catch (error) {
        console.error('Error getting Lenel S2 NetBox config:', error);
        // Create a basic config object even if there was an error
        lenelConfig = {
          host: lenelHost,
          username: lenelUsername,
          port: process.env.LENEL_S2_NETBOX_PORT || 443,
          localNetwork: process.env.LENEL_S2_NETBOX_LOCAL_NETWORK === 'true',
          configuredAt: new Date()
        };
      }
    }
    
    // Return combined configuration status
    return res.json({
      unifiAccess: unifiConfig,
      lenelS2NetBox: lenelConfig
    });
  } catch (error) {
    console.error('Error getting access control configuration status:', error);
    return res.status(500).json({ error: 'Failed to get access control configuration status' });
  }
};

/**
 * Get all doors/portals from all access control systems
 */
exports.getAllDoors = async (req, res) => {
  try {
    // Create mock request and response objects
    const unifiReq = { ...req };
    const unifiRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    const lenelReq = { ...req };
    const lenelRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    // Get doors from both systems
    let unifiDoors = [];
    let lenelPortals = [];
    
    try {
      unifiDoors = await unifiAccessController.getDoors(unifiReq, unifiRes) || [];
      // Add system identifier to each door
      unifiDoors = unifiDoors.map(door => ({
        ...door,
        system: 'unifi-access',
        systemName: 'Unifi Access'
      }));
    } catch (error) {
      console.error('Error getting Unifi Access doors:', error);
    }
    
    try {
      lenelPortals = await lenelS2NetBoxController.getPortals(lenelReq, lenelRes) || [];
      // Add system identifier to each portal
      lenelPortals = lenelPortals.map(portal => ({
        ...portal,
        system: 'lenel-s2-netbox',
        systemName: 'Lenel S2 NetBox'
      }));
    } catch (error) {
      console.error('Error getting Lenel S2 NetBox portals:', error);
    }
    
    // Combine and return all doors/portals
    const allDoors = [...unifiDoors, ...lenelPortals];
    
    return res.json(allDoors);
  } catch (error) {
    console.error('Error getting all doors/portals:', error);
    return res.status(500).json({ error: 'Failed to get all doors/portals' });
  }
};

/**
 * Get all users from all access control systems
 * Supports pagination with page and limit parameters
 */
exports.getAllUsers = async (req, res) => {
  try {
    // Extract pagination parameters from request query
    const page = parseInt(req.query.page) || 0;
    const limit = parseInt(req.query.limit) || 10;
    
    // Create mock request and response objects
    const unifiReq = { ...req };
    const unifiRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    const lenelReq = { ...req };
    const lenelRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    // Get users from both systems
    let unifiUsers = [];
    let lenelCardholders = [];
    
    try {
      unifiUsers = await unifiAccessController.getUsers(unifiReq, unifiRes) || [];
      // Add system identifier to each user
      unifiUsers = unifiUsers.map(user => ({
        ...user,
        system: 'unifi-access',
        systemName: 'Unifi Access'
      }));
    } catch (error) {
      console.error('Error getting Unifi Access users:', error);
    }
    
    try {
      lenelCardholders = await lenelS2NetBoxController.getCardholders(lenelReq, lenelRes) || [];
      // Add system identifier to each cardholder
      lenelCardholders = lenelCardholders.map(cardholder => ({
        ...cardholder,
        system: 'lenel-s2-netbox',
        systemName: 'Lenel S2 NetBox'
      }));
    } catch (error) {
      console.error('Error getting Lenel S2 NetBox cardholders:', error);
    }
    
    // Attempt to match users between systems based on name or email
    const matchedUsers = [];
    const processedUnifiUsers = new Set();
    const processedLenelCardholders = new Set();
    
    // First pass: try to match users by email
    unifiUsers.forEach(unifiUser => {
      if (unifiUser.email) {
        const matchingCardholder = lenelCardholders.find(
          cardholder => cardholder.email && cardholder.email.toLowerCase() === unifiUser.email.toLowerCase()
        );
        
        if (matchingCardholder) {
          matchedUsers.push({
            id: `${unifiUser.id}|${matchingCardholder.id}`,
            name: unifiUser.name || matchingCardholder.name,
            firstName: unifiUser.firstName || matchingCardholder.firstName,
            lastName: unifiUser.lastName || matchingCardholder.lastName,
            email: unifiUser.email,
            phone: unifiUser.phone || matchingCardholder.phone,
            department: unifiUser.department || matchingCardholder.department,
            title: unifiUser.title || matchingCardholder.title,
            systems: ['unifi-access', 'lenel-s2-netbox'],
            unifiAccessId: unifiUser.id,
            lenelS2NetBoxId: matchingCardholder.id,
            unifiAccessData: unifiUser,
            lenelS2NetBoxData: matchingCardholder
          });
          
          processedUnifiUsers.add(unifiUser.id);
          processedLenelCardholders.add(matchingCardholder.id);
        }
      }
    });
    
    // Second pass: try to match users by name
    unifiUsers.forEach(unifiUser => {
      if (!processedUnifiUsers.has(unifiUser.id) && unifiUser.name) {
        const matchingCardholder = lenelCardholders.find(
          cardholder => !processedLenelCardholders.has(cardholder.id) && 
            cardholder.name && cardholder.name.toLowerCase() === unifiUser.name.toLowerCase()
        );
        
        if (matchingCardholder) {
          matchedUsers.push({
            id: `${unifiUser.id}|${matchingCardholder.id}`,
            name: unifiUser.name,
            firstName: unifiUser.firstName || matchingCardholder.firstName,
            lastName: unifiUser.lastName || matchingCardholder.lastName,
            email: unifiUser.email || matchingCardholder.email,
            phone: unifiUser.phone || matchingCardholder.phone,
            department: unifiUser.department || matchingCardholder.department,
            title: unifiUser.title || matchingCardholder.title,
            systems: ['unifi-access', 'lenel-s2-netbox'],
            unifiAccessId: unifiUser.id,
            lenelS2NetBoxId: matchingCardholder.id,
            unifiAccessData: unifiUser,
            lenelS2NetBoxData: matchingCardholder
          });
          
          processedUnifiUsers.add(unifiUser.id);
          processedLenelCardholders.add(matchingCardholder.id);
        }
      }
    });
    
    // Add remaining unmatched users
    unifiUsers.forEach(unifiUser => {
      if (!processedUnifiUsers.has(unifiUser.id)) {
        matchedUsers.push({
          id: unifiUser.id,
          name: unifiUser.name,
          firstName: unifiUser.firstName,
          lastName: unifiUser.lastName,
          email: unifiUser.email,
          phone: unifiUser.phone,
          department: unifiUser.department,
          title: unifiUser.title,
          systems: ['unifi-access'],
          unifiAccessId: unifiUser.id,
          unifiAccessData: unifiUser
        });
      }
    });
    
    lenelCardholders.forEach(cardholder => {
      if (!processedLenelCardholders.has(cardholder.id)) {
        matchedUsers.push({
          id: cardholder.id,
          name: cardholder.name,
          firstName: cardholder.firstName,
          lastName: cardholder.lastName,
          email: cardholder.email,
          phone: cardholder.phone,
          department: cardholder.department,
          title: cardholder.title,
          systems: ['lenel-s2-netbox'],
          lenelS2NetBoxId: cardholder.id,
          lenelS2NetBoxData: cardholder
        });
      }
    });
    
    // Apply search filter if provided
    let filteredUsers = matchedUsers;
    const search = req.query.search;
    const system = req.query.system;
    
    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        (user.name && user.name.toLowerCase().includes(searchLower)) ||
        (user.firstName && user.firstName.toLowerCase().includes(searchLower)) ||
        (user.lastName && user.lastName.toLowerCase().includes(searchLower)) ||
        (user.email && user.email.toLowerCase().includes(searchLower)) ||
        (user.department && user.department.toLowerCase().includes(searchLower))
      );
    }
    
    // Apply system filter if provided
    if (system) {
      filteredUsers = filteredUsers.filter(user => 
        user.systems && user.systems.includes(system)
      );
    }
    
    // Apply pagination
    const totalUsers = filteredUsers.length;
    const startIndex = page * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    // Return paginated results with metadata
    return res.json({
      users: paginatedUsers,
      pagination: {
        total: totalUsers,
        page: page,
        limit: limit,
        totalPages: Math.ceil(totalUsers / limit)
      }
    });
  } catch (error) {
    console.error('Error getting all users:', error);
    return res.status(500).json({ error: 'Failed to get all users' });
  }
};

/**
 * Create a new user in selected access control systems
 */
exports.createUser = async (req, res) => {
  try {
    const { 
      firstName, 
      lastName, 
      email, 
      phone, 
      department, 
      title, 
      systems,
      accessLevels,
      cards
    } = req.body;
    
    if (!firstName || !lastName) {
      return res.status(400).json({ error: 'First name and last name are required' });
    }
    
    if (!systems || !Array.isArray(systems) || systems.length === 0) {
      return res.status(400).json({ error: 'At least one system must be selected' });
    }
    
    const results = {
      unifiAccess: null,
      lenelS2NetBox: null
    };
    
    // Create user in Unifi Access if selected
    if (systems.includes('unifi-access')) {
      try {
        const unifiReq = { 
          ...req,
          body: {
            firstName,
            lastName,
            email,
            phone,
            department,
            title,
            accessLevels: accessLevels?.unifiAccess || []
          }
        };
        
        const unifiRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Unifi Access controller to create user
        results.unifiAccess = await unifiAccessController.createUser(unifiReq, unifiRes);
      } catch (error) {
        console.error('Error creating user in Unifi Access:', error);
        results.unifiAccess = { error: error.message || 'Failed to create user in Unifi Access' };
      }
    }
    
    // Create user in Lenel S2 NetBox if selected
    if (systems.includes('lenel-s2-netbox')) {
      try {
        const lenelReq = { 
          ...req,
          body: {
            firstName,
            lastName,
            email,
            phone,
            department,
            title,
            accessLevels: accessLevels?.lenelS2NetBox || [],
            cards: cards || []
          }
        };
        
        const lenelRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Lenel S2 NetBox controller to create user
        results.lenelS2NetBox = await lenelS2NetBoxController.createCardholder(lenelReq, lenelRes);
      } catch (error) {
        console.error('Error creating user in Lenel S2 NetBox:', error);
        results.lenelS2NetBox = { error: error.message || 'Failed to create user in Lenel S2 NetBox' };
      }
    }
    
    // Check if both operations failed
    if (
      (systems.includes('unifi-access') && results.unifiAccess?.error) &&
      (systems.includes('lenel-s2-netbox') && results.lenelS2NetBox?.error)
    ) {
      return res.status(500).json({
        error: 'Failed to create user in all selected systems',
        details: results
      });
    }
    
    // Return results
    return res.status(201).json({
      message: 'User created successfully in at least one system',
      results
    });
  } catch (error) {
    console.error('Error creating user:', error);
    return res.status(500).json({ error: 'Failed to create user' });
  }
};

/**
 * Get a user by ID from all access control systems
 */
exports.getUserById = async (req, res) => {
  try {
    const userId = req.params.id;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    // Parse the composite ID if it contains system-specific IDs
    let unifiAccessId = null;
    let lenelS2NetBoxId = null;
    
    if (userId.includes('|')) {
      const [unifiId, lenelId] = userId.split('|');
      unifiAccessId = unifiId;
      lenelS2NetBoxId = lenelId;
    } else {
      // If it's a single ID, we need to determine which system it belongs to
      // For now, we'll try both systems
      unifiAccessId = userId;
      lenelS2NetBoxId = userId;
    }
    
    const results = {
      unifiAccess: null,
      lenelS2NetBox: null
    };
    
    // Get user from Unifi Access if ID is available
    if (unifiAccessId) {
      try {
        const unifiReq = { 
          ...req,
          params: { id: unifiAccessId }
        };
        
        const unifiRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Unifi Access controller to get user
        results.unifiAccess = await unifiAccessController.getUserById(unifiReq, unifiRes);
      } catch (error) {
        console.error('Error getting user from Unifi Access:', error);
        results.unifiAccess = { error: error.message || 'Failed to get user from Unifi Access' };
      }
    }
    
    // Get user from Lenel S2 NetBox if ID is available
    if (lenelS2NetBoxId) {
      try {
        const lenelReq = { 
          ...req,
          params: { id: lenelS2NetBoxId }
        };
        
        const lenelRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Lenel S2 NetBox controller to get user
        results.lenelS2NetBox = await lenelS2NetBoxController.getCardholderById(lenelReq, lenelRes);
      } catch (error) {
        console.error('Error getting user from Lenel S2 NetBox:', error);
        results.lenelS2NetBox = { error: error.message || 'Failed to get user from Lenel S2 NetBox' };
      }
    }
    
    // Check if both operations failed
    if (
      (!results.unifiAccess || results.unifiAccess.error) &&
      (!results.lenelS2NetBox || results.lenelS2NetBox.error)
    ) {
      return res.status(404).json({
        error: 'User not found in any system',
        details: results
      });
    }
    
    // Combine user data from both systems
    const userData = {
      id: userId,
      systems: []
    };
    
    if (results.unifiAccess && !results.unifiAccess.error) {
      userData.firstName = results.unifiAccess.firstName || userData.firstName;
      userData.lastName = results.unifiAccess.lastName || userData.lastName;
      userData.email = results.unifiAccess.email || userData.email;
      userData.phone = results.unifiAccess.phone || userData.phone;
      userData.department = results.unifiAccess.department || userData.department;
      userData.title = results.unifiAccess.title || userData.title;
      userData.systems.push('unifi-access');
      userData.unifiAccessId = unifiAccessId;
      userData.unifiAccessData = results.unifiAccess;
    }
    
    if (results.lenelS2NetBox && !results.lenelS2NetBox.error) {
      userData.firstName = userData.firstName || results.lenelS2NetBox.firstName;
      userData.lastName = userData.lastName || results.lenelS2NetBox.lastName;
      userData.email = userData.email || results.lenelS2NetBox.email;
      userData.phone = userData.phone || results.lenelS2NetBox.phone;
      userData.department = userData.department || results.lenelS2NetBox.department;
      userData.title = userData.title || results.lenelS2NetBox.title;
      userData.systems.push('lenel-s2-netbox');
      userData.lenelS2NetBoxId = lenelS2NetBoxId;
      userData.lenelS2NetBoxData = results.lenelS2NetBox;
      userData.cards = results.lenelS2NetBox.cards || [];
    }
    
    // Return the combined user data
    return res.json(userData);
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return res.status(500).json({ error: 'Failed to get user by ID' });
  }
};

/**
 * Update a user in all provisioned systems
 */
exports.updateUser = async (req, res) => {
  try {
    const userId = req.params.id;
    const { 
      firstName, 
      lastName, 
      email, 
      phone, 
      department, 
      title, 
      systems,
      accessLevels,
      cards
    } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    if (!firstName || !lastName) {
      return res.status(400).json({ error: 'First name and last name are required' });
    }
    
    // Parse the composite ID if it contains system-specific IDs
    let unifiAccessId = null;
    let lenelS2NetBoxId = null;
    
    if (userId.includes('|')) {
      const [unifiId, lenelId] = userId.split('|');
      unifiAccessId = unifiId;
      lenelS2NetBoxId = lenelId;
    } else {
      // If it's a single ID, we need to determine which system it belongs to
      // For now, we'll use the systems array to determine which systems to update
      if (systems.includes('unifi-access')) {
        unifiAccessId = userId;
      }
      if (systems.includes('lenel-s2-netbox')) {
        lenelS2NetBoxId = userId;
      }
    }
    
    const results = {
      unifiAccess: null,
      lenelS2NetBox: null
    };
    
    // Update user in Unifi Access if ID is available and system is selected
    if (unifiAccessId && systems.includes('unifi-access')) {
      try {
        const unifiReq = { 
          ...req,
          params: { id: unifiAccessId },
          body: {
            firstName,
            lastName,
            email,
            phone,
            department,
            title,
            accessLevels: accessLevels?.unifiAccess || []
          }
        };
        
        const unifiRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Unifi Access controller to update user
        results.unifiAccess = await unifiAccessController.updateUser(unifiReq, unifiRes);
      } catch (error) {
        console.error('Error updating user in Unifi Access:', error);
        results.unifiAccess = { error: error.message || 'Failed to update user in Unifi Access' };
      }
    }
    
    // Update user in Lenel S2 NetBox if ID is available and system is selected
    if (lenelS2NetBoxId && systems.includes('lenel-s2-netbox')) {
      try {
        const lenelReq = { 
          ...req,
          params: { id: lenelS2NetBoxId },
          body: {
            firstName,
            lastName,
            email,
            phone,
            department,
            title,
            accessLevels: accessLevels?.lenelS2NetBox || [],
            cards: cards || []
          }
        };
        
        const lenelRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Lenel S2 NetBox controller to update user
        results.lenelS2NetBox = await lenelS2NetBoxController.updateCardholder(lenelReq, lenelRes);
      } catch (error) {
        console.error('Error updating user in Lenel S2 NetBox:', error);
        results.lenelS2NetBox = { error: error.message || 'Failed to update user in Lenel S2 NetBox' };
      }
    }
    
    // Check if all operations failed
    if (
      (systems.includes('unifi-access') && results.unifiAccess?.error) &&
      (systems.includes('lenel-s2-netbox') && results.lenelS2NetBox?.error)
    ) {
      return res.status(500).json({
        error: 'Failed to update user in all selected systems',
        details: results
      });
    }
    
    // Return results
    return res.json({
      message: 'User updated successfully in at least one system',
      results
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return res.status(500).json({ error: 'Failed to update user' });
  }
};

/**
 * Delete a user from all provisioned systems
 */
exports.deleteUser = async (req, res) => {
  try {
    const userId = req.params.id;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    // Parse the composite ID if it contains system-specific IDs
    let unifiAccessId = null;
    let lenelS2NetBoxId = null;
    
    if (userId.includes('|')) {
      const [unifiId, lenelId] = userId.split('|');
      unifiAccessId = unifiId;
      lenelS2NetBoxId = lenelId;
    } else {
      // If it's a single ID, we need to determine which system it belongs to
      // For now, we'll try both systems
      unifiAccessId = userId;
      lenelS2NetBoxId = userId;
    }
    
    const results = {
      unifiAccess: null,
      lenelS2NetBox: null
    };
    
    // Delete user from Unifi Access if ID is available
    if (unifiAccessId) {
      try {
        const unifiReq = { 
          ...req,
          params: { id: unifiAccessId }
        };
        
        const unifiRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Unifi Access controller to delete user
        results.unifiAccess = await unifiAccessController.deleteUser(unifiReq, unifiRes);
      } catch (error) {
        console.error('Error deleting user from Unifi Access:', error);
        results.unifiAccess = { error: error.message || 'Failed to delete user from Unifi Access' };
      }
    }
    
    // Delete user from Lenel S2 NetBox if ID is available
    if (lenelS2NetBoxId) {
      try {
        const lenelReq = { 
          ...req,
          params: { id: lenelS2NetBoxId }
        };
        
        const lenelRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Lenel S2 NetBox controller to delete user
        results.lenelS2NetBox = await lenelS2NetBoxController.deleteCardholder(lenelReq, lenelRes);
      } catch (error) {
        console.error('Error deleting user from Lenel S2 NetBox:', error);
        results.lenelS2NetBox = { error: error.message || 'Failed to delete user from Lenel S2 NetBox' };
      }
    }
    
    // Check if both operations failed
    if (
      (unifiAccessId && results.unifiAccess?.error) &&
      (lenelS2NetBoxId && results.lenelS2NetBox?.error)
    ) {
      return res.status(500).json({
        error: 'Failed to delete user from all systems',
        details: results
      });
    }
    
    // Return results
    return res.json({
      message: 'User deleted successfully from at least one system',
      results
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    return res.status(500).json({ error: 'Failed to delete user' });
  }
};

/**
 * Enable or disable a user across all provisioned systems
 */
exports.updateUserStatus = async (req, res) => {
  try {
    const userId = req.params.id;
    const { enabled } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    if (enabled === undefined) {
      return res.status(400).json({ error: 'Enabled status is required' });
    }
    
    // Parse the composite ID if it contains system-specific IDs
    let unifiAccessId = null;
    let lenelS2NetBoxId = null;
    
    if (userId.includes('|')) {
      const [unifiId, lenelId] = userId.split('|');
      unifiAccessId = unifiId;
      lenelS2NetBoxId = lenelId;
    } else {
      // If it's a single ID, we need to determine which system it belongs to
      // For now, we'll try both systems
      unifiAccessId = userId;
      lenelS2NetBoxId = userId;
    }
    
    const results = {
      unifiAccess: null,
      lenelS2NetBox: null
    };
    
    // Update user status in Unifi Access if ID is available
    if (unifiAccessId) {
      try {
        const unifiReq = { 
          ...req,
          params: { id: unifiAccessId },
          body: { enabled }
        };
        
        const unifiRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Unifi Access controller to update user status
        results.unifiAccess = await unifiAccessController.updateUserStatus(unifiReq, unifiRes);
      } catch (error) {
        console.error('Error updating user status in Unifi Access:', error);
        results.unifiAccess = { error: error.message || 'Failed to update user status in Unifi Access' };
      }
    }
    
    // Update user status in Lenel S2 NetBox if ID is available
    if (lenelS2NetBoxId) {
      try {
        const lenelReq = { 
          ...req,
          params: { id: lenelS2NetBoxId },
          body: { enabled }
        };
        
        const lenelRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Lenel S2 NetBox controller to update user status
        results.lenelS2NetBox = await lenelS2NetBoxController.updateCardholderStatus(lenelReq, lenelRes);
      } catch (error) {
        console.error('Error updating user status in Lenel S2 NetBox:', error);
        results.lenelS2NetBox = { error: error.message || 'Failed to update user status in Lenel S2 NetBox' };
      }
    }
    
    // Check if both operations failed
    if (
      (unifiAccessId && results.unifiAccess?.error) &&
      (lenelS2NetBoxId && results.lenelS2NetBox?.error)
    ) {
      return res.status(500).json({
        error: 'Failed to update user status in all systems',
        details: results
      });
    }
    
    // Return results
    return res.json({
      message: `User ${enabled ? 'enabled' : 'disabled'} successfully in at least one system`,
      results
    });
  } catch (error) {
    console.error('Error updating user status:', error);
    return res.status(500).json({ error: 'Failed to update user status' });
  }
};

/**
 * Get all access levels from all access control systems
 */
exports.getAllAccessLevels = async (req, res) => {
  try {
    // Create mock request and response objects
    const unifiReq = { ...req };
    const unifiRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    const lenelReq = { ...req };
    const lenelRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    // Get access levels from both systems
    let unifiAccessLevels = [];
    let lenelAccessLevels = [];
    
    try {
      // Note: This is a placeholder. The actual method name may differ.
      unifiAccessLevels = await unifiAccessController.getAccessLevels(unifiReq, unifiRes) || [];
      // Add system identifier to each access level
      unifiAccessLevels = unifiAccessLevels.map(level => ({
        ...level,
        system: 'unifi-access',
        systemName: 'Unifi Access'
      }));
    } catch (error) {
      console.error('Error getting Unifi Access levels:', error);
    }
    
    try {
      lenelAccessLevels = await lenelS2NetBoxController.getAccessLevels(lenelReq, lenelRes) || [];
      // Add system identifier to each access level
      lenelAccessLevels = lenelAccessLevels.map(level => ({
        ...level,
        system: 'lenel-s2-netbox',
        systemName: 'Lenel S2 NetBox'
      }));
    } catch (error) {
      console.error('Error getting Lenel S2 NetBox access levels:', error);
    }
    
    // Combine and return all access levels
    const allAccessLevels = [...unifiAccessLevels, ...lenelAccessLevels];
    
    return res.json(allAccessLevels);
  } catch (error) {
    console.error('Error getting all access levels:', error);
    return res.status(500).json({ error: 'Failed to get all access levels' });
  }
};

/**
 * Get all schedules from all access control systems
 */
exports.getAllSchedules = async (req, res) => {
  try {
    // Create mock request and response objects
    const unifiReq = { ...req };
    const unifiRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    const lenelReq = { ...req };
    const lenelRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    // Get schedules from both systems
    let unifiSchedules = [];
    let lenelSchedules = [];
    
    try {
      // Note: This is a placeholder. The actual method name may differ.
      unifiSchedules = await unifiAccessController.getSchedules(unifiReq, unifiRes) || [];
      // Add system identifier to each schedule
      unifiSchedules = unifiSchedules.map(schedule => ({
        ...schedule,
        system: 'unifi-access',
        systemName: 'Unifi Access'
      }));
    } catch (error) {
      console.error('Error getting Unifi Access schedules:', error);
    }
    
    try {
      lenelSchedules = await lenelS2NetBoxController.getDoorSchedules(lenelReq, lenelRes) || [];
      // Add system identifier to each schedule
      lenelSchedules = lenelSchedules.map(schedule => ({
        ...schedule,
        system: 'lenel-s2-netbox',
        systemName: 'Lenel S2 NetBox'
      }));
    } catch (error) {
      console.error('Error getting Lenel S2 NetBox schedules:', error);
    }
    
    // Combine and return all schedules
    const allSchedules = [...unifiSchedules, ...lenelSchedules];
    
    return res.json(allSchedules);
  } catch (error) {
    console.error('Error getting all schedules:', error);
    return res.status(500).json({ error: 'Failed to get all schedules' });
  }
};

/**
 * Get all holidays from all access control systems
 */
exports.getAllHolidays = async (req, res) => {
  try {
    // Create mock request and response objects
    const unifiReq = { ...req };
    const unifiRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    const lenelReq = { ...req };
    const lenelRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    // Get holidays from both systems
    let unifiHolidays = [];
    let lenelHolidays = [];
    
    try {
      // Note: This is a placeholder. The actual method name may differ.
      unifiHolidays = await unifiAccessController.getHolidays(unifiReq, unifiRes) || [];
      // Add system identifier to each holiday
      unifiHolidays = unifiHolidays.map(holiday => ({
        ...holiday,
        system: 'unifi-access',
        systemName: 'Unifi Access'
      }));
    } catch (error) {
      console.error('Error getting Unifi Access holidays:', error);
    }
    
    try {
      // Note: This is a placeholder. The actual method name may differ.
      lenelHolidays = await lenelS2NetBoxController.getHolidays(lenelReq, lenelRes) || [];
      // Add system identifier to each holiday
      lenelHolidays = lenelHolidays.map(holiday => ({
        ...holiday,
        system: 'lenel-s2-netbox',
        systemName: 'Lenel S2 NetBox'
      }));
    } catch (error) {
      console.error('Error getting Lenel S2 NetBox holidays:', error);
    }
    
    // Combine and return all holidays
    const allHolidays = [...unifiHolidays, ...lenelHolidays];
    
    return res.json(allHolidays);
  } catch (error) {
    console.error('Error getting all holidays:', error);
    return res.status(500).json({ error: 'Failed to get all holidays' });
  }
};

/**
 * Create a new holiday in selected access control systems
 */
exports.createHoliday = async (req, res) => {
  try {
    const { 
      name, 
      date, 
      description, 
      systems 
    } = req.body;
    
    if (!name || !date) {
      return res.status(400).json({ error: 'Holiday name and date are required' });
    }
    
    if (!systems || !Array.isArray(systems) || systems.length === 0) {
      return res.status(400).json({ error: 'At least one system must be selected' });
    }
    
    const results = {
      unifiAccess: null,
      lenelS2NetBox: null
    };
    
    // Create holiday in Unifi Access if selected
    if (systems.includes('unifi-access')) {
      try {
        const unifiReq = { 
          ...req,
          body: {
            name,
            date,
            description
          }
        };
        
        const unifiRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Unifi Access controller to create holiday
        // Note: This is a placeholder. The actual method name may differ.
        results.unifiAccess = await unifiAccessController.createHoliday(unifiReq, unifiRes);
      } catch (error) {
        console.error('Error creating holiday in Unifi Access:', error);
        results.unifiAccess = { error: error.message || 'Failed to create holiday in Unifi Access' };
      }
    }
    
    // Create holiday in Lenel S2 NetBox if selected
    if (systems.includes('lenel-s2-netbox')) {
      try {
        const lenelReq = { 
          ...req,
          body: {
            name,
            date,
            description
          }
        };
        
        const lenelRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Lenel S2 NetBox controller to create holiday
        // Note: This is a placeholder. The actual method name may differ.
        results.lenelS2NetBox = await lenelS2NetBoxController.createHoliday(lenelReq, lenelRes);
      } catch (error) {
        console.error('Error creating holiday in Lenel S2 NetBox:', error);
        results.lenelS2NetBox = { error: error.message || 'Failed to create holiday in Lenel S2 NetBox' };
      }
    }
    
    // Check if both operations failed
    if (
      (systems.includes('unifi-access') && results.unifiAccess?.error) &&
      (systems.includes('lenel-s2-netbox') && results.lenelS2NetBox?.error)
    ) {
      return res.status(500).json({
        error: 'Failed to create holiday in all selected systems',
        details: results
      });
    }
    
    // Return results
    return res.status(201).json({
      message: 'Holiday created successfully in at least one system',
      results
    });
  } catch (error) {
    console.error('Error creating holiday:', error);
    return res.status(500).json({ error: 'Failed to create holiday' });
  }
};

/**
 * Create a new schedule in selected access control systems
 */
exports.createSchedule = async (req, res) => {
  try {
    const { 
      name, 
      description, 
      timeWindows,
      doors,
      systems,
      timeZone,
      handleDaylightSaving
    } = req.body;
    
    if (!name || !timeWindows || !Array.isArray(timeWindows) || timeWindows.length === 0) {
      return res.status(400).json({ error: 'Schedule name and at least one time window are required' });
    }
    
    if (!systems || !Array.isArray(systems) || systems.length === 0) {
      return res.status(400).json({ error: 'At least one system must be selected' });
    }
    
    if (!timeZone) {
      return res.status(400).json({ error: 'Time zone is required' });
    }
    
    // Ensure handleDaylightSaving is a boolean
    const handleDst = handleDaylightSaving === undefined ? true : Boolean(handleDaylightSaving);
    
    const results = {
      unifiAccess: null,
      lenelS2NetBox: null
    };
    
    // Create schedule in Unifi Access if selected
    if (systems.includes('unifi-access')) {
      try {
        const unifiReq = { 
          ...req,
          body: {
            name,
            description,
            timeWindows,
            doors: doors?.filter(door => door.system === 'unifi-access').map(door => door.id) || [],
            timeZone,
            handleDaylightSaving: handleDst
          }
        };
        
        const unifiRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Unifi Access controller to create schedule
        // Note: This is a placeholder. The actual method name may differ.
        results.unifiAccess = await unifiAccessController.createSchedule(unifiReq, unifiRes);
      } catch (error) {
        console.error('Error creating schedule in Unifi Access:', error);
        results.unifiAccess = { error: error.message || 'Failed to create schedule in Unifi Access' };
      }
    }
    
    // Create schedule in Lenel S2 NetBox if selected
    if (systems.includes('lenel-s2-netbox')) {
      try {
        const lenelReq = { 
          ...req,
          body: {
            name,
            description,
            timeWindows,
            doors: doors?.filter(door => door.system === 'lenel-s2-netbox').map(door => door.id) || [],
            timeZone,
            handleDaylightSaving: handleDst
          }
        };
        
        const lenelRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call Lenel S2 NetBox controller to create schedule
        // Note: This is a placeholder. The actual method name may differ.
        results.lenelS2NetBox = await lenelS2NetBoxController.createDoorSchedule(lenelReq, lenelRes);
      } catch (error) {
        console.error('Error creating schedule in Lenel S2 NetBox:', error);
        results.lenelS2NetBox = { error: error.message || 'Failed to create schedule in Lenel S2 NetBox' };
      }
    }
    
    // Check if both operations failed
    if (
      (systems.includes('unifi-access') && results.unifiAccess?.error) &&
      (systems.includes('lenel-s2-netbox') && results.lenelS2NetBox?.error)
    ) {
      return res.status(500).json({
        error: 'Failed to create schedule in all selected systems',
        details: results
      });
    }
    
    // Return results
    return res.status(201).json({
      message: 'Schedule created successfully in at least one system',
      results
    });
  } catch (error) {
    console.error('Error creating schedule:', error);
    return res.status(500).json({ error: 'Failed to create schedule' });
  }
};

/**
 * Bulk update access levels for multiple users
 */
exports.bulkUpdateAccessLevels = async (req, res) => {
  try {
    const { 
      userIds, 
      accessLevels, 
      updateMode 
    } = req.body;
    
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ error: 'At least one user ID must be provided' });
    }
    
    if (!accessLevels || (!accessLevels.unifiAccess && !accessLevels.lenelS2NetBox)) {
      return res.status(400).json({ error: 'At least one access level must be provided' });
    }
    
    if (!updateMode || !['add', 'remove', 'replace'].includes(updateMode)) {
      return res.status(400).json({ error: 'Valid update mode (add, remove, or replace) must be provided' });
    }
    
    // Track results for each user and system
    const results = {
      unifiAccess: {},
      lenelS2NetBox: {}
    };
    
    // Process Unifi Access users if access levels are provided
    if (accessLevels.unifiAccess && accessLevels.unifiAccess.length > 0) {
      // Get Unifi Access user IDs from the provided user IDs
      // For combined users (users in both systems), extract the Unifi Access ID
      const unifiUserIds = userIds.map(userId => {
        if (userId.includes('|')) {
          return userId.split('|')[0];
        }
        return userId;
      }).filter(id => id);
      
      if (unifiUserIds.length > 0) {
        try {
          const unifiReq = { 
            ...req,
            body: {
              userIds: unifiUserIds,
              accessLevels: accessLevels.unifiAccess,
              updateMode
            }
          };
          
          const unifiRes = {
            json: (data) => data,
            status: (code) => ({ json: (data) => ({ code, data }) })
          };
          
          // Call Unifi Access controller to update access levels
          // Note: This is a placeholder. The actual method name may differ.
          const unifiResult = await unifiAccessController.updateUserAccessLevels(unifiReq, unifiRes);
          
          // Store results for each user
          unifiUserIds.forEach(userId => {
            results.unifiAccess[userId] = unifiResult;
          });
        } catch (error) {
          console.error('Error updating access levels in Unifi Access:', error);
          unifiUserIds.forEach(userId => {
            results.unifiAccess[userId] = { error: error.message || 'Failed to update access levels in Unifi Access' };
          });
        }
      }
    }
    
    // Process Lenel S2 NetBox users if access levels are provided
    if (accessLevels.lenelS2NetBox && accessLevels.lenelS2NetBox.length > 0) {
      // Get Lenel S2 NetBox user IDs from the provided user IDs
      // For combined users (users in both systems), extract the Lenel S2 NetBox ID
      const lenelUserIds = userIds.map(userId => {
        if (userId.includes('|')) {
          return userId.split('|')[1];
        }
        return userId;
      }).filter(id => id);
      
      if (lenelUserIds.length > 0) {
        try {
          const lenelReq = { 
            ...req,
            body: {
              userIds: lenelUserIds,
              accessLevels: accessLevels.lenelS2NetBox,
              updateMode
            }
          };
          
          const lenelRes = {
            json: (data) => data,
            status: (code) => ({ json: (data) => ({ code, data }) })
          };
          
          // Call Lenel S2 NetBox controller to update access levels
          // Note: This is a placeholder. The actual method name may differ.
          const lenelResult = await lenelS2NetBoxController.updateCardholderAccessLevels(lenelReq, lenelRes);
          
          // Store results for each user
          lenelUserIds.forEach(userId => {
            results.lenelS2NetBox[userId] = lenelResult;
          });
        } catch (error) {
          console.error('Error updating access levels in Lenel S2 NetBox:', error);
          lenelUserIds.forEach(userId => {
            results.lenelS2NetBox[userId] = { error: error.message || 'Failed to update access levels in Lenel S2 NetBox' };
          });
        }
      }
    }
    
    // Check if all operations failed
    const allFailed = userIds.every(userId => {
      const unifiId = userId.includes('|') ? userId.split('|')[0] : userId;
      const lenelId = userId.includes('|') ? userId.split('|')[1] : userId;
      
      const unifiResult = unifiId && results.unifiAccess[unifiId];
      const lenelResult = lenelId && results.lenelS2NetBox[lenelId];
      
      return (unifiResult && unifiResult.error) || (lenelResult && lenelResult.error);
    });
    
    if (allFailed) {
      return res.status(500).json({
        error: 'Failed to update access levels for all users',
        details: results
      });
    }
    
    // Return results
    return res.json({
      message: `Successfully updated access levels for ${userIds.length} user(s)`,
      results
    });
  } catch (error) {
    console.error('Error bulk updating access levels:', error);
    return res.status(500).json({ error: 'Failed to bulk update access levels' });
  }
};

/**
 * Control a door/portal in the specified access control system
 */
exports.controlDoor = async (req, res) => {
  try {
    const { doorId, system, action } = req.body;
    
    if (!doorId || !system || !action) {
      return res.status(400).json({ error: 'Door ID, system, and action are required' });
    }
    
    if (!['unlock', 'lock', 'momentary-unlock'].includes(action)) {
      return res.status(400).json({ error: 'Invalid action. Must be one of: unlock, lock, momentary-unlock' });
    }
    
    // Control door in the specified system
    if (system === 'unifi-access') {
      try {
        const unifiReq = { 
          ...req,
          params: { id: doorId },
          body: { action }
        };
        
        const unifiRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call appropriate Unifi Access controller method based on action
        let result;
        if (action === 'unlock') {
          result = await unifiAccessController.unlockDoor(unifiReq, unifiRes);
        } else if (action === 'lock') {
          // Note: The lockDoor function does not exist in the UniFi Access API
          // Return an error message directly instead of calling the controller
          return res.status(400).json({ 
            message: 'The lock door functionality is not supported by the UniFi Access API',
            error: 'UNSUPPORTED_OPERATION'
          });
        } else if (action === 'momentary-unlock') {
          // Note: This is a placeholder. The actual method name may differ.
          result = await unifiAccessController.momentaryUnlockDoor(unifiReq, unifiRes);
        }
        
        return res.json(result);
      } catch (error) {
        console.error(`Error controlling Unifi Access door ${doorId}:`, error);
        return res.status(500).json({ error: `Failed to control Unifi Access door: ${error.message}` });
      }
    } else if (system === 'lenel-s2-netbox') {
      try {
        const lenelReq = { 
          ...req,
          params: { id: doorId },
          body: { 
            action: action === 'momentary-unlock' ? 'momentary' : action 
          }
        };
        
        const lenelRes = {
          json: (data) => data,
          status: (code) => ({ json: (data) => ({ code, data }) })
        };
        
        // Call appropriate Lenel S2 NetBox controller method based on action
        let result;
        if (action === 'unlock') {
          result = await lenelS2NetBoxController.unlockDoor(lenelReq, lenelRes);
        } else if (action === 'lock') {
          result = await lenelS2NetBoxController.lockDoor(lenelReq, lenelRes);
        } else if (action === 'momentary-unlock') {
          // Note: This is a placeholder. The actual method name may differ.
          result = await lenelS2NetBoxController.momentaryUnlockDoor(lenelReq, lenelRes);
        }
        
        return res.json(result);
      } catch (error) {
        console.error(`Error controlling Lenel S2 NetBox door ${doorId}:`, error);
        return res.status(500).json({ error: `Failed to control Lenel S2 NetBox door: ${error.message}` });
      }
    } else {
      return res.status(400).json({ error: 'Invalid system. Must be one of: unifi-access, lenel-s2-netbox' });
    }
  } catch (error) {
    console.error('Error controlling door:', error);
    return res.status(500).json({ error: 'Failed to control door' });
  }
};

/**
 * Get all elevators from all access control systems
 */
exports.getAllElevators = async (req, res) => {
  try {
    // Create mock request and response objects
    const unifiReq = { ...req };
    const unifiRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    const lenelReq = { ...req };
    const lenelRes = {
      json: (data) => data,
      status: (code) => ({ json: (data) => ({ code, data }) })
    };
    
    // Get elevators from both systems
    let unifiElevators = [];
    let lenelElevators = [];
    
    // Currently, only Lenel S2 NetBox supports elevators
    try {
      lenelElevators = await lenelS2NetBoxController.getElevators(lenelReq, lenelRes) || [];
      // Add system identifier to each elevator
      lenelElevators = lenelElevators.map(elevator => ({
        ...elevator,
        system: 'lenel-s2-netbox',
        systemName: 'Lenel S2 NetBox'
      }));
    } catch (error) {
      console.error('Error getting Lenel S2 NetBox elevators:', error);
    }
    
    // Combine and return all elevators
    const allElevators = [...unifiElevators, ...lenelElevators];
    
    return res.json(allElevators);
  } catch (error) {
    console.error('Error getting all elevators:', error);
    return res.status(500).json({ error: 'Failed to get all elevators' });
  }
};