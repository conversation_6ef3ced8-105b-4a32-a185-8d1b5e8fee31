const NewsPost = require('../../models/NewsPost');
const NewsCategory = require('../../models/NewsCategory');
const { validationResult } = require('express-validator');

/**
 * News Post Controller
 * Handles API operations for news posts
 */
const newsPostController = {
  /**
   * Get all news posts
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with posts
   */
  getAllPosts: async (req, res) => {
    try {
      // Get query parameters
      const { limit = 10, page = 1, category, featured, published = true } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      // Build query
      const query = {};
      if (category) query.category = category;
      if (featured !== undefined) query.featured = featured === 'true';
      if (published !== undefined) query.published = published === 'true';
      
      // If user is not admin, only show posts from accessible categories
      if (!req.user.roles.includes('admin')) {
        // Get categories accessible by user's roles
        const accessibleCategories = await NewsCategory.find({
          $or: [
            { accessRoles: { $in: req.user.roles } },
            { accessRoles: { $size: 0 } } // Categories with empty accessRoles are accessible to all
          ]
        }).select('_id');
        
        const categoryIds = accessibleCategories.map(cat => cat._id);
        query.category = { $in: categoryIds };
      }
      
      // Get total count for pagination
      const total = await NewsPost.countDocuments(query);
      
      // Get posts
      const posts = await NewsPost.find(query)
        .sort({ featured: -1, publishedAt: -1, createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate('category', 'name')
        .populate('author', 'name avatar');
      
      res.json({
        posts,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (err) {
      console.error('Error fetching news posts:', err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Get latest news posts for dashboard widget
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with latest posts
   */
  getLatestPosts: async (req, res) => {
    try {
      // Get query parameters
      const { limit = 5 } = req.query;
      
      // Build query
      const query = { published: true };
      
      // If user is not admin, only show posts from accessible categories
      if (!req.user.roles.includes('admin')) {
        // Get categories accessible by user's roles
        const accessibleCategories = await NewsCategory.find({
          $or: [
            { accessRoles: { $in: req.user.roles } },
            { accessRoles: { $size: 0 } } // Categories with empty accessRoles are accessible to all
          ]
        }).select('_id');
        
        const categoryIds = accessibleCategories.map(cat => cat._id);
        query.category = { $in: categoryIds };
      }
      
      // Get latest posts
      const posts = await NewsPost.find(query)
        .sort({ featured: -1, publishedAt: -1 })
        .limit(parseInt(limit))
        .populate('category', 'name')
        .populate('author', 'name avatar');
      
      res.json(posts);
    } catch (err) {
      console.error('Error fetching latest news posts:', err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Get post by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with post
   */
  getPostById: async (req, res) => {
    try {
      const post = await NewsPost.findById(req.params.id)
        .populate('category', 'name')
        .populate('author', 'name avatar');
      
      if (!post) {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      // Check if user has access to this post's category
      if (!req.user.roles.includes('admin')) {
        const category = await NewsCategory.findById(post.category._id);
        
        // Check if user has access to this category
        const hasAccess = category.accessRoles.length === 0 || 
          category.accessRoles.some(role => req.user.roles.includes(role));
        
        if (!hasAccess) {
          return res.status(403).json({ msg: 'Not authorized to view this post' });
        }
      }
      
      // Increment view count
      post.viewCount += 1;
      await post.save();
      
      res.json(post);
    } catch (err) {
      console.error('Error fetching news post:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Create a new news post
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with created post
   */
  createPost: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        title, 
        content, 
        summary,
        category,
        published,
        featured,
        tags
      } = req.body;

      // Check if category exists
      const categoryExists = await NewsCategory.findById(category);
      if (!categoryExists) {
        return res.status(400).json({ msg: 'Invalid category' });
      }
      
      // Check if user has write access to this category
      if (!req.user.roles.includes('admin')) {
        const hasAccess = categoryExists.accessRoles.length === 0 || 
          categoryExists.accessRoles.some(role => req.user.roles.includes(role));
        
        if (!hasAccess) {
          return res.status(403).json({ msg: 'Not authorized to create posts in this category' });
        }
      }

      // Create new post
      const newPost = new NewsPost({
        title,
        content,
        summary: summary || title.substring(0, 100),
        category,
        author: req.user.id,
        published: published || false,
        featured: featured || false,
        tags: tags || []
      });

      const post = await newPost.save();
      
      // Populate category and author
      await post.populate('category', 'name');
      await post.populate('author', 'name avatar');
      
      res.json(post);
    } catch (err) {
      console.error('Error creating news post:', err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Update a news post
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated post
   */
  updatePost: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        title, 
        content, 
        summary,
        category,
        published,
        featured,
        tags
      } = req.body;

      // Find post by ID
      let post = await NewsPost.findById(req.params.id);
      
      if (!post) {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      // Check if user is authorized to update this post
      if (!req.user.roles.includes('admin') && post.author.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to update this post' });
      }
      
      // If category is being changed, check if it exists and user has access
      if (category && category !== post.category.toString()) {
        const categoryExists = await NewsCategory.findById(category);
        if (!categoryExists) {
          return res.status(400).json({ msg: 'Invalid category' });
        }
        
        // Check if user has write access to this category
        if (!req.user.roles.includes('admin')) {
          const hasAccess = categoryExists.accessRoles.length === 0 || 
            categoryExists.accessRoles.some(role => req.user.roles.includes(role));
          
          if (!hasAccess) {
            return res.status(403).json({ msg: 'Not authorized to move posts to this category' });
          }
        }
      }
      
      // Update post fields
      if (title) post.title = title;
      if (content) post.content = content;
      if (summary) post.summary = summary;
      if (category) post.category = category;
      if (published !== undefined) {
        post.published = published;
        if (published && !post.publishedAt) {
          post.publishedAt = Date.now();
        }
      }
      if (featured !== undefined) post.featured = featured;
      if (tags) post.tags = tags;
      
      await post.save();
      
      // Populate category and author
      await post.populate('category', 'name');
      await post.populate('author', 'name avatar');
      
      res.json(post);
    } catch (err) {
      console.error('Error updating news post:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Delete a news post
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response
   */
  deletePost: async (req, res) => {
    try {
      // Find post by ID
      const post = await NewsPost.findById(req.params.id);
      
      if (!post) {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      // Check if user is authorized to delete this post
      if (!req.user.roles.includes('admin') && post.author.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to delete this post' });
      }
      
      await post.remove();
      res.json({ msg: 'News post removed' });
    } catch (err) {
      console.error('Error deleting news post:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Toggle post featured status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated post
   */
  toggleFeatured: async (req, res) => {
    try {
      // Find post by ID
      const post = await NewsPost.findById(req.params.id);
      
      if (!post) {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      // Check if user is authorized to update this post
      if (!req.user.roles.includes('admin') && post.author.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to update this post' });
      }
      
      // Toggle featured status
      post.featured = !post.featured;
      
      await post.save();
      
      // Populate category and author
      await post.populate('category', 'name');
      await post.populate('author', 'name avatar');
      
      res.json(post);
    } catch (err) {
      console.error('Error toggling news post featured status:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Toggle post published status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated post
   */
  togglePublished: async (req, res) => {
    try {
      // Find post by ID
      const post = await NewsPost.findById(req.params.id);
      
      if (!post) {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      // Check if user is authorized to update this post
      if (!req.user.roles.includes('admin') && post.author.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to update this post' });
      }
      
      // Toggle published status
      post.published = !post.published;
      
      // Set publishedAt date if publishing for the first time
      if (post.published && !post.publishedAt) {
        post.publishedAt = Date.now();
      }
      
      await post.save();
      
      // Populate category and author
      await post.populate('category', 'name');
      await post.populate('author', 'name avatar');
      
      res.json(post);
    } catch (err) {
      console.error('Error toggling news post published status:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
};

module.exports = newsPostController;