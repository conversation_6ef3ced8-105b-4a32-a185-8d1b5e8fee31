const nodemailer = require('nodemailer');
const User = require('../../models/User');
const Ticket = require('../../models/Ticket');
const TicketComment = require('../../models/TicketComment');

/**
 * Ticket Notification Service
 * Handles email notifications for ticket events
 */
class TicketNotificationService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  /**
   * Initialize email transporter
   */
  initializeTransporter() {
    if (process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS) {
      this.transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: process.env.SMTP_PORT || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      });
    } else {
      console.log('SMTP configuration not found, email notifications disabled');
    }
  }

  /**
   * Send notification when ticket is created
   * @param {Object} ticket - Ticket object
   * @param {Object} creator - User who created the ticket
   */
  async notifyTicketCreated(ticket, creator) {
    if (!this.transporter) return;

    try {
      // Get standard recipients
      const recipients = await this.getTicketNotificationRecipients(ticket, creator._id);
      
      // Add requesterEmail if it exists and is not already included
      if (ticket.requesterEmail) {
        const requesterEmailRecipient = {
          email: ticket.requesterEmail,
          name: 'Requester',
          isExternal: true
        };
        
        // Check if this email is already in recipients
        const emailExists = recipients.some(r => r.email === ticket.requesterEmail);
        
        if (!emailExists) {
          recipients.push(requesterEmailRecipient);
        }
      }
      
      if (recipients.length === 0) return;

      const subject = `New Ticket Created: ${ticket.ticketNumber} - ${ticket.subject}`;
      const content = await this.generateTicketCreatedEmail(ticket, creator);

      await this.sendNotificationEmails(recipients, subject, content, ticket);
      
      console.log(`Ticket creation notification sent for ${ticket.ticketNumber} to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending ticket creation notification:', error);
    }
  }

  /**
   * Send notification when ticket is updated
   * @param {Object} ticket - Updated ticket object
   * @param {Object} updater - User who updated the ticket
   * @param {Array} changes - Array of changes made
   */
  async notifyTicketUpdated(ticket, updater, changes) {
    if (!this.transporter || !changes || changes.length === 0) return;

    try {
      const recipients = await this.getTicketNotificationRecipients(ticket, updater._id);
      
      if (recipients.length === 0) return;

      const subject = `Ticket Updated: ${ticket.ticketNumber} - ${ticket.subject}`;
      const content = await this.generateTicketUpdatedEmail(ticket, updater, changes);

      await this.sendNotificationEmails(recipients, subject, content, ticket);
      
      console.log(`Ticket update notification sent for ${ticket.ticketNumber} to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending ticket update notification:', error);
    }
  }

  /**
   * Send notification when ticket is assigned
   * @param {Object} ticket - Ticket object
   * @param {Object} assignee - User assigned to the ticket
   * @param {Object} assigner - User who made the assignment
   */
  async notifyTicketAssigned(ticket, assignee, assigner) {
    if (!this.transporter) return;

    try {
      // Always notify the assignee
      const recipients = [assignee];
      
      // Also notify requester if different from assigner
      if (String(ticket.requester) !== String(assigner._id)) {
        const requester = await User.findById(ticket.requester);
        if (requester) recipients.push(requester);
      }

      // Notify followers
      if (ticket.followers && ticket.followers.length > 0) {
        const followers = await User.find({ 
          _id: { $in: ticket.followers },
          _id: { $ne: assigner._id }
        });
        recipients.push(...followers);
      }

      const subject = `Ticket Assigned: ${ticket.ticketNumber} - ${ticket.subject}`;
      const content = await this.generateTicketAssignedEmail(ticket, assignee, assigner);

      await this.sendNotificationEmails(recipients, subject, content, ticket);
      
      console.log(`Ticket assignment notification sent for ${ticket.ticketNumber} to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending ticket assignment notification:', error);
    }
  }

  /**
   * Send notification when comment is added
   * @param {Object} ticket - Ticket object
   * @param {Object} comment - Comment object
   * @param {Object} author - Comment author
   */
  async notifyCommentAdded(ticket, comment, author) {
    if (!this.transporter || comment.type === 'internal_note' || comment.type === 'system') return;

    try {
      const recipients = await this.getTicketNotificationRecipients(ticket, author._id);
      
      if (recipients.length === 0) return;

      const subject = `New Comment: ${ticket.ticketNumber} - ${ticket.subject}`;
      const content = await this.generateCommentAddedEmail(ticket, comment, author);

      await this.sendNotificationEmails(recipients, subject, content, ticket);
      
      console.log(`Comment notification sent for ${ticket.ticketNumber} to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending comment notification:', error);
    }
  }

  /**
   * Send notification when ticket status changes
   * @param {Object} ticket - Ticket object
   * @param {string} oldStatus - Previous status
   * @param {string} newStatus - New status
   * @param {Object} updater - User who changed the status
   */
  async notifyStatusChanged(ticket, oldStatus, newStatus, updater) {
    if (!this.transporter) return;

    try {
      const recipients = await this.getTicketNotificationRecipients(ticket, updater._id);
      
      if (recipients.length === 0) return;

      const subject = `Ticket Status Changed: ${ticket.ticketNumber} - ${newStatus.toUpperCase()}`;
      const content = await this.generateStatusChangedEmail(ticket, oldStatus, newStatus, updater);

      await this.sendNotificationEmails(recipients, subject, content, ticket);
      
      console.log(`Status change notification sent for ${ticket.ticketNumber} to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending status change notification:', error);
    }
  }

  /**
   * Get recipients for ticket notifications
   * @param {Object} ticket - Ticket object
   * @param {string} excludeUserId - User ID to exclude from notifications
   * @returns {Array} Array of user objects
   */
  async getTicketNotificationRecipients(ticket, excludeUserId) {
    const recipients = new Set();
    
    try {
      // Check for requesterEmail first (for non-registered users)
      if (ticket.requesterEmail) {
        // Add external requester with email
        recipients.add({
          email: ticket.requesterEmail,
          name: 'Requester',
          isExternal: true
        });
      } 
      // Then check for registered user requester (unless they're the one making the change)
      else if (ticket.requester && String(ticket.requester) !== String(excludeUserId)) {
        const requester = await User.findById(ticket.requester);
        if (requester && requester.email && requester.isActive) {
          recipients.add(requester);
        }
      }
      
      // Include assignee (unless they're the one making the change)
      if (ticket.assignedTo && String(ticket.assignedTo) !== String(excludeUserId)) {
        const assignee = await User.findById(ticket.assignedTo);
        if (assignee && assignee.email && assignee.isActive) {
          recipients.add(assignee);
        }
      }
      
      // Include followers (excluding the one making the change)
      if (ticket.followers && ticket.followers.length > 0) {
        const followers = await User.find({ 
          _id: { $in: ticket.followers },
          _id: { $ne: excludeUserId },
          isActive: true
        });
        followers.forEach(follower => {
          if (follower.email) {
            recipients.add(follower);
          }
        });
      }
      
      return Array.from(recipients);
    } catch (error) {
      console.error('Error getting notification recipients:', error);
      return [];
    }
  }

  /**
   * Send notification emails
   * @param {Array} recipients - Array of user objects
   * @param {string} subject - Email subject
   * @param {string} content - Email content (HTML)
   * @param {Object} ticket - Ticket object
   */
  async sendNotificationEmails(recipients, subject, content, ticket) {
    if (!this.transporter || recipients.length === 0) return;

    const emailPromises = recipients.map(recipient => {
      return this.transporter.sendMail({
        from: process.env.TICKET_EMAIL_FROM || process.env.SMTP_USER,
        to: recipient.email,
        subject: subject,
        html: content,
        headers: {
          'References': ticket.emailThreadId || ticket._id,
          'X-Ticket-ID': ticket.ticketNumber
        }
      });
    });

    try {
      await Promise.allSettled(emailPromises);
    } catch (error) {
      console.error('Error sending notification emails:', error);
    }
  }

  /**
   * Generate email content for ticket creation
   * @param {Object} ticket - Ticket object
   * @param {Object} creator - Creator user object
   * @returns {string} HTML email content
   */
  async generateTicketCreatedEmail(ticket, creator) {
    const ticketUrl = `${process.env.PORTAL_URL || 'http://localhost:3000'}/tickets/${ticket._id}`;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #1976d2; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .ticket-info { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .button { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>New Support Ticket Created</h1>
          </div>
          <div class="content">
            <p>A new support ticket has been created in the CSF Portal.</p>
            
            <div class="ticket-info">
              <h3>Ticket Details</h3>
              <p><strong>Ticket Number:</strong> ${ticket.ticketNumber}</p>
              <p><strong>Subject:</strong> ${ticket.subject}</p>
              <p><strong>Priority:</strong> ${ticket.priority.toUpperCase()}</p>
              <p><strong>Type:</strong> ${ticket.type.charAt(0).toUpperCase() + ticket.type.slice(1)}</p>
              <p><strong>Status:</strong> ${ticket.status.replace('_', ' ').toUpperCase()}</p>
              <p><strong>Created by:</strong> ${creator.name} (${creator.email})</p>
              <p><strong>Created:</strong> ${ticket.createdAt.toLocaleString()}</p>
              ${ticket.category ? `<p><strong>Category:</strong> ${ticket.category}</p>` : ''}
              ${ticket.dueDate ? `<p><strong>Due Date:</strong> ${new Date(ticket.dueDate).toLocaleDateString()}</p>` : ''}
            </div>
            
            <div class="ticket-info">
              <h3>Description</h3>
              <p>${ticket.description.replace(/\n/g, '<br>')}</p>
            </div>
            
            <p style="text-align: center;">
              <a href="${ticketUrl}" class="button">View Ticket</a>
            </p>
          </div>
          <div class="footer">
            <p>CSF Portal - Ticket Management System</p>
            <p>Please do not reply to this email. Use the portal to add comments to the ticket.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate email content for ticket updates
   * @param {Object} ticket - Ticket object
   * @param {Object} updater - Updater user object
   * @param {Array} changes - Array of changes
   * @returns {string} HTML email content
   */
  async generateTicketUpdatedEmail(ticket, updater, changes) {
    const ticketUrl = `${process.env.PORTAL_URL || 'http://localhost:3000'}/tickets/${ticket._id}`;
    
    const changesList = changes.map(change => 
      `<li><strong>${change.field}:</strong> ${change.from} → ${change.to}</li>`
    ).join('');
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #ff9800; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .ticket-info { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .changes { background-color: #fff3cd; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #ffc107; }
          .button { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Ticket Updated</h1>
          </div>
          <div class="content">
            <p>Ticket <strong>${ticket.ticketNumber}</strong> has been updated by ${updater.name}.</p>
            
            <div class="changes">
              <h3>Changes Made</h3>
              <ul>${changesList}</ul>
              <p><small>Updated by: ${updater.name} on ${new Date().toLocaleString()}</small></p>
            </div>
            
            <div class="ticket-info">
              <h3>Current Ticket Details</h3>
              <p><strong>Subject:</strong> ${ticket.subject}</p>
              <p><strong>Priority:</strong> ${ticket.priority.toUpperCase()}</p>
              <p><strong>Status:</strong> ${ticket.status.replace('_', ' ').toUpperCase()}</p>
              ${ticket.assignedTo ? `<p><strong>Assigned to:</strong> Loading...</p>` : ''}
              ${ticket.dueDate ? `<p><strong>Due Date:</strong> ${new Date(ticket.dueDate).toLocaleDateString()}</p>` : ''}
            </div>
            
            <p style="text-align: center;">
              <a href="${ticketUrl}" class="button">View Ticket</a>
            </p>
          </div>
          <div class="footer">
            <p>CSF Portal - Ticket Management System</p>
            <p>Please do not reply to this email. Use the portal to add comments to the ticket.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate email content for ticket assignment
   * @param {Object} ticket - Ticket object
   * @param {Object} assignee - Assignee user object
   * @param {Object} assigner - Assigner user object
   * @returns {string} HTML email content
   */
  async generateTicketAssignedEmail(ticket, assignee, assigner) {
    const ticketUrl = `${process.env.PORTAL_URL || 'http://localhost:3000'}/tickets/${ticket._id}`;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #4caf50; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .ticket-info { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .assignment { background-color: #e8f5e8; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #4caf50; }
          .button { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Ticket Assigned</h1>
          </div>
          <div class="content">
            <div class="assignment">
              <h3>Assignment Details</h3>
              <p>Ticket <strong>${ticket.ticketNumber}</strong> has been assigned to <strong>${assignee.name}</strong> by ${assigner.name}.</p>
              <p><small>Assigned on: ${new Date().toLocaleString()}</small></p>
            </div>
            
            <div class="ticket-info">
              <h3>Ticket Details</h3>
              <p><strong>Subject:</strong> ${ticket.subject}</p>
              <p><strong>Priority:</strong> ${ticket.priority.toUpperCase()}</p>
              <p><strong>Status:</strong> ${ticket.status.replace('_', ' ').toUpperCase()}</p>
              ${ticket.dueDate ? `<p><strong>Due Date:</strong> ${new Date(ticket.dueDate).toLocaleDateString()}</p>` : ''}
              ${ticket.category ? `<p><strong>Category:</strong> ${ticket.category}</p>` : ''}
            </div>
            
            <p style="text-align: center;">
              <a href="${ticketUrl}" class="button">View Ticket</a>
            </p>
          </div>
          <div class="footer">
            <p>CSF Portal - Ticket Management System</p>
            <p>Please do not reply to this email. Use the portal to add comments to the ticket.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate email content for new comments
   * @param {Object} ticket - Ticket object
   * @param {Object} comment - Comment object
   * @param {Object} author - Comment author object
   * @returns {string} HTML email content
   */
  async generateCommentAddedEmail(ticket, comment, author) {
    const ticketUrl = `${process.env.PORTAL_URL || 'http://localhost:3000'}/tickets/${ticket._id}`;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #2196f3; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .ticket-info { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .comment { background-color: #e3f2fd; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #2196f3; }
          .button { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>New Comment Added</h1>
          </div>
          <div class="content">
            <p>A new comment has been added to ticket <strong>${ticket.ticketNumber}</strong>.</p>
            
            <div class="comment">
              <h3>Comment from ${author.name}</h3>
              <p><small>${comment.createdAt.toLocaleString()}</small></p>
              <div style="margin-top: 10px;">
                ${comment.contentType === 'html' ? comment.content : comment.content.replace(/\n/g, '<br>')}
              </div>
            </div>
            
            <div class="ticket-info">
              <h3>Ticket Details</h3>
              <p><strong>Subject:</strong> ${ticket.subject}</p>
              <p><strong>Priority:</strong> ${ticket.priority.toUpperCase()}</p>
              <p><strong>Status:</strong> ${ticket.status.replace('_', ' ').toUpperCase()}</p>
            </div>
            
            <p style="text-align: center;">
              <a href="${ticketUrl}" class="button">View Ticket</a>
            </p>
          </div>
          <div class="footer">
            <p>CSF Portal - Ticket Management System</p>
            <p>Please do not reply to this email. Use the portal to add comments to the ticket.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate email content for status changes
   * @param {Object} ticket - Ticket object
   * @param {string} oldStatus - Old status
   * @param {string} newStatus - New status
   * @param {Object} updater - User who changed the status
   * @returns {string} HTML email content
   */
  async generateStatusChangedEmail(ticket, oldStatus, newStatus, updater) {
    const ticketUrl = `${process.env.PORTAL_URL || 'http://localhost:3000'}/tickets/${ticket._id}`;
    
    const statusColors = {
      open: '#4caf50',
      pending: '#ff9800',
      on_hold: '#ff5722',
      resolved: '#2196f3',
      closed: '#607d8b'
    };
    
    const statusColor = statusColors[newStatus] || '#1976d2';
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: ${statusColor}; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .ticket-info { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .status-change { background-color: #f0f0f0; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid ${statusColor}; }
          .button { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Ticket Status Changed</h1>
          </div>
          <div class="content">
            <div class="status-change">
              <h3>Status Update</h3>
              <p>Ticket <strong>${ticket.ticketNumber}</strong> status has been changed from <strong>${oldStatus.replace('_', ' ').toUpperCase()}</strong> to <strong>${newStatus.replace('_', ' ').toUpperCase()}</strong>.</p>
              <p><small>Changed by: ${updater.name} on ${new Date().toLocaleString()}</small></p>
            </div>
            
            <div class="ticket-info">
              <h3>Ticket Details</h3>
              <p><strong>Subject:</strong> ${ticket.subject}</p>
              <p><strong>Priority:</strong> ${ticket.priority.toUpperCase()}</p>
              <p><strong>Current Status:</strong> ${newStatus.replace('_', ' ').toUpperCase()}</p>
              ${ticket.resolvedAt && newStatus === 'resolved' ? `<p><strong>Resolved:</strong> ${ticket.resolvedAt.toLocaleString()}</p>` : ''}
              ${ticket.closedAt && newStatus === 'closed' ? `<p><strong>Closed:</strong> ${ticket.closedAt.toLocaleString()}</p>` : ''}
            </div>
            
            <p style="text-align: center;">
              <a href="${ticketUrl}" class="button">View Ticket</a>
            </p>
          </div>
          <div class="footer">
            <p>CSF Portal - Ticket Management System</p>
            <p>Please do not reply to this email. Use the portal to add comments to the ticket.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

module.exports = new TicketNotificationService();